#!/bin/bash
set -e

#Go to root directory
BASEDIR=$(dirname "$0")

#Account variables
AWS_REGION="ap-south-1"
REGISTRY="************.dkr.ecr.ap-south-1.amazonaws.com"

#App specific variables
#use "crm1-frontend only for ganga-dev1"
ecr_repo_name="stocks-mini"

cd $BASEDIR/../
git_commit=$(git rev-parse --short HEAD)
git_branch=${BRANCH_NAME}
epoch=$(date +%s)

#For building docker images locally
local=0
if [ "$git_branch" = "" ];then
   echo "Branch name not found in env variables"
   git_branch="$(git rev-parse --abbrev-ref HEAD)"
   local=1
fi
app_name="stocks-mini"
if [ "$git_branch" = "staging" ];then
    env_name="staging"
    bucket_name="${app_name}-${env_name}"
elif [ "$git_branch" = "beta" ];then
    env_name="beta"
    bucket_name="${app_name}-${env_name}"
elif [ "$git_branch" = "release" ];then
    env_name="prod"
    bucket_name="${app_name}-${env_name}"
elif [ "$git_branch" = "pre-prod" ];then
    env_name="preprod"
    bucket_name="${app_name}-${env_name}"
elif [ "$git_branch" = "bbc-staging" ];then
    env_name="bbc-staging"
    bucket_name="${app_name}-${env_name}"
else
   env_name="staging"
   bucket_name="${app_name}-${env_name}"
fi





tag="${git_branch}-${git_commit}-${epoch}"
echo "$tag" > docker-tag.txt

$(aws ecr get-login --no-include-email --region ${AWS_REGION})

#echo "Copying Jenkins Private Key to Docker Build Directory."
#cp ~/.ssh/gitlab-docker-ssh id_rsa
echo "*** Building Docker image: ${REGISTRY}/${ecr_repo_name}:${tag} ***"
docker build --build-arg git_commit_id=${git_commit} --build-arg bucket_name=${bucket_name} --build-arg nexus_token=${NEXUS_NPM_TOKEN} -t ${REGISTRY}/${ecr_repo_name}:${tag} .
echo "S3 built successfully".
if [ $local = 0 ];then
   docker rmi -f ${REGISTRY}/${ecr_repo_name}:${tag}
   echo "Deleted image from build server."
fi

#echo "Removing Jenkins Private Key to Docker Build Directory."
#rm id_rsa

