import Loader from '@components/Loader/Loader';
import { useFirstTrade } from '@src/components/provider/AppProvider';
import React from 'react';

function FirstTradeHOC({ children }) {
  const { isFirstTradeDataLoading, isFirstTradeEligible } = useFirstTrade();
  if (isFirstTradeDataLoading || isFirstTradeEligible === null)
    return <Loader />;

  return React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        firstTrade: isFirstTradeEligible,
      });
    }
  });
}

export default FirstTradeHOC;
