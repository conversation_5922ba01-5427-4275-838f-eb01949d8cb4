import React from 'react';

import { useAggregator } from '../../query/aggregatorQuery';

import RenderPage from './RenderPage';
import { useStoreFront } from '@src/query/storeFrontQuery';
import { downloadNudgeSF } from '@src/config/urlConfig';

const AggregatorComponent = ({ queryProps, loader }) => WrappedComponent => {
  function BaseComponent(props) {
    const { isLoading, data, refetch: refetchAggrData } = useAggregator(
      queryProps,
    );

    const { data: storeFrontDataDownloadPML } = useStoreFront({
      name: 'downloadNudge',
      url: downloadNudgeSF(),
    });

    if (isLoading) {
      return loader;
    }

    return (
      <>
        <WrappedComponent
          {...props}
          pageComponent={RenderPage}
          pages={data}
          refetchAggrData={refetchAggrData}
          storeFrontDataDownloadPML={storeFrontDataDownloadPML}
        />
      </>
    );
  }

  return BaseComponent;
};

export default AggregatorComponent;
