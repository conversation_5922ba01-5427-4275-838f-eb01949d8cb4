import Loader from '@components/Loader/Loader';
import history from '@src/history';
import { DEFAULT_AMOUNT } from '@src/pages/FirstInvestment/constants';
import { useGetFundsSummaryV2 } from '@src/query/generalQuery';
import React from 'react';

function FundsSummaryHOC({ children, ...props }) {
  const amountFromState = history.location?.state?.amount;
  const { data: summaryData, isLoading } = useGetFundsSummaryV2(
    !amountFromState,
  );
  const amount =
    amountFromState ||
    summaryData?.data?.available_funds ||
    summaryData?.data?.funds_summary?.available_cash;

  if (isLoading) return <Loader />;

  return React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        amount: amount || DEFAULT_AMOUNT,
        availableFunds: amount || 0,
        ...props,
      });
    }
  });
}

export default FundsSummaryHOC;
