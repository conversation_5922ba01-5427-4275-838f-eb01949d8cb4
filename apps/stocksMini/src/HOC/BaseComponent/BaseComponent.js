import React, { useContext } from 'react';
import axios from 'axios';

import Loader from '../../components/Loader/Loader';
import AppContext from '../../context/AppContext';
import Drawer from '../../components/Drawer';
import Button from '../../components/Button/Button';
import { setRootError } from '../../actions/genericActions';
import { useBackPress } from '../../utils/Equities/hooks';
import { mapErrorCode } from '../../utils/errorUtils';
import { API_ERROR } from '../../utils/Constants';

import s from './BaseComponent.scss';
// import ExitAppOverlay from '../../components/ExitAppOverlay/ExitAppOverlay';

const baseComponent = WrappedComponent => {
  function BaseComponent(props) {
    // useStyles(s);
    const { Context } = useContext(AppContext);
    // eslint-disable-next-line no-underscore-dangle
    window._context = Context;

    const { CancelToken } = axios;
    const axiosSource = CancelToken.source();

    const showLoader = loader => {
      if (loader) return <Loader />;
    };

    const { popStack } = useBackPress();

    const showErrorPopup = errorData => {
      if (errorData) {
        function handleCta() {
          if (errorData.callback) {
            errorData.callback();
          } else {
            setRootError(false);
          }

          popStack();
        }
        const handlePopupClose = () => {
          setRootError(false);
        };
        return (
          <Drawer
            headerStyles={s.header}
            isOpen
            popup
            showCross={false}
            onClose={handlePopupClose}
          >
            <div className={s.ErrorWrapper}>
              {errorData.mainMsg && <h3>{errorData.mainMsg}</h3>}
              <p>
                {errorData?.message ||
                  errorData?.Message ||
                  errorData?.error ||
                  errorData?.meta?.displayMessage ||
                  API_ERROR.MESSAGE}
              </p>

              {errorData?.code || mapErrorCode(errorData?.meta) ? (
                <p className={s.code}>
                  {API_ERROR.ERROR_CODE(
                    errorData?.code || mapErrorCode(errorData?.meta),
                  )}
                </p>
              ) : null}

              {!errorData.hideBtn && (
                <Button
                  isPrimaryBlue
                  buttonText={errorData.btnTxt || 'OK'}
                  onClickHandler={handleCta}
                />
              )}
            </div>
          </Drawer>
        );
      }
    };

    const { loader, error } = Context;

    return (
      <>
        <WrappedComponent
          {...props}
          context={Context}
          axiosSource={axiosSource}
        />
        {showLoader(loader)}
        {showErrorPopup(error)}
        {/* <ExitAppOverlay /> */}
      </>
    );
  }

  return BaseComponent;
};

export default baseComponent;
