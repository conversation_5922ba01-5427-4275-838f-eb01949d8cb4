import React from 'react';
import { usePaymentsFlow } from '@utils/Equities/hooks/usePaymentsFlow';
import Loader from '@components/Loader/Loader';
import { TYPE } from '@HOC/AddFunds/Statics';

function AddFunds({ children, type = TYPE.PAY_IN }) {
  const { isLoading, renderAddFunds } = usePaymentsFlow({
    type,
  });

  if (isLoading) return <Loader />;

  if (renderAddFunds) return children;

  return null;
}

export default AddFunds;
