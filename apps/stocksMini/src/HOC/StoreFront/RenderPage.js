import React, { useEffect, useState } from 'react';
import { SFWidget } from 'storefront_h5_sdk';
import { log } from '@utils/commonUtils.js';
import { combineDashboardDeepTypeHelper } from '@src/utils/navigationUtil';
import { useStoreFrontBackUpData } from '@query/storeFrontQuery';
import { useAnalyticsEventForBbc } from '@utils/Equities/hooks';
import generateLabel from '@src/analyticsEvents/utils';
import { PULSE_STATICS } from '@src/pages/CombinedDashboard/enums';

import { getComponent } from './customComponentConfig';

const RenderPage = ({
  data,
  viewConfig,
  props = {},
  storeFrontName = null,
  backupUrl = null,
}) => {
  const { sendAnalyticsEventBBC } = useAnalyticsEventForBbc();
  const [componentData, setComponentData] = useState(
    data.views[0]?.items?.length || !backupUrl ? data : null, // TODO : update once all backfile created
  );

  const { isLoading, data: backUpData, isError } = useStoreFrontBackUpData(
    storeFrontName,
    backupUrl,
    !data.views[0]?.items?.length && !!backupUrl, // TODO : update once all backfile created
  );

  const getSpecificComponent = () => {
    const result = {
      views: [],
    };
    const viewData = data.views[0];
    const key =
      viewData?.type === 'custom'
        ? viewData.properties.custom_type
        : viewData.title;
    result.views.push(backUpData?.[key]);
    if (!componentData) setComponentData(result);
  };

  useEffect(() => {
    if (!isLoading && backUpData) getSpecificComponent();
  }, [isLoading, backUpData]);

  useEffect(() => {
    if (isError && !isLoading) setComponentData(data);
  }, [isLoading, isError]);

  const sfWidget = {
    ScreenName: PULSE_STATICS.SCREEN_NAME,
    Category: PULSE_STATICS.CATEGORY,
    Action: '',
  };

  const pulseEvents = (sfEvents, id, name) => {
    const label = {
      id,
      name,
    };
    sendAnalyticsEventBBC(
      sfEvents?.ScreenName,
      sfEvents?.Category,
      sfEvents?.Action,
      generateLabel(label),
    );
  };

  const handleWidgetClick = item => {
    pulseEvents(sfWidget, item?.slotId, item?.name);
    const { ct_link, ct_type, title } = item;
    if (title === 'SIP') {
      localStorage.setItem('fromHomePage', true);
    }
    combineDashboardDeepTypeHelper(ct_type, ct_link);
  };

  if (isLoading || !componentData) return <></>;

  if (componentData?.views?.[0]?.type === 'custom') {
    const Component = getComponent(
      componentData.views[0],
      { ...props, ...componentData.views[0] },
      viewConfig,
    );
    if (Component) {
      return Component;
    }
    return <></>;
  }

  return (
    <SFWidget
      key={componentData?.id}
      widgetId={componentData?.id}
      type={componentData?.views?.[0]?.type}
      data={componentData?.views?.[0]}
      handleClick={val => handleWidgetClick(val)}
      longPressDelay={1000}
      handleLongPress={val => {
        log('long press event:', val);
      }}
    />
  );
};
export default RenderPage;
