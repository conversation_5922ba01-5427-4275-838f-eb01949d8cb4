import React from 'react';

import { useStoreFront } from '../../query/storeFrontQuery';

import RenderPage from './RenderPage';

const StoreFrontComponent = ({ queryProps, loader }) => WrappedComponent => {
  function BaseComponent() {
    const { isLoading, data } = useStoreFront(queryProps);

    if (isLoading) {
      return loader;
    }

    return (
      <>
        <WrappedComponent pageComponent={RenderPage} pages={data.page} />
      </>
    );
  }

  return BaseComponent;
};

export default StoreFrontComponent;
