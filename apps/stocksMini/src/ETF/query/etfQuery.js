import { useQuery } from 'react-query';

// import { etfScrips } from '@src/actions/genericActions';
import { getGenericAppHeaders, makeApiGetCall } from '@src/utils/apiUtil';
import { AxiosErrorHandler } from '@src/utils/errorUtils';
import { SIP } from '../config/urlConfig';

// export const useLowTrackingEtf = () => {
//   //TODO - change sort: 'ONE_YEAR_RETURN' to Tracking error once available,
//   const params = {
//     i: 'ETF',
//     p: 0,
//     ps: 5,
//     sort: 'ONE_YEAR_RETURN',
//     so: 'DESC',
//     x: 'NSE',
//     fp: 0,
//     tp: 1000000,
//   };

//   return useQuery(
//     ['lowTrackingEtf'],
//     async () => {
//       const response = await etfScrips(params);
//       return response;
//     },
//     { staleTime: Infinity },
//   );
// };

export const useCheckSip = () =>
  useQuery(
    ['checkSip'],
    async () => {
      try {
        const response = await makeApiGetCall({
          url: SIP.CHECK,
          headers: getGenericAppHeaders(),
        });
        return response.data.data || [];
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
        return [];
      }
    },
    { staleTime: Infinity },
  );

export const useFeaturedEtfConfig = (category, url, isEnabled = true) => {
  const queryKey = ['categoryDetail', category];
  return useQuery(
    queryKey,
    async () => {
      try {
        const { data } = await makeApiGetCall({
          url,
        });
        return data;
      } catch (error) {
        AxiosErrorHandler(error, false, false, false, true);
      }
    },
    { staleTime: Infinity, enabled: isEnabled },
  );
};
