import { useQuery } from 'react-query';
import { getUserId } from '@src/services/coreUtil';

import axios from 'axios';
import { STORE_FRONT_URL } from '../config/urlConfig';

export const useEtfStoreFront = (isEnabled = true) => {
  const userId = getUserId();
  const storeFrontURl = STORE_FRONT_URL(userId);

  const headers = {
    user_id: userId,
  };

  const config = {
    method: 'post',
    url: storeFrontURl,
    headers,
  };

  return useQuery(
    'etfStoreFront',
    async () => {
      const { data } = await axios(config);

      const pages = {};

      for (const { id, views } of data.page) {
        pages[id] = views;
      }

      return pages;
    },
    { enabled: isEnabled },
  );
};
