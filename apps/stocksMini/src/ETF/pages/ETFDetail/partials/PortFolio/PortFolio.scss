@import 'src/commonStyles/commoncss';

.wrapper {
  margin-top: 25px;
}

.portfolioCard {
  background-color: map-get($colors, LightGrayishBg);
  border: solid 1px map-get($colors, ETFLightBlue);
  padding: 15px;
  margin-top: 10px;
}

.row {
  display: flex;
  justify-content: space-between;
}
.rowItem {
  width: 45%;
  margin: 10px 0;
}

.label {
  @include fontStyle(12px, map-get($colors, DGREY5), normal);
  line-height: 1.75;
  height: 21px;
  display: flex;
  align-items: center;
}

.value {
  @include fontStyle(12px, map-get($colors, Black5), 600);
  line-height: 1.67;
  height: 20px;
  display: flex;
  align-items: center;
}

.prices > div {
  font-size: 12px;
}

.shareIcon {
  margin-top: -3px;
  margin-right: 5px;
}

.changeIcon {
  margin-left: 5px;
}
