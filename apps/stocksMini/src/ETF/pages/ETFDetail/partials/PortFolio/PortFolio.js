import React from 'react';
import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import Card from '@src/ETF/components/Card/Card';
import PortfolioEtfIcon from '@assets/etf/in_your_portfolio.svg';
import { useStockFeed } from '@src/utils/Equities/hooks';
import { roundValue } from '@src/utils/commonUtils';
import Icon, { ICONS_NAME } from '@src/components/Icon';
import PortfolioShare from '@assets/etf/portfolio_share.svg';
import { ChangeWithPercent, Rupee } from '@components/Prices';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import styles from './PortFolio.scss';

const PortFolioSection = ({
  costPrice,
  quantity,
  securityId,
  exchange,
  segment,
}) => {
  const { ltp } = useStockFeed({
    securityId: parseInt(securityId, 10),
    exchange,
    segment,
  });

  const overallReturns = roundValue(quantity * (ltp - costPrice));
  const percentageChangeVal = roundValue(((ltp - costPrice) * 100) / costPrice);
  const investedValue = roundValue(quantity * costPrice);
  const currentValue = roundValue(quantity * ltp);
  return (
    <div className={styles.wrapper}>
      <SectionHeader
        title={ETF_DETAIL.PORTFOLIO.TITLE}
        titleSize="small"
        icon={PortfolioEtfIcon}
      />
      <Card customClass={styles.portfolioCard}>
        <div className={styles.row}>
          <div className={styles.rowItem}>
            <div className={styles.label}>
              {ETF_DETAIL.PORTFOLIO.INVESTMENT_LABEL}
            </div>
            <div className={styles.value}>
              {!costPrice || costPrice <= 0 ? '-' : investedValue}
            </div>
          </div>
          <div className={styles.rowItem}>
            <div className={styles.label}>
              {ETF_DETAIL.PORTFOLIO.SHARE_DETAIL_LABEL}
            </div>
            <div className={styles.value}>
              <img className={styles.shareIcon} src={PortfolioShare} alt="" />
              {quantity}
              {' x '}
              {roundValue(costPrice)}
            </div>
          </div>
        </div>
        <br />
        <></>

        <div className={styles.row}>
          <div className={styles.rowItem}>
            <div className={styles.label}>
              {ETF_DETAIL.PORTFOLIO.CURRENT_VALUE_LABEL}
            </div>
            <div className={styles.value}>
              <Rupee
                className={styles.prices}
                value={currentValue}
                changeWithSign
                withRupee
              />
            </div>
          </div>
          <div className={styles.rowItem}>
            <div className={styles.label}>
              {ETF_DETAIL.PORTFOLIO.RETURN_LABEL}
            </div>
            <div className={styles.value}>
              {!costPrice || costPrice <= 0 ? (
                '-'
              ) : (
                <>
                  <ChangeWithPercent
                    value={overallReturns}
                    percent={percentageChangeVal}
                    withRupee
                    changeWithSign
                    percentWithSign
                    className={styles.prices}
                  />
                  <div className={styles.changeIcon}>
                    <Icon
                      name={
                        percentageChangeVal > 0
                          ? ICONS_NAME.ETF_PROFIT
                          : percentageChangeVal < 0
                          ? ICONS_NAME.ETF_LOSE
                          : ''
                      }
                      width={12}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PortFolioSection;
