import Card from '@src/components/Card/Card';
import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import ManageSipIcon from '@assets/etf/etf_manage_sip.svg';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import React from 'react';
import styles from './ManageSip.scss';

const ManageSip = ({ upcomingSip }) => {
  const getDayDateLabel = (frequency, dayOrDate) => {
    switch (frequency) {
      case `MONTHLY`:
        return `${dayOrDate} of Every Month`;

      case 'WEEKLY':
        return `Every ${dayOrDate}`;

      default:
        return ``;
    }
  };
  return (
    <Card customClass={styles.wrapper}>
      <SectionHeader
        title={ETF_DETAIL.MANAGE_SIP.TITLE}
        titleSize="small"
        icon={ManageSipIcon}
      />
      <div className={styles.sipWrapper}>
        {upcomingSip.map((list, index) => (
          <div className={styles.sipCard} key={index}>
            <div className={styles.sipDetailsRow}>
              <span className={styles.freq}>{list.frequency}</span>
              <span className={styles.qty}>
                Qty: <span>{list.qty}</span>
              </span>
            </div>
            <div className={styles.sipDetailsRow}>
              <span className={styles.date}>
                {getDayDateLabel(list.frequency, list.sip_date || list.sip_day)}
              </span>
              <span className={styles.exchange}>{list.exchange}</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default ManageSip;
