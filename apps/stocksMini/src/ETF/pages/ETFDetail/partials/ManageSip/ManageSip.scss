@import 'src/commonStyles/commoncss';

.wrapper {
  border: solid 1px map-get($colors, ETFLightBlue);
  padding: 7px 15px;
  margin-top: 25px;
  background-color: map-get($colors, LightGrayishBg);
}

.sipWrapper {
  margin-top: 15px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  row-gap: 10px;
}

.sipCard {
  background-color: map-get($colors, secondaryBgColor);
  padding: 10px;
  box-shadow: 0 4px 10px 0  map-get($colors, DGREYShadow);
  border-radius: 10px;
}

.sipDetailsRow {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.freq {
  @include fontStyle(10px, map-get($colors, Green5), normal);
  border-radius: 4px;
  border: solid 0.5px map-get($colors, Green5);
  padding: 1px 5px;
  line-height: 1.6;
}

.date {
  @include fontStyle(10px, map-get($colors, DGREY5), normal);
  line-height: 1.5;
  margin-top: 5px;
}

.exchange {
  @include fontStyle(10px, map-get($colors, DGREY5), normal);
  padding: 4px 6px;
  border-radius: 4px;
  border: solid 0.5px  map-get($colors, DGREY5);  
}

.qty {
  @include fontStyle(10px, map-get($colors, BlackOpacity7), normal);
  line-height: 1.33;
  margin-top: 4px;
}

.qty > span {
  font-weight: bold;
}