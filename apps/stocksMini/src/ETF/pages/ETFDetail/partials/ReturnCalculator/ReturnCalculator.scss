@import 'src/commonStyles/commoncss';

.container {
  margin: 25px 0 75px;
  position: relative;

  .calculatorContainer {
    margin-top: 13px;
    border-radius: 10px;
    border: solid 1px map-get($colors, BlackOpacity1);
    padding: 20px 15px;
    background-color: map-get($colors, PureWhite);

    .returnBarContainer {
      display: flex;
      justify-content: space-between;

      .returnBar {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: end;

        .return {
          padding: 0 8px;
          margin-bottom: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .price {
            @include typography(body2B, map-get($colors, BlackOpacity7));
            text-align: center;
          }
        }

        .progressBar {
          width: 30px;
          max-height: 100px;

          border-radius: 5px 5px 0 0;
          overflow: hidden;
          position: relative;

          .filled {
            max-height: 50%;
            width: 100%;
          }

          .profit {
            background-color: map-get($colors, Green5);
          }

          .loss {
            background-color: map-get($colors, ETFRed);
          }

          .empty {
            height: 50px;
            width: 100%;
            background-color: rgba(33, 193, 121, 0.1);
          }
        }

        .divider {
          height: 1px;
          background-color: map-get($colors, BlackOpacity1);
          width: 100%;
        }

        .lable {
          margin-top: 10px;

          @include typography(body2B, map-get($colors, BlackOpacity7));
        }
      }
    }
  }
}

.returnData {
  background-color: map-get($colors, LightGrayishBg);
  padding: 15px 15px;
  margin: 20px 0px;
  border-radius: 10px;
}

.returnDataRow {
  display: flex;
  justify-content: space-between;
}

.returnDataLabel {
  line-height: 1.5;
  @include fontStyle(12px, map-get($colors, Black5), 600);
}

.rowDivider {
  background-color: map-get($colors, DGREYShadow);
  height: 0.8px;
  width: 100%;
  margin: 10px 0px;
}

.resultantValue {
  @include fontStyle(20px, map-get($colors, Black5), 600);
  line-height: 1.5;
}

.flexDisplay {
  display: flex;
}

.inputStyle {
  text-align: center;
  input {
    text-align: center;
    @include fontStyle(
      28px !important,
      map-get($colors, Black5) !important,
      bold !important
    );
    line-height: 1.43;
    border: none !important;
  }
  label {
    @include fontStyle(12px, map-get($colors, Black5), 600);
  }
}

.inputContainer {
  text-align: center;
  margin-bottom: 30px;
}
.line {
  width: 170px;
  border-bottom: 1px solid map-get($colors, LGrey);
  margin: 0 auto 20px;
}

.amountChip {
  display: inline-block;
  @include fontStyle(14px, map-get($colors, BlackHalfOpacity), 600);
  line-height: 1.43;
  border-radius: 100px;
  border: solid 1px map-get($colors, BlackOpacity1);
  margin-right: 10px;
  padding: 5px 10px;
  margin-bottom: 5px;
}

.actionContainer {
  display: flex;
  justify-content: center;
}

.rangeContainer {
  margin: 30px 15px;
}

.rangeLabels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin: 5px 0 0 0;
  line-height: 1.33;
  color: rgba(16, 16, 16, 0.5);
  font-family: Inter;
}

.note {
  @include fontStyle(10px, map-get($colors, Black5), 600);
  background-color: map-get($colors, LightYellowBg);
  border: 1px solid map-get($colors, LightYellowBorder);
  padding: 45px 15px 10px 15px;
  border-radius: 0 0 10px 10px;
  width: 100%;
  font-family: Inter;
  line-height: 1.6;
  position: absolute;
  bottom: -50px;
  z-index: -1;
}

.selected {
  color: map-get($colors, ETFBlue);
}

.buttonContainer {
  display: flex;
}

.arrow {
  padding-left: 5px;
}

.returnText {
  line-height: 1.43;
  align-items: center;

  > div {
    font-size: 10px;
  }

  > div:first-child {
    padding-right: 3px;
    font-size: 14px;
  }
}
