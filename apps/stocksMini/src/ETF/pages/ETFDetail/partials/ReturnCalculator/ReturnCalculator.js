import React, { lazy, Suspense, useEffect, useState } from 'react';
import classNames from 'classnames';

import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import InputRange from '@src/ETF/components/InputRange/InputRange';
import Button from '@src/ETF/components/Button/Button';
import ReturnCalculatorIcon from '@assets/etf/return_calculator_icon.svg';
import { useToast } from '@src/components/provider/AppProvider';
import { calculateAbsolute, calculateCagr } from '@src/ETF/utils/utilFunctions';
import { ChangeWithPercent, formatPrice } from '@components/Prices';
import InputBox from '@src/components/InputBox/InputBox';
import Icon, { ICONS_NAME } from '@src/components/Icon';
import ToogleButton from '@src/ETF/components/ToggleButton/ToggleButton';
import Shimmer from '@src/components/Shimmer/Shimmer';
import If from '@src/components/If';
import { validInstrumentTypeForSIP } from '@src/pages/CompanyDetail/partials/PriceAlertAndStartSip/utils';
import { RETURN_CALCULATOR } from '@src/ETF/components/enum';
import IndianNumberingSystem from '@src/components/IndianNumberingSystem/IndianNumberingSystem';

import Change from '../../../../components/Change/Change';
import styles from './ReturnCalculator.scss';

const StartSip = lazy(() =>
  import(
    /* webpackChunkName: 'CompanyDetailsStartSip' */ '@src/pages/CompanyDetail/partials/StartSip'
  ),
);

const ReturnCalculator = ({
  currentEtfPrice,
  pclose = null,
  id,
  buy,
  companyDetails,
}) => {
  const [inputValue, onInputChange] = useState(
    ETF_DETAIL.RETURN_CALCULATOR.INIT_AMOUNT,
  );
  const [error] = useState();
  const [timePeriodIndex, setTimePeriodIndex] = useState(
    ETF_DETAIL.RETURN_CALCULATOR.INIT_TF_INDEX,
  );
  const [etfIR, setetfIR] = useState(0);
  const [toggleType, handleToggle] = useState(
    ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[1].key,
  );
  const [data, handleData] = useState(ETF_DETAIL.RETURN_CALCULATOR.DATA_ARR);

  const { addToast, appearanceType } = useToast();

  const calculateSI = (p, t, r) => (p ? (p * r * t) / 100 : 0);

  const calculateCI = (p, t, r) => {
    const ci = p ? p * (1 + r / 100) ** t : 0;
    return ci - p;
  };

  const calculateIR = (pCloseData, tpIndex) => {
    switch (toggleType) {
      case ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[0].key:
        return calculateAbsolute(
          pCloseData[tpIndex].pcloseValue,
          currentEtfPrice,
        );
      case ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[1].key:
        return calculateCagr(
          pCloseData[tpIndex].pcloseValue,
          currentEtfPrice,
          pCloseData?.[tpIndex].year,
        );

      default:
        return 0;
    }
  };

  const calculateIntrest = (...args) => {
    if (toggleType === ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[0].key) {
      return calculateSI(...args);
    }
    return calculateCI(...args);
  };

  const totalReturn = (ir, t) => {
    if (toggleType === ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[1].key) {
      return ir;
    }
    return ir * t;
  };

  useEffect(() => {
    setTimePeriodIndex(ETF_DETAIL.RETURN_CALCULATOR.INIT_TF_INDEX);
  }, [id]);

  const handleTimePeriodIndexChange = length => {
    if (pclose?.length > length) {
      setetfIR(calculateIR(pclose, timePeriodIndex));
    } else if (length) {
      setTimePeriodIndex(0);
    } else {
      addToast({
        message: `${ETF_DETAIL.RETURN_CALCULATOR.TF_ARR[0].value}Y Returns not present.`,
        type: appearanceType.FAIL,
      });
      setetfIR(0);
    }
  };

  useEffect(() => {
    if (pclose != null && currentEtfPrice) {
      switch (timePeriodIndex) {
        case 0:
          handleTimePeriodIndexChange(0, 1);
          break;
        case 1:
          handleTimePeriodIndexChange(1, 3);
          break;
        case 2:
          handleTimePeriodIndexChange(2, 5);
          break;
        default:
          setetfIR(0);
      }
    } else {
      setetfIR(0);
    }
  }, [timePeriodIndex, toggleType, pclose, currentEtfPrice]);

  useEffect(() => {
    const newData = [...data];
    const tf = ETF_DETAIL.RETURN_CALCULATOR.TF_ARR[timePeriodIndex].value;
    newData.forEach((element, i) => {
      const ir = i === 0 ? etfIR : element.change;
      let returnValue = calculateIntrest(
        inputValue,
        i === 0 && toggleType === ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST[0].key
          ? 1
          : tf,
        ir,
      );
      returnValue = returnValue || 0;
      const amount = inputValue || 0;
      newData[i].totalChange = i ? totalReturn(ir, tf) : ir;
      newData[i].totalAmount = amount + returnValue;
      newData[i].return = returnValue;
    });
    handleData(newData);
  }, [etfIR, inputValue]);

  const handleChipClick = value => {
    const input = inputValue || 0;
    onInputChange(input + value);
  };

  const handleTf = value => {
    const indexValue = parseInt(value, 10);
    if (pclose?.length <= indexValue) {
      addToast({
        message: `${ETF_DETAIL.RETURN_CALCULATOR.TF_ARR[indexValue].value}Y Returns not present.`,
        type: appearanceType.FAIL,
      });
    } else {
      setTimePeriodIndex(indexValue);
    }
  };

  const handleInputChange = e => {
    const value = e.target.value
      .replace(/^\s+|\s+$/gm, '') // to remove spaces
      .replace(/₹/g, '') // to remove '₹' symbol
      .replace(/,/g, ''); // to remove ',' comma
    onInputChange(parseInt(value, 10));
  };

  const enableStartSip = validInstrumentTypeForSIP({
    instrumentType: companyDetails?.instrument_type,
  });

  return (
    <>
      <div className={styles.container}>
        <SectionHeader
          title={ETF_DETAIL.RETURN_CALCULATOR.TITLE}
          titleSize="small"
          icon={ReturnCalculatorIcon}
        />
        <div className={styles.calculatorContainer}>
          <div className={styles.inputContainer}>
            <InputBox
              onChange={handleInputChange}
              value={`₹ ${formatPrice(inputValue, 0)}`}
              data-value={inputValue}
              inputMode="decimal"
              label="Investment Amount"
              customStyles={styles.inputStyle}
              maxLength="14"
            />
            <div className={styles.line} />
            {error && <div className={styles.errorText}>{error}</div>}
            {ETF_DETAIL.RETURN_CALCULATOR.CHIP_VALUE.map((v, index) => (
              <div
                key={index}
                className={styles.amountChip}
                onClick={() => handleChipClick(v.value)}
                type="white"
              >
                {v.label}
              </div>
            ))}
          </div>
          <div className={styles.returnBarContainer}>
            {data.map((v, index) => (
              <div className={styles.returnBar} key={v.id}>
                {index === 0 && !etfIR ? (
                  <Shimmer
                    type="line"
                    width="55px"
                    height="30px"
                    margin="35px 0 20px"
                  />
                ) : (
                  <>
                    <div className={styles.return}>
                      <div className={styles.price}>
                        <IndianNumberingSystem
                          number={parseInt(v.totalAmount, 10)}
                        />
                      </div>
                      <div className={styles.flexDisplay}>
                        <Change
                          value={parseFloat(
                            index === 0 ? etfIR : v.totalChange,
                          ).toFixed(2)}
                        />
                        <Icon
                          name={
                            parseFloat(index === 0 ? etfIR : v.totalChange) > 0
                              ? ICONS_NAME.ETF_PROFIT
                              : parseFloat(
                                  index === 0 ? etfIR : v.totalChange,
                                ) < 0
                              ? ICONS_NAME.ETF_LOSE
                              : ''
                          }
                          width={12}
                        />
                      </div>
                    </div>
                  </>
                )}
                <div
                  className={styles.progressBar}
                  style={{
                    height: `${50 +
                      Number(index === 0 ? etfIR : v.totalChange)}px`,
                  }}
                >
                  <div
                    className={classNames(
                      styles.filled,
                      (index === 0 ? etfIR : v.totalChange) > 0
                        ? styles.profit
                        : styles.loss,
                    )}
                    style={{
                      height: `${Math.abs(
                        index === 0 ? etfIR : v.totalChange,
                      )}px`,
                    }}
                  />
                  <div className={styles.empty} />
                </div>
                <div className={styles.divider} />
                <div className={styles.lable}>{v.label}</div>
              </div>
            ))}
          </div>
          <div className={styles.returnData}>
            <div className={styles.returnDataRow}>
              <div className={styles.returnDataLabel}>
                {ETF_DETAIL.RETURN_CALCULATOR.RESULTANT_AMOUNT}
              </div>
              <div className={styles.resultantValue}>
                <IndianNumberingSystem
                  number={parseInt(data[0].totalAmount, 10)}
                />
              </div>
            </div>
            <div className={styles.rowDivider} />
            <div className={styles.returnDataRow}>
              <div className={styles.returnDataLabel}>
                {ETF_DETAIL.RETURN_CALCULATOR.RETURNS}
              </div>
              {!data[0]?.return ? (
                <Shimmer type="line" width="70px" height="18px" />
              ) : (
                <div className={styles.flexDisplay}>
                  <ChangeWithPercent
                    value={data[0]?.return}
                    percent={data[0]?.totalChange}
                    withRupee
                    changeWithSign
                    className={styles.returnText}
                  />
                  <Icon
                    name={
                      data[0]?.totalChange > 0
                        ? ICONS_NAME.ETF_PROFIT
                        : data[0]?.totalChange < 0
                        ? ICONS_NAME.ETF_LOSE
                        : ''
                    }
                    width={12}
                    iconStyles={styles.arrow}
                  />
                </div>
              )}
            </div>
          </div>

          <div className={styles.actionContainer}>
            <ToogleButton
              config={ETF_DETAIL.RETURN_CALCULATOR.TYPE_LIST}
              onClick={handleToggle}
              activeBtn={toggleType}
            />
          </div>
          <div className={styles.rangeContainer}>
            <InputRange
              min={0}
              max={2}
              value={timePeriodIndex}
              onChange={e => handleTf(e)}
              step={1}
            />

            <div className={styles.rangeLabels}>
              {ETF_DETAIL.RETURN_CALCULATOR.TF_ARR.map((range, index) => (
                <div
                  key={index}
                  className={
                    ETF_DETAIL.RETURN_CALCULATOR.TF_ARR[timePeriodIndex]
                      .label === range.label
                      ? styles.selected
                      : ''
                  }
                >
                  {range.label}
                </div>
              ))}
            </div>
          </div>
          <div className={styles.buttonContainer}>
            <Button type="blue" height="large" width="full" onClick={buy}>
              {RETURN_CALCULATOR.INVEST_NOW}
            </Button>
            <If test={enableStartSip}>
              <Suspense fallback={<></>}>
                <StartSip isETF companyDetail={companyDetails} />
              </Suspense>
            </If>
          </div>
        </div>
        <div className={styles.note}>{ETF_DETAIL.RETURN_CALCULATOR.NOTE}</div>
      </div>
    </>
  );
};

export default ReturnCalculator;
