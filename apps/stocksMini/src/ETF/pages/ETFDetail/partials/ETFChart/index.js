import React, { memo, useRef, useEffect, useState } from 'react';
import cx from 'classnames';

import LiveIcon from '@src/components/LiveIcon';
import Charts from '@src/components/Charts';
import Shimmer from '@src/components/Shimmer/Shimmer';
import { Change } from '@src/components/Prices';
import { recentSearchLocalStorage } from '@src/utils/commonUtils';
import { useStockFeed } from '@src/utils/Equities/hooks';
import styles from './index.scss';
import history from '../../../../../history';
import CAGROverview from '../CAGROverview';

const ETFChartShimmer = () => (
  <>
    <Shimmer type="line" width="100%" height="69px" margin="20px 0 0" />
  </>
);

function CurrentSharePrice({ exchange, securityId }) {
  const ltpRef = useRef(null);
  const { ltp } = useStockFeed({
    exchange,
    securityId,
  });

  useEffect(() => {
    const stockData = history.location?.state?.stockData;
    if (!stockData?.storeInLocal) {
      ltpRef.current = false;
    }
    if (ltpRef.current === null && ltp !== undefined) {
      ltpRef.current = ltp;
      if (stockData?.storeInLocal) {
        recentSearchLocalStorage.add({
          ...stockData.companyData,
          p_close: ltpRef.current,
        });
      }
    }
  }, [ltp, ltpRef]);

  return ltp === undefined ? (
    <div className={styles.ltp}>
      <Shimmer
        width="158px"
        height="35px"
        className={styles.shimmerCustomClass}
      />
    </div>
  ) : (
    <Change value={ltp} withRupee className={styles.ltp} />
  );
}

const ETFChart = ({
  securityId,
  exchange,
  id,
  name,
  instrumentType,
  segment,
  isCompanyDetailLoading,
  cagrLabel,
  cagrValue,
  isCagrLoading = true,
}) => {
  // states
  const [expanded, setExpanded] = useState(false);

  if (!securityId || !exchange || !id || !name || !instrumentType) {
    return <ETFChartShimmer />;
  }

  const handleChartExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <section className={styles.chartSection}>
      <div className={cx(styles.head, styles.dFlex, styles.alignItemsCenter)}>
        <CurrentSharePrice exchange={exchange} securityId={securityId} />
        <LiveIcon exchange={exchange} />
      </div>
      {!expanded ? (
        <div onClick={handleChartExpand}>
          <CAGROverview
            isLoading={isCagrLoading}
            cagrValue={cagrValue}
            cagrLabel={cagrLabel}
          />
        </div>
      ) : (
        <div className={styles.chartWrapper}>
          <Charts
            stockSecurityId={securityId}
            stockName={name}
            stockExchangeType={exchange}
            segment={segment}
            stockId={id}
            isCollapsible
            handleCollapse={handleChartExpand}
          />
        </div>
      )}

      {isCompanyDetailLoading && (
        <div className={styles.chartSectionShimmerContainer}>
          <ETFChartShimmer />
        </div>
      )}
    </section>
  );
};

export default memo(ETFChart);
