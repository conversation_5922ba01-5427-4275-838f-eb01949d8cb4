@import 'src/commonStyles/commoncss';

.chartSection {
  position: relative;
  margin-top: 21px;

  h1 {
    @include typography(heading1B1, map-get($colors, DBlue2));
  }

  .chartSectionShimmerContainer {
    position: absolute;
    width: 100%;
    top: 0;
    background-color: map-get($colors, secondaryBgColor);
  }
}

.head {
  margin-bottom: 20px;
}

.ltp {
  margin-right: 11px;
  @include typography(heading266136, map-get($colors, DBlue2));
}

.shimmerCustomClass {
  border-radius: 6px;
}

.chartWrapper {
  overflow: hidden;
  outline: none;
  border-radius: 10px;
  border: solid 1px map-get($colors, ETFBorder);
  background-color: map-get($colors, PureWhite);
  padding-top: 15px;
}
