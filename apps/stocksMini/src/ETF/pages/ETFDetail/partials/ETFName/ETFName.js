import React from 'react';
import Icon, { LOGO_SIZE } from '@components/Icon';
import Chip from '@src/ETF/components/Chip/Chip';
import styles from './ETFName.scss';

const ETFName = ({ id, name, subCategory, onViewAllClick }) => (
  <div className={styles.wrapper}>
    <Icon
      name={id}
      companyName={name}
      className={styles.logo}
      logoSize={LOGO_SIZE.LARGE}
    />
    <div>
      <div className={styles.name}>{name}</div>
      <div className={styles.chipWrapper}>
        <Chip customClass={styles.chip} onClickHandler={onViewAllClick}>
          {subCategory}
        </Chip>
      </div>
    </div>
  </div>
);

export default ETFName;
