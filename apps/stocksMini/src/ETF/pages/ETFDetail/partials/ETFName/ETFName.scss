@import 'src/commonStyles/commoncss';

.wrapper {
  display: flex;
  align-items: center;

  .logo {
    margin-right: 10px;
  }

  .name {
    @include typography(heading3B, map-get($colors, Black5));
  }

  .chipWrapper {
    display: flex;
  }

  .chip {
    height: 18px;
    display: flex;
    font-size: 10px;
    padding: 0 10px;
    font-weight: 500;
    line-height: 1.6;
    color: map-get($colors, BlackHalfOpacity);
    border-radius: 42px;
    border: solid 1px map-get($colors, ETFLightBlue);
    background-color: map-get($colors, LightGrayishBg);
    margin: 5px;
  }
}
