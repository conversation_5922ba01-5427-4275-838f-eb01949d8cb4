.wrapper {
  margin-top: 25px;
}

.card {
  border: solid 1px map-get($colors, ETFLightBlue);
}

.holdingDetailRoot {
  padding: 15px;
}

.holdingDetail {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  padding-bottom: 15px;
  border-bottom: 1px solid map-get($colors, BlackOpacity1);
}

.label {
  @include fontStyle(12px, map-get($colors, DGREY5), normal);
  line-height: 1.5;
}

.value {
  @include fontStyle(12px, map-get($colors, Black5), bold);
  line-height: 1.5;
}

.holdingSection {
  border-bottom: 1px solid map-get($colors, BlackOpacity1); 
}

.holdingSection:last-child {
  border-bottom: 0;
}