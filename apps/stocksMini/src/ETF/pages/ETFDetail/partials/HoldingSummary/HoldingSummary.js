import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import React, { useState } from 'react';
import HoldingSummaryIcon from '@assets/etf/holding_summary.svg';
import Card from '@src/ETF/components/Card/Card';
import Tab from '@src/ETF/components/Tab/Tab';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import HoldingTracker from '@src/ETF/components/HoldingTracker/HoldingTracker';
import styles from './HoldingSummary.scss';

const HoldingSummary = () => {
  const [selectedTab, handleTab] = useState(
    ETF_DETAIL.HOLDING_SUMMARY.TAB_LIST[0].key,
  );

  const holdingTrackerData = [
    {
      label: 'Technology',
      value: 15.71,
    },
    {
      label: 'FMCG',
      value: 15.53,
    },
    {
      label: 'Financial',
      value: 13,
    },
    {
      label: 'Energy',
      value: '9.93',
    },
    {
      label: 'Construction',
      value: '9.34',
    },
  ];

  const onTabClick = value => {
    handleTab(value);
  };
  return (
    <div className={styles.wrapper}>
      <SectionHeader
        title={ETF_DETAIL.HOLDING_SUMMARY.TITLE}
        titleSize="small"
        icon={HoldingSummaryIcon}
      />
      <Card customClass={styles.card}>
        <Tab
          config={ETF_DETAIL.HOLDING_SUMMARY.TAB_LIST}
          activeTab={selectedTab}
          onTabClick={onTabClick}
        />
        <div className={styles.holdingDetailRoot}>
          <div className={styles.holdingDetail}>
            <div>
              <div className={styles.label}>
                {ETF_DETAIL.HOLDING_SUMMARY.FUND_SIZE_LABEL}
              </div>
              <span className={styles.value}>₹554.93</span>
            </div>
            <div>
              <div className={styles.label}>
                {ETF_DETAIL.HOLDING_SUMMARY.AS_ON_DATE_LABEL}
              </div>
              <span className={styles.value}>30 Nov 2021</span>
            </div>
          </div>
          {holdingTrackerData.map(v => (
            <div className={styles.holdingSection}>
              <HoldingTracker
                label={v.label}
                key={v.key}
                value={v.value}
                maxValue={Math.round(holdingTrackerData[0].value)}
                withSign
              />
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default HoldingSummary;
