@import 'src/commonStyles/commoncss';

.label {
  @include fontStyle(11px, map-get($colors, Black5), normal);
  line-height: 1.45;
  margin-bottom: 5px;
}

.value {
  @include fontStyle(13px, map-get($colors, BlackOpacity7), bold);
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.rowItems {
  min-width: 100px;
  height: 72px;
  padding: 10px;
  border-radius: 10px;
  background-color: map-get($colors, LightGrayishBg);
  margin: 8.5px 9px;
}

.wrapper {
  margin-top: 25px;
}
