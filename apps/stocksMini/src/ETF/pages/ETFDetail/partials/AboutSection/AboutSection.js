import React from 'react';
import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import Card from '@src/ETF/components/Card/Card';
import { ABOUT_SECTION } from '@src/ETF/components/enum';
import AboutEtfIcon from '@assets/etf/about_etf.svg';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import { roundValue } from '@src/utils/commonUtils';
import styles from './AboutSection.scss';

const AboutSection = ({ companyDetail }) => {
  const {
    latest_aum: aum,
    expense_ratio: expenseRatio,
    tracking_error: trackingError,
    latest_nav: nav,
    benchmark_name: benchmark,
  } = companyDetail;
  const AboutList = [
    {
      label: ABOUT_SECTION.FUND_SIZE,
      value: aum ? `${roundValue(aum)} cr` : '-',
    },
    {
      label: ABOUT_SECTION.TRACKING_ERROR,
      value: trackingError ? `${roundValue(trackingError)}%` : '-',
    },
    {
      label: ABOUT_SECTION.EXPENSE,
      value: expenseRatio ? `${roundValue(expenseRatio)}%` : '-',
    },
    {
      label: ABOUT_SECTION.NAV,
      value: nav ? roundValue(nav) : '-',
    },
    {
      label: ABOUT_SECTION.BENCHMARK,
      value: benchmark || '-',
    },
  ];

  return (
    <div className={styles.wrapper}>
      <SectionHeader
        title={ETF_DETAIL.ABOUT.TITLE}
        titleSize="small"
        icon={AboutEtfIcon}
      />
      <div className={styles.row}>
        {AboutList.map(list => (
          <Card customClass={styles.rowItems} key={list.label}>
            <div className={styles.label}>{list.label}</div>
            <div className={styles.value}>{list.value}</div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AboutSection;
