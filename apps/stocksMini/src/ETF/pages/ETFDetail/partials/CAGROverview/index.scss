.cagrContainer {
  border-radius: 10px;
  border: solid 1px map-get($colors, ETFBorder); 
  background-color: map-get($colors, PureWhite);
  display: flex;
  padding: 12.3px 15px 11.3px;
  justify-content: space-between;

  .cagr {
    display: flex;
    flex-direction: column;

    .label {
      line-height: 1.6;
      @include fontStyle(10px, map-get($colors, ETFGray5), bold);
    }

    .value {
      display: flex;
      column-gap: 5px;
      line-height: 1.5;
      align-items: center;
      @include fontStyle(14px, false, bold);

      &__profit {
        color: map-get($colors, Green5);
      }

      &__lose {
        color: map-get($colors, ETFRed);
      }
    }
  }

  .icons {
    display: flex;
    column-gap: 20px;
  }
}
