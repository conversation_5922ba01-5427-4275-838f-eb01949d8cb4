import React from 'react';
import cx from 'classnames';

import Icon, { ICONS_NAME } from '@src/components/Icon';
import { roundValue } from '@src/utils/commonUtils';
import Shimmer from '@src/components/Shimmer/Shimmer';
import styles from './index.scss';

const CAGROverview = ({ isLoading = true, cagrValue, cagrLabel }) => (
  <section className={styles.cagrContainer}>
    <div className={styles.cagr}>
      {isLoading ? (
        <Shimmer type="line" width="50px" height="12px" margin="0 0 4px 0" />
      ) : (
        <div className={styles.label}> {cagrLabel}</div>
      )}
      <div
        className={cx(
          styles.value,
          cagrValue > 0 ? styles.value__profit : styles.value__lose,
        )}
      >
        {isLoading ? (
          <Shimmer type="line" width="80px" height="21px" />
        ) : (
          <>
            <span>{`(${roundValue(cagrValue)}%)`}</span>
            <span>
              <Icon
                name={
                  cagrValue > 0
                    ? ICONS_NAME.ETF_PROFIT
                    : cagrValue < 0
                    ? ICONS_NAME.ETF_LOSE
                    : ''
                }
                width={12}
              />
            </span>
          </>
        )}
      </div>
    </div>

    <div className={styles.icons}>
      {isLoading ? (
        <Shimmer type="line" width="124px" height="44px" />
      ) : (
        <>
          <Icon
            name={
              cagrValue > 0
                ? ICONS_NAME.ETF_POSITIVE_GRAPH
                : ICONS_NAME.ETF_NEGATIVE_GRAPH
            }
            width={94}
          />
          <Icon name={ICONS_NAME.BLACK_ARROW_DOWN} width={10} />
        </>
      )}
    </div>
  </section>
);

export default CAGROverview;
