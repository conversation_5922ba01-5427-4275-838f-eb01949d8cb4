import React, { useEffect, useState } from 'react';
import classNames from 'classnames';

import CategoryEtfIcon from '@assets/etf/category_etf.svg';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import Chip from '@src/ETF/components/Chip/Chip';
import { useEtfData } from '@src/query/stocksQuery';
import { ETF_CATEGORY } from '@src/ETF/components/enum';
import { roundValue } from '@src/utils/commonUtils';
import { useCombineStockFeeds } from '@src/utils/Equities/hooks';
import Shimmer from '@src/components/Shimmer/Shimmer';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import { etfNavigateTo } from '@src/ETF/utils/utilFunctions';
import Icon, { ICONS_NAME } from '@src/components/Icon';
import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';
import styles from './ETFCategory.scss';

const ETFCategory = ({
  category,
  currentEtfOneYearReturn,
  onViewAllClick,
  currentId,
}) => {
  const [categoryArr, setCategoryArr] = useState([]);

  const veiwAllButton = {
    onButtonClick: onViewAllClick,
    buttonTitle: ETF_DETAIL.ETF_CATEGORY.VIEW_ALL,
  };

  const getQueryParams = pageNumber => {
    const queryParams = {
      i: 'ETF',
      p: pageNumber,
      ps: 3,
      sort: 'ONE_YEAR_RETURN',
      so: 'DESC',
      x: 'NSE',
    };
    queryParams.sc = category;

    return queryParams;
  };

  const { data: etfDataPages, isLoading } = useEtfData(
    null,
    category,
    getQueryParams,
    true,
  );

  const combinedStockFeeds = useCombineStockFeeds(
    etfDataPages?.pages[0].results || [],
  );

  const handleCardClick = id => {
    if (id === currentId) {
      window.scrollTo(0, 0);
    } else {
      etfNavigateTo({ base: `${ETF_ROUTES.DETAIL}?id=${id}` });
    }
  };

  useEffect(() => {
    if (!isLoading) {
      const dataArr = etfDataPages?.pages[0].results.filter(
        etf => etf.id !== currentId,
      );
      setCategoryArr(dataArr);
    }
  }, [isLoading, currentId]);

  return (
    <div className={styles.wrapper}>
      <SectionHeader
        icon={CategoryEtfIcon}
        title={ETF_DETAIL.ETF_CATEGORY.TITLE}
        titleSize="small"
        buttonProps={veiwAllButton}
      />
      <div className={styles.pillsWrapper}>
        <Chip className={styles.chip} onClickHandler={onViewAllClick}>
          {category}
        </Chip>
      </div>

      <div className={styles.cardWrapper}>
        {/* Return Overview Purple card */}
        <div className={styles.returnsOverview}>
          <div className={styles.left}>
            <div className={styles.label}>
              {ETF_CATEGORY.THIS_ETFS_RETURNS}{' '}
            </div>
            <div className={classNames(styles.value)}>
              <span
                className={
                  currentEtfOneYearReturn > 0 ? styles.up : styles.down
                }
              >
                {roundValue(currentEtfOneYearReturn)}%
              </span>
              <Icon
                name={
                  currentEtfOneYearReturn > 0
                    ? ICONS_NAME.ETF_RETURNS_UP
                    : currentEtfOneYearReturn < 0
                    ? ICONS_NAME.ETF_LOSE
                    : ''
                }
                width={12}
              />
            </div>
          </div>
          <div className={styles.right}>
            <Icon name={ICONS_NAME.ETF_CATEGORY_RETURNS_OVERVIEW} width={63} />
          </div>
        </div>
        {/* Cards */}
        {categoryArr.map((etf, index) => (
          <div key={etf.id}>
            <div
              className={styles.card}
              onClick={() => handleCardClick(etf.id)}
            >
              <div className={styles.top}>
                <div className={styles.name}>{etf.name}</div>
                <div>
                  <Icon name={ICONS_NAME.ARROW_CHEVRON_RIGHT} width={4.4} />
                </div>
              </div>
              <div className={styles.bottom}>
                <div className={styles.bottomLeft}>
                  <div className={styles.label}>{ETF_CATEGORY.MIN_INVEST}</div>
                  <div className={styles.value}>
                    {combinedStockFeeds?.[index]?.ltp ? (
                      `₹${roundValue(combinedStockFeeds?.[index]?.ltp)}`
                    ) : (
                      <Shimmer type="line" width="60px" height="20px" />
                    )}
                  </div>
                </div>
                <div>
                  <div className={styles.label}>
                    {ETF_CATEGORY.ONE_YEAR_RETURN}
                  </div>
                  <div
                    className={classNames(
                      styles.value,
                      etf.one_year_return > 0 ? styles.up : styles.down,
                    )}
                  >
                    {etf.one_year_return > 0 ? '+' : ''}
                    {etf.one_year_return}%
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.line} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ETFCategory;
