@import 'src/commonStyles/commoncss';

.wrapper {
  display: flex;
  flex-direction: column;
  margin: 25px 0 0;
}

.pillsWrapper {
  display: flex;
  margin: 1px 0 15px 34px;
}

.chip {
  height: 18px;
  display: flex;
  font-size: 10px;
  padding: 0 10px;
  font-weight: 500;
  line-height: 1.6;
  color: map-get($colors, BlackHalfOpacity);
  border-radius: 42px;
  border: solid 1px map-get($colors, ETFLightBlue);
  background-color: map-get($colors, LightGrayishBg);
  margin: 5px;
}

.cardWrapper {
  border-radius: 10px;
  width: 100%;
  border: solid 1px map-get($colors, BlackOpacity1);
  background-color: map-get($colors, PureWhite);
}

.returnsOverview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 93px;
  border-radius: 10px;
  margin-bottom: 20px;

  padding: 0 20px 0 15px;
  background-image: linear-gradient(to right, #29007a 0%, #6c4efe 100%),
    linear-gradient(76deg, #31007a -9%, #00acef 103%);

  .left {
    display: flex;
    flex-direction: column;

    .label {
      height: 18px;
      line-height: 1.5;
      @include fontStyle(12px, map-get($colors, PureWhite));
      padding-bottom: 2px;
    }

    .value {
      display: flex;

      .up {
        color: map-get($colors, ETFGreen2);
        padding-right: 5px;
      }

      .down {
        color: map-get($colors, ETFRed);
        padding-right: 5px;
      }

      span {
        display: flex;
        align-items: center;
        line-height: 1.5;
        @include fontStyle(14px, false, bold);
      }
    }
  }

  .right {
    width: 63px;
    height: 63px;
  }
}

.card {
  display: flex;
  flex-direction: column;
  padding: 0 15px;

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
  }

  .name {
    line-height: 1.43;
    @include fontStyle(14px, map-get($colors, Black5), 600);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .bottom {
    display: flex;

    .bottomLeft {
      margin-right: 94px;
    }

    .label {
      line-height: 1.75;
      @include fontStyle(12px, map-get($colors, BlackHalfOpacity));
    }

    .value {
      line-height: 1.5;
      @include fontStyle(14px, map-get($colors, BlackOpacity7), bold);
    }

    .up {
      color: map-get($colors, Green5);
    }

    .down {
      color: map-get($colors, ETFRed);
    }
  }
}

.line {
  height: 1px;
  flex-grow: 0;
  margin: 9.5px 15px 9.5px 15px;
  background-color: map-get($colors, BlackOpacity1);
}
