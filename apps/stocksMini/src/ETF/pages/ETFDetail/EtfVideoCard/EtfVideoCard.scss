@import 'src/commonStyles/commoncss';

.videoWrapper {
  position: relative;
  margin: 20px 0 25px 0;
}

.videoWrapperHome {
  position: relative;
  min-width: 264px;
  border-radius: 10px;
  margin: 0 0 25px 15px;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.1);
}

.thumbnail {
  padding: 0;
  background-color: transparent;

  > figure {
    height: 210px;
    width: 100%;
    border-radius: 20px;
  }
}

.thumbnailHome {
  padding: 0;
  background-color: transparent;

  > figure {
    height: 242px;
    width: 100%;
    border-radius: 10px 10px 0 0;
  }
}

.thumbnail > div {
  display: none;
}

.thumbnailHome > div {
  display: none;
}

.textContainer {
  position: absolute;
  bottom: -50px;
  width: 100%;
}

.title {
  @include fontStyle(16px, map-get($colors, PureWhite), normal);
  position: relative;
  padding: 0 15px;
  z-index: 2;
}

.note {
  @include fontStyle(10px, map-get($colors, Black5), normal);
  background-color: map-get($colors, LightYellowBg);
  border: 1px solid map-get($colors, LightYellowBorder);
  padding: 45px 15px 10px 15px;
  border-radius: 0 0 10px 10px;
}

.description {
  width: 100%;
  white-space: pre-line;
  padding: 0 15px;

  > p {
    @include fontStyle(16px, map-get($colors, Black5), 600);
  }
}
.zIndex15 {
  z-index: 15;
}
