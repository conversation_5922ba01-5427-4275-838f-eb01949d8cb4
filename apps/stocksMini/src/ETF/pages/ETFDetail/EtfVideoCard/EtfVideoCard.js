import React, { useEffect, useState } from 'react';

import cx from 'classnames';
import { BASE_URL } from '@config/envConfig';
import Thumbnail from '@src/components/VideoPlayers/partials/Thumbnail';
import YouTubePlayer from '@src/components/VideoPlayers';
import { useHomeConfig } from '@query/generalQuery';
import { ETF_DETAIL } from '@src/ETF/config/etfDetailConfig';
import styles from './EtfVideoCard.scss';

const EtfVideoCard = ({ data, etfHome = false }) => {
  const { data: dataHomeConfig } = useHomeConfig();
  /* TODO: useHomeConfig will be replaced with etf query */

  const [isPlaying, setIsPlaying] = useState(false);
  const [ytId, setYtId] = useState(null);

  const onVideoClose = () => {
    setIsPlaying(false);
  };

  useEffect(() => {
    if (etfHome) {
      setYtId(data.url.split('/').pop());
    } else {
      setYtId(dataHomeConfig?.etfDetailVideo.ytId);
    }
  }, [data, dataHomeConfig]);

  return (
    <div
      className={cx(etfHome ? styles.videoWrapperHome : styles.videoWrapper)}
    >
      {isPlaying ? (
        <YouTubePlayer
          key={ytId}
          videoId={ytId}
          onClose={onVideoClose}
          className={styles.zIndex15}
        />
      ) : (
        <div>
          {ytId && (
            <Thumbnail
              customThumbnail={
                etfHome
                  ? data.image_url
                  : `${BASE_URL.STATIC_HOME_FILE_CONFIG}${dataHomeConfig?.introVideo.thumbnail}.png`
              }
              thumbnail={ytId}
              className={etfHome ? styles.thumbnailHome : styles.thumbnail}
              ytId={ytId}
              noMargin
              onClick={setIsPlaying.bind(null, true)}
            />
          )}
          {!etfHome ? (
            <div className={styles.textContainer}>
              <div className={styles.title}>
                {dataHomeConfig?.etfDetailVideo.title}
              </div>
              <div className={styles.note}>{ETF_DETAIL.VIDEO_CARD.NOTE}</div>
            </div>
          ) : (
            <div className={styles.description}>
              <p>{data.title}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EtfVideoCard;
