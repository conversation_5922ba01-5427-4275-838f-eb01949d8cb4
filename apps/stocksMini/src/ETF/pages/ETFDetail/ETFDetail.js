import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import {
  calculateCagr,
  fetchPCloseData,
  getOverViewReturns,
  etfNavigateTo,
} from '@src/ETF/utils/utilFunctions';
import baseComponent from '@HOC/BaseComponent/BaseComponent';
import GenericErrorLayout from '@layout/GenericErrorLayout/GenericErrorLayout';
import Header from '@components/Header/Header';
import ETFFooter from '@src/ETF/components/ETFFooter/ETFFooter';
import { getQueryString, navigateHome } from '@src/utils/navigationUtil';
import {
  useCompanyDetails,
  useHoldingsData,
  // useUpcomingSipInstruction,
} from '@src/query/stocksQuery';
import { useUserReadiness } from '@src/query/generalQuery';
import { LISTING_DAY_TRADING_START_TIME } from '@src/config/common';
import PlaceOrder from '@src/pages/PlaceOrder';
import { useStockFeed } from '@src/utils/Equities/hooks';
import { ETF_DETAIL } from '@src/ETF/components/enum';
import { ETF_FILTER_PARAMS, tabs } from '@src/ETF/config/topETFConfig';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';

import ETFName from './partials/ETFName/ETFName';
import ReturnCalculator from './partials/ReturnCalculator/ReturnCalculator';
import AboutSection from './partials/AboutSection/AboutSection';
import Portfolio from './partials/PortFolio/PortFolio';
import styles from './ETFDetail.scss';
import ETFCategory from './partials/ETFCategory/ETFCategory';
import ETFChart from './partials/ETFChart';
// import ManageSip from './partials/ManageSip/ManageSip';
// import HoldingSummary from './partials/HoldingSummary/HoldingSummary';
import EtfVideoCard from './EtfVideoCard/EtfVideoCard';

const ETFDetail = () => {
  const { id: pmlId } = getQueryString(window.location.search);
  if (!pmlId) {
    navigateHome({});
    return null;
  }

  // states
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [stockData, setStockData] = useState(false);
  const [pclose, setpclose] = useState(null);
  const [currentEtfPrice, setCurrentEtfPrice] = useState(0);
  const [oneYearCagr, setoneYearCagr] = useState(0);
  const [cagrLabel, setCagrLabel] = useState('3Y CAGR');
  const [cagrValue, setCagrValue] = useState(0);
  const [isCagrLoading, setisCagrLoading] = useState(true);
  const [isPcloseLoading, setisPcloseLoading] = useState(true);

  // const [upcomingSip, setUpcomingSip] = useState([]);
  // hooks
  const { isLoading: isHoldingLoading, data: holdingData } = useHoldingsData();
  const { data: userBootData } = useUserReadiness();
  // get company details
  const {
    data: { data: companyData } = {},
    isLoading: isCompanyDetailLoading,
  } = useCompanyDetails(parseInt(pmlId, 10), true, true);
  const companyDetail = companyData?.results[0];
  // const { data: upcomingSipData } = useUpcomingSipInstruction();
  const { ltp } = useStockFeed({
    exchange: companyDetail?.exchange,
    securityId: companyDetail?.security_id,
  });

  const getHoldingData = async () => {
    if (userBootData.investmentStatus) {
      function filter(holdingArray) {
        if (holdingArray.length) {
          return holdingArray.filter(
            holding =>
              +holding.nse_pml_id === +pmlId || +holding.bse_pml_id === +pmlId,
          );
        }

        return [];
      }

      const data = filter(holdingData?.uniqueISINHoldingList);
      // eslint-disable-next-line radix
      if (data.length && parseInt(data[0].remaining_quantity)) {
        setStockData({
          quantity: data[0].remaining_quantity,
          cost_price: +data[0].cost_price,
        });
      } else {
        setStockData(null);
      }
    }
  };

  // effects
  useEffect(() => {
    if (pmlId && !isHoldingLoading && holdingData) {
      getHoldingData();
    }
  }, [pmlId, isHoldingLoading, holdingData]);

  // Temporarily comment use of SIP part
  // useEffect(() => {
  //   const sipDetail = upcomingSipData?.sip_detail;
  //   let currentEtfUpcomingSip = [];
  //   if (sipDetail && companyDetail) {
  //     for (const key of Object.keys(sipDetail)) {
  //       if (currentEtfUpcomingSip.length <= 2) {
  //         const filteredSip = sipDetail[key].filter(
  //           sip => sip.scrip_code === companyDetail?.security_id.toString(),
  //         );
  //         currentEtfUpcomingSip = [...currentEtfUpcomingSip, ...filteredSip];
  //       }
  //     }
  //   }
  //   setUpcomingSip(currentEtfUpcomingSip);
  // }, [upcomingSipData, companyDetail]);

  useEffect(() => {
    window.scrollTo(0, 0);
    setoneYearCagr(0);
    setpclose(null);
    setCurrentEtfPrice(0);

    setisCagrLoading(true);
    setisPcloseLoading(true);
    fetchPCloseData(pmlId).then(response => {
      setpclose(response);
      setisPcloseLoading(false);
    });
  }, [pmlId]);

  useEffect(() => {
    if (!isPcloseLoading && ltp) {
      const currentPrice = ltp;
      setCurrentEtfPrice(currentPrice);
      switch (pclose.length) {
        case 0: {
          getOverViewReturns(pmlId, currentPrice).then(response => {
            if (response) {
              setCagrLabel(response?.label);
              setCagrValue(response?.value);
              setisCagrLoading(false);
            }
          });
          break;
        }
        case 1: {
          const calculatedCagr = calculateCagr(
            pclose[0].pcloseValue,
            currentPrice,
            1,
          );
          setoneYearCagr(calculatedCagr);
          setCagrValue(calculatedCagr);
          setCagrLabel(ETF_DETAIL.ONE_YEAR_CAGR);
          setisCagrLoading(false);
          break;
        }

        case 2:
        case 3:
          setoneYearCagr(calculateCagr(pclose[0].pcloseValue, currentPrice, 1));
          setCagrValue(calculateCagr(pclose[1].pcloseValue, currentPrice, 3));
          setCagrLabel(ETF_DETAIL.THREE_YEAR_CAGR);
          setisCagrLoading(false);
          break;

        default:
          break;
      }
    }
  }, [isPcloseLoading, ltp, pclose]);

  // handlers
  const handleSearchView = value => {
    setIsSearchOpen(value);
  };

  const isTradingStarted = () => {
    const date = `${dayjs().year()}-${dayjs().month() + 1}-${dayjs().date()}`;
    return dayjs().isAfter(dayjs(`${date} ${LISTING_DAY_TRADING_START_TIME}`));
  };

  const onViewAllClick = () => {
    let topEtfTab = tabs[0];
    switch (companyDetail?.subcategory) {
      case ETF_FILTER_PARAMS.GLOBAL:
        topEtfTab = tabs[1];
        break;
      case ETF_FILTER_PARAMS.GOLD:
        topEtfTab = tabs[2];
        break;
      case ETF_FILTER_PARAMS.DEBT:
        topEtfTab = tabs[3];
        break;
      default:
        topEtfTab = tabs[0];
        break;
    }
    etfNavigateTo({ base: `${ETF_ROUTES.TOP_ETFS}?category=${topEtfTab}` });
  };

  return (
    <GenericErrorLayout>
      <Header
        showFav
        showSearch
        etf
        showShare
        searchState={{ isSearchOpen, handleSearchView }}
        companyDetails={companyDetail}
        customRootStyle={{
          [styles.isSearchOpen]: isSearchOpen,
        }}
      />
      <div className={styles.container}>
        <ETFName
          id={pmlId}
          name={companyDetail?.name}
          subCategory={companyDetail?.subcategory}
          onViewAllClick={onViewAllClick}
        />
        <ETFChart
          securityId={companyDetail?.security_id}
          exchange={companyDetail?.exchange}
          id={pmlId}
          name={companyDetail?.name}
          instrumentType={companyDetail?.instrument_type}
          segment={companyDetail?.segment}
          isCompanyDetailLoading={isCompanyDetailLoading}
          cagrLabel={cagrLabel}
          cagrValue={cagrValue}
          isCagrLoading={isCagrLoading}
        />

        <PlaceOrder {...companyDetail}>
          <ReturnCalculator
            currentEtfPrice={currentEtfPrice}
            pclose={pclose}
            id={pmlId}
            companyDetails={companyDetail}
          />
        </PlaceOrder>

        {companyDetail?.id && <AboutSection companyDetail={companyDetail} />}
        {stockData && companyDetail?.id && (
          <Portfolio
            costPrice={stockData.cost_price}
            quantity={stockData.quantity}
            securityId={companyDetail.security_id}
            exchange={companyDetail.exchange}
            segment={companyDetail.segment}
          />
        )}
        {companyDetail?.subcategory && (
          <ETFCategory
            category={companyDetail?.subcategory}
            currentEtfOneYearReturn={oneYearCagr}
            onViewAllClick={onViewAllClick}
            currentId={pmlId}
          />
        )}
        {/* {!!upcomingSip?.length && <ManageSip upcomingSip={upcomingSip} />} */}
        <EtfVideoCard />
        {/* <HoldingSummary /> */}
      </div>
      <div className={styles.footerBtns}>
        {!isCompanyDetailLoading &&
          !(companyDetail.is_listing_today && !isTradingStarted()) && (
            <PlaceOrder {...companyDetail}>
              <ETFFooter companyDetail={companyDetail} />
            </PlaceOrder>
          )}
      </div>
    </GenericErrorLayout>
  );
};

export default baseComponent(ETFDetail);
