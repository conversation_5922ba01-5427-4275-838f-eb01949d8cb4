import React, { useMemo, useState } from 'react';

import { useIRApi } from '@utils/IRutils';
import { useUserReadiness } from '@query/generalQuery';

import EducationalBanners from '@src/ETF/components/EducationalBanners/EducationalBanners';
import ETFComparison from '@src/ETF/components/ETFComparison/ETFComparison';
import FeaturedETF from '@src/ETF/components/FeaturedETF/FeaturedETF';
import KycCard from '@src/ETF/components/KycCard/KycCard';
import NewAndStories from '@src/ETF/components/NewAndStories/NewAndStories';
// import RecentETF from '@src/ETF/components/RecentETF/RecentETF';
import PromotionalBanner from '@src/ETF/components/PromotionalBanner/PromotionalBanner';
import TopETF from '@src/ETF/components/TopETF/TopETF';
// import EtfSipWrapper from '@src/ETF/components/EtfSipWrapper/EtfSipWrapper';
import { ETF_HEADER } from '@src/ETF/config/etfHomeConfig';
import VideoLearn from '@src/ETF/components/VideoLearn/VideoLearn';
import Header from '@components/Header/Header';
import { IR_STATUS_ENUM } from '@src/config/common';

import styles from './ETFHome.scss';

const ETFHome = () => {
  const { irData, isLoading: isIrLoading } = useIRApi();
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const irDone = useMemo(() => {
    if (irData) {
      return irData.irStatus === IR_STATUS_ENUM.ACTIVE;
    }

    return false;
  }, [irData]);

  const {
    isLoading: isUserReadinessDataLoading,
    data: userReadinessData,
  } = useUserReadiness(irDone);

  const investmentDone = useMemo(() => {
    if (userReadinessData) {
      return userReadinessData.investmentStatus;
    }

    return false;
  }, [userReadinessData]);

  // handlers
  const handleSearchView = value => {
    setIsSearchOpen(value);
  };

  if (isIrLoading && isUserReadinessDataLoading) return <></>;

  return (
    <div>
      <Header
        showSearch
        title={ETF_HEADER.HOME}
        customTitleStyle={styles.headerTitle}
        searchState={{ isSearchOpen, handleSearchView }}
        etf
      />
      <KycCard
        irDone={irDone}
        irData={irData}
        investmentDone={investmentDone}
      />
      {!investmentDone && <EducationalBanners />}
      {/* {irDone && <EtfSipWrapper />} */}
      <TopETF />
      <PromotionalBanner />
      <FeaturedETF />
      {/* can be enabled once script-list API provides sorting on basis of tracking_error 
      <RecentETF /> */}
      <VideoLearn />
      {!investmentDone && <ETFComparison />}
      <NewAndStories />
    </div>
  );
};

export default ETFHome;
