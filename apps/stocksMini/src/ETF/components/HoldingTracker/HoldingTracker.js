import React from 'react';
import styles from './HoldingTracker.scss';

const HoldingTracker = ({ label, value, maxValue, withSign }) => (
  <div className={styles.wrapper}>
    <div className={styles.container}>
      <div className={styles.label}>{label}</div>
      <div className={styles.progressBar} />
      <div
        className={styles.progressBarInner}
        style={{ width: `${(value / maxValue) * 100}%` }}
      />
    </div>
    <div className={styles.value}>
      {value}
      {withSign ? '%' : ''}
    </div>
  </div>
);

export default HoldingTracker;
