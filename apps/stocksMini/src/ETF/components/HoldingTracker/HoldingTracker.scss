.wrapper {
  display: flex;
  margin-top: 9.5px;
  margin-bottom: 9.5px;
}

.label {
  @include fontStyle(14px, map-get($colors, Black5), 600);
  line-height: 1.43;
}

.value {
  @include fontStyle(12px, map-get($colors, BlackOpacity7), bold);
  line-height: 1.43;
  text-align: end;
  flex: 1;
  margin-top: 10px;
}

.progressBar {
  width: 100%;
  opacity: 0.2;
  border-radius: 20px;
  background-color: map-get($colors, ETFBlue);
  height: 4px;
  margin-top: 10px;
}

.progressBarInner {
  height: 4px;
  background-color: map-get($colors, ETFBlue);
  margin-top: -4px;
  border-radius: 20px;
}

.container {
  flex: 3;
}
