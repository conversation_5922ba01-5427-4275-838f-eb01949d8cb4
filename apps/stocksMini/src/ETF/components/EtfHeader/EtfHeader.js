import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import history from '@src/history';

import styles from './EtfHeader.scss';
import BackIcon from '../../../assets/etf/arrow_back_black.svg';

const EtfHeader = ({
  text,
  hideShadow,
  removeMarginBottom,
  customBackPress,
}) => {
  const handleBackPress = () => {
    if (customBackPress !== null) {
      customBackPress();
    }
    history.goBack();
  };

  return (
    <div
      className={cx(styles.root, {
        [styles.hideShadow]: hideShadow,
        [styles.removeMarginBottom]: removeMarginBottom,
      })}
    >
      <div className={styles.imgContainer}>
        <img onClick={handleBackPress} src={BackIcon} alt="" />
      </div>
      {text && <div className={styles.header}>{text}</div>}
      <div className={styles.rightContainer} />
    </div>
  );
};

EtfHeader.propTypes = {
  text: PropTypes.string,
  hideShadow: PropTypes.bool,
  removeMarginBottom: PropTypes.bool,
  customBackPress: PropTypes.func,
};

EtfHeader.defaultProps = {
  text: null,
  hideShadow: false,
  removeMarginBottom: false,
  customBackPress: null,
};

export default EtfHeader;
