.root {
  padding: 17px 15px;
  display: flex;
  box-shadow: 0 2px 8px 0 rgba(1, 42, 114, 0.1);
  margin-bottom: 20px;
  z-index: 2;
  background-color: map-get($colors, PureWhite);
  position: sticky;
  top: -1px;
}

.hideShadow {
  box-shadow: unset;
}

.removeMarginBottom {
  margin-bottom: unset;
}

.header {
  @include ipoTypography(heading3B1);
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
  margin-left: auto;
}

.imgContainer {
  width: 20px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
}

.rightContainer {
  margin-left: auto;
  display: flex;

  > div {
    margin-left: 15px;
  }

  > div:first-child {
    margin-left: 0;
  }
}
