.tabContainer {
  .tabHeaderContainer {
    z-index: 999;
    background-color: #fff;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px 0 rgba(1, 42, 114, 0.1);

    .tab {
      position: relative;
      flex-grow: 1;
      font-size: 1.4rem;
      text-align: center;
      padding: 1.5rem;
      color: map-get($colors, BlackHalfOpacity);

      &.activeTabClass {
        font-weight: 700;
        color: #012a72;
      }

      &.activeTabClass::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0.4rem;
        background-color: map-get($colors, ETFBlue);
        border-radius: 5px 5px 0 0;
      }
    }
  }
}
