import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { isEmpty } from 'lodash';
import styles from './Tab.scss';

const Tab = ({
  children,
  customHeader,
  tabs,
  tabContainerCustomClass,
  tabHeaderContainerCustomClass,
  activeTab,
  tabOnClick,
}) => {
  const tabClickHandler = tab => {
    if (tab === activeTab) return;
    tabOnClick(tab);
  };

  return (
    <div className={cx(styles.tabContainer, tabContainerCustomClass)}>
      {!isEmpty(customHeader) ? (
        customHeader
      ) : (
        <div
          className={cx(
            styles.tabHeaderContainer,
            tabHeaderContainerCustomClass,
          )}
        >
          {tabs.map(tab => (
            <span
              className={cx(styles.tab, {
                [styles.activeTabClass]: tab === activeTab,
              })}
              key={tab}
              onClick={() => tab<PERSON>lickHandler(tab)}
            >
              {tab}
            </span>
          ))}
        </div>
      )}
      {children}
    </div>
  );
};

Tab.defaultProps = {
  tabContainerCustomClass: '',
  tabHeaderContainerCustomClass: '',
  customHeader: null,
  tabs: null,
};

Tab.propTypes = {
  children: PropTypes.element.isRequired,
  customHeader: PropTypes.element,
  tabContainerCustomClass: PropTypes.string,
  tabHeaderContainerCustomClass: PropTypes.string,
  tabs: PropTypes.arrayOf(PropTypes.string),
  activeTab: PropTypes.string.isRequired,
  tabOnClick: PropTypes.func.isRequired,
};

export default Tab;
