import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import styles from './Chip.scss';

const Chip = ({ children, customClass, onClickHandler, ...restProps }) => (
  <span
    onClick={onClickHandler}
    className={cx(styles.chip, customClass)}
    {...restProps}
  >
    {children}
  </span>
);

Chip.propTypes = {
  children: PropTypes.oneOfType([PropTypes.element, PropTypes.string])
    .isRequired,
  customClass: PropTypes.string,
};

Chip.defaultProps = {
  customClass: '',
};

export default Chip;
