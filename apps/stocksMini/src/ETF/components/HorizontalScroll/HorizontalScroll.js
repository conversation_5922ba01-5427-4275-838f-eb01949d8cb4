import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import styles from './HorizontalScroll.scss';

const HorizontalScroll = ({ customClass, children }) => (
  <div className={cx(styles.horizontalScrollContainer, customClass)}>
    {children}
  </div>
);

HorizontalScroll.propTypes = {
  customClass: PropTypes.string,
  children: PropTypes.node.isRequired,
};

HorizontalScroll.defaultProps = {
  customClass: '',
};

export default HorizontalScroll;
