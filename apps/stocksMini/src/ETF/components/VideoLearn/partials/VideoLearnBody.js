import React from 'react';

import EtfVideoCard from '@src/ETF/pages/ETFDetail/EtfVideoCard/EtfVideoCard';

import HorizontalScroll from '../../HorizontalScroll/HorizontalScroll';
import styles from './VideoLearnBody.scss';

const VideoLearnBody = ({ data }) => (
  <div className={styles.videoSection}>
    <HorizontalScroll>
      {data?.map((video, index) => (
        <EtfVideoCard key={video.id + index} data={video} etfHome />
      ))}
    </HorizontalScroll>
  </div>
);

export default VideoLearnBody;
