import React from 'react';
import { isEmpty } from 'lodash';

import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';

import VideoLearnHeader from '../SectionHeader/SectionHeader';
import videoLearnIcon from '../../../assets/etf/video_learn.svg';
import { ETF_HOME } from '../../config/etfHomeConfig';
import VideoLearnBody from './partials/VideoLearnBody';
import styles from './VideoLearn.scss';

const VideoLearn = () => {
  const { isLoading, data } = useEtfStoreFront();
  const videoLearnData = data?.[STORE_FRONT_IDS.VIDEO_LEARN_ID][0].items;
  if (isLoading || isEmpty(videoLearnData)) return <></>;
  return (
    <section className={styles.container}>
      <VideoLearnHeader
        icon={videoLearnIcon}
        title={ETF_HOME.VIDEO_LEARN.TITLE}
      />
      <VideoLearnBody data={videoLearnData} />
    </section>
  );
};

VideoLearn.propTypes = {};

export default VideoLearn;
