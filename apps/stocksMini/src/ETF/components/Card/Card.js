import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import styles from './Card.scss';

const Card = ({ children, customClass, ...restProps }) => (
  <div className={cx(styles.cardContainer, customClass)} {...restProps}>
    {children}
  </div>
);

Card.propTypes = {
  customClass: PropTypes.string,
  children: PropTypes.node.isRequired,
};

Card.defaultProps = {
  customClass: '',
};

export default Card;
