.root {
  border-radius: 10px;
  border: solid 1px rgba(16, 16, 16, 0.1);
  padding: 20px 20px 15px;
  margin: 15px 15px 0;
}

.container {
  display: flex;
}

.title {
  @include ipoTypography(heading3);
  margin-bottom: 5px;
}

.desc {
  @include ipoTypography(subTitle1);
  margin-bottom: 15px;
  opacity: 0.6;
}

.img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  padding-left: 5px;
}

.indicator {
  height: 2px;
  flex-grow: 0;
  margin-right: 5px;
  border-radius: 5px;
  background-color: map-get($colors, BlackOpacity1);
  width: 100%;
}

.indicator:last-child {
  margin-right: 0px;
}

.activeIndicator {
  display: flex;
  opacity: 0.5;
  border-radius: 20px;
  background-color: map-get($colors, BlackHalfOpacity);
}

.indicatorWrapper {
  margin-top: 10px;
  display: flex;
}
