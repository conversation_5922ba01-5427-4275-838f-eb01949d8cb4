import React, { useMemo, useState } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';

import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import CustomSwipeLayout from '@src/components/CustomSwipeLayout/CustomSwipeLayout';
import { SWIPE_MODE } from '@src/utils/Constants';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';

import styles from './EducationalBanners.scss';

const EducationalBanners = () => {
  const [currentIdx, setCurrentIdx] = useState(0);

  const { isLoading, data } = useEtfStoreFront();

  const educationalBanners = useMemo(() => {
    if (data) {
      return data?.[STORE_FRONT_IDS.EDUCATIONAL_BANNERS_ID][0].items || [];
    }

    return [];
  }, [data]);

  const handleSwipeEvent = event => {
    if (event === SWIPE_MODE.SWIPE_LEFT) {
      const idx = currentIdx + 1;

      if (idx < educationalBanners.length) {
        setCurrentIdx(idx);
      } else {
        setCurrentIdx(0);
      }
    } else if (event === SWIPE_MODE.SWIPE_RIGHT) {
      const idx = currentIdx - 1;

      if (idx > -1) {
        setCurrentIdx(idx);
      } else {
        setCurrentIdx(educationalBanners.length - 1);
      }
    }
  };

  if (isLoading || isEmpty(educationalBanners)) return <></>;

  return (
    <>
      {educationalBanners.length ? (
        <div className={styles.root}>
          <CustomSwipeLayout onSlideChange={handleSwipeEvent}>
            <div className={styles.container}>
              <div className={styles.content}>
                <div className={styles.title}>
                  {educationalBanners[currentIdx]?.title}
                </div>
                <div className={styles.desc}>
                  {educationalBanners[currentIdx]?.description}
                </div>
              </div>
              <img
                className={styles.img}
                src={educationalBanners[currentIdx]?.image_url}
                alt=""
              />
            </div>
          </CustomSwipeLayout>
          <div className={styles.indicatorWrapper}>
            {new Array(educationalBanners.length).fill(0).map((_, idx) => (
              <div
                key={idx}
                className={classNames(styles.indicator, {
                  [styles.activeIndicator]: idx === currentIdx,
                })}
              />
            ))}
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default EducationalBanners;
