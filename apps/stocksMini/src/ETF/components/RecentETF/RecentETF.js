import React from 'react';
import { useLowTrackingEtf } from '@src/ETF/query/etfQuery';

import RecentETFBody from './partials/RecentETFBody';
import RecentETFHeader from '../SectionHeader/SectionHeader';
import recentETFIcon from '../../../assets/etf/recent_etfs.svg';
import { ETF_HOME } from '../../config/etfHomeConfig';
// eslint-disable-next-line css-modules/no-unused-class
import styles from './RecentETF.scss';

const RecentETF = () => {
  const { isLoading, data } = useLowTrackingEtf();

  if (isLoading) return <></>;

  return (
    <section className={styles.container}>
      <RecentETFHeader
        icon={recentETFIcon}
        title={ETF_HOME.RECENT_ETFS.TITLE}
        description={ETF_HOME.RECENT_ETFS.DESC}
      />
      <RecentETFBody items={data?.results || []} />
    </section>
  );
};

RecentETF.propTypes = {};

export default RecentETF;
