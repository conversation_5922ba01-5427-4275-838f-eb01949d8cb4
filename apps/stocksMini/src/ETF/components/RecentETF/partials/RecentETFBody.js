import React from 'react';

import Icon, { LOGO_SIZE } from '@src/components/Icon';
import {
  amountToIndianFormat,
  etfNavigateTo,
} from '@src/ETF/utils/utilFunctions';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import Shimmer from '@src/components/Shimmer/Shimmer';

import HorizontalScroll from '../../HorizontalScroll/HorizontalScroll';
import Card from '../../Card/Card';
import Tag from '../../Chip/Chip';
import { RECENT_ETF_BODY } from '../../enum';
// eslint-disable-next-line css-modules/no-unused-class
import styles from '../RecentETF.scss';

const RecentETFBody = ({ items }) => {
  const getShimmerArray = noOfRows => {
    const shimmerArray = [];
    Array.from(Array(noOfRows).keys()).forEach(() => {
      shimmerArray.push({
        height: '120.66px',
        row: ['245px'],
        margin: '0 10px 0 0',
        className: styles.recentETFCard,
      });
    });
    return shimmerArray;
  };
  const renderTag = tag => (
    <Tag customClass={styles.recentETFCardTag}>{tag}</Tag>
  );

  const onCardClick = id => {
    etfNavigateTo({ base: `${ETF_ROUTES.DETAIL}?id=${id}` });
  };

  return (
    <HorizontalScroll>
      {items.length ? (
        items.map(({ id, name, p_close, subcategory, tag }) => (
          <Card
            onClick={() => onCardClick(id)}
            customClass={styles.recentETFCard}
            key={id}
          >
            <div className={styles.cardTop}>
              <div className={styles.cardImage}>
                <Icon
                  name={id}
                  companyName={name}
                  logoSize={LOGO_SIZE.MEDIUM}
                  className={styles.iconWrapper}
                />
              </div>
              <div className={styles.cardTopLabel}>{name}</div>
              {tag && renderTag(tag)}
            </div>
            <div className={styles.cardBottom}>
              <div className={styles.recentETFPrice}>
                <div>{RECENT_ETF_BODY.PRICE}</div>
                <div>{amountToIndianFormat(p_close)}</div>
              </div>
              <div className={styles.recentETFCategory}>
                <div>{RECENT_ETF_BODY.CATEGORY}</div>
                <div>{subcategory}</div>
              </div>
            </div>
          </Card>
        ))
      ) : (
        <Shimmer type="card" cardArray={getShimmerArray(4)} />
      )}
      {}
    </HorizontalScroll>
  );
};

export default RecentETFBody;
