.recentETFCard {
  font-size: 1.4rem;
  margin-right: 1rem;
  padding: 1.5rem 4rem 1.5rem 1.5rem;
  max-width: 25.2rem;
  border: 1px solid #e0f6fa;
  background-color: map-get($colors, ETFBlue2);

  .recentETFCardTag {
    align-self: center;
    background-color: map-get($colors, Orange5);
    color: map-get($colors, Orange3);
    margin: 0.2rem 0 0 0.5rem;
  }

  .cardTop,
  .cardBottom {
    display: flex;
    align-items: center;
  }

  .cardTop {
    font-weight: 600;
    margin-bottom: 1.5rem;

    .cardImage {
      margin-right: 0.5rem;

      .iconWrapper {
        width: 2.6rem;
        height: 2.6rem;
        border-radius: 50%;
        overflow: hidden;
        border: 1px solid map-get($colors, Grey17);

        img {
          height: 2.6rem;
          width: 2.6rem;
        }

        span {
          border: none;
          font-size: 2rem;
        }
      }
    }

    .cardTopLabel {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .cardBottom {
    .recentETFPrice {
      margin-right: 7.4rem;
    }

    .recentETFPrice,
    .recentETFCategory {
      div:first-child {
        @include typography(text, map-get($colors, DGREY5));
        font-size: 1.2rem;
        font-weight: 400;
        margin-bottom: 0.5rem;
      }
    }

    .recentETFCategory {
      div:last-child {
        font-weight: 700;
      }
    }
  }

  &:last-child {
    margin-right: 1.5rem;
  }
}