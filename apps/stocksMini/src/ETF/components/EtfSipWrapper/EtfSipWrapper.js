import React from 'react';

import { useCheckSip } from '@src/ETF/query/etfQuery';

import GetStartedWithSip from '../GetStartedWithSip/GetStartedWithSip';
import ManageSip from '../ManageSip/ManageSip';

const EtfSipWrapper = () => {
  const { isLoading, data } = useCheckSip();

  if (isLoading) return <></>;

  if (data.length === 0) return <GetStartedWithSip />;

  if (data.length) return <ManageSip />;

  return <></>;
};

export default EtfSipWrapper;
