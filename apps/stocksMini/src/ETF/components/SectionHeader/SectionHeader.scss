@import 'src/commonStyles/commoncss';

.headerContainer {
  .headerTop {
    display: flex;
    justify-content: space-between;
    padding-bottom: 5px;
    > div {
      display: flex;
    }
  }

  .title {
    margin-left: 1rem;
    font-size: 1.8rem;
    font-weight: 700;
  }

  .smalltitle {
    margin-left: 11px;
    font-size: 16px;
    font-weight: bold;
  }

  .icon {
    width: 24px;
    height: 24px;
  }

  .description {
    @include typography(body2, map-get($colors, BlackHalfOpacity));
  }

  .buttonText {
    @include typography(body2B, map-get($colors, ETFBlue));
  }

  .buttonContainer {
    height: 24px;
  }

  .whiteButton {
    background-color: map-get($colors, PureWhite);
    border-radius: 110px;
    width: 78px;
    height: 32px;
  }
}
