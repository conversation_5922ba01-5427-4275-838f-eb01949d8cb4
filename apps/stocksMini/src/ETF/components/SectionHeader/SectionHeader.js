import React from 'react';
import PropTypes from 'prop-types';
import Button from '../../../components/Button/Button';
import styles from './SectionHeader.scss';

const SectionHeader = ({
  icon,
  title,
  description,
  buttonProps,
  titleSize,
}) => (
  <div className={styles.headerContainer}>
    <div className={styles.headerTop}>
      <div>
        {icon && (
          <div className={styles.icon}>
            <img src={icon} alt="featured-etfs" />
          </div>
        )}
        <span
          className={titleSize === 'small' ? styles.smalltitle : styles.title}
        >
          {title}
        </span>
      </div>
      {buttonProps && (
        <Button
          onClickHandler={buttonProps.onButtonClick}
          buttonText={buttonProps.buttonTitle}
          className={styles.buttonContainer}
          buttonTextClassName={styles.buttonText}
          isTextOnly={!buttonProps.isWhiteButton}
          buttonClassName={
            buttonProps.isWhiteButton ? styles.whiteButton : false
          }
        />
      )}
    </div>
    <div className={styles.headerBottom}>
      {description && <span className={styles.description}>{description}</span>}
    </div>
  </div>
);

SectionHeader.propTypes = {
  icon: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
  buttonProps: PropTypes.shape({
    buttonTitle: PropTypes.string.isRequired,
    onButtonClick: PropTypes.func.isRequired,
    customButtonClass: PropTypes.string,
  }),
};

SectionHeader.defaultProps = {
  icon: null,
  description: '',
  buttonProps: null,
};

export default SectionHeader;
