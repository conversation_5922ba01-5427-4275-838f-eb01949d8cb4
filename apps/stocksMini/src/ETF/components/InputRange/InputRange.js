import React, { useEffect, useRef } from 'react';
import styles from './InputRange.scss';

const InputRange = ({ onChange, min, max, step, value }) => {
  const rangeSliderRef = useRef(null);
  useEffect(() => {
    const percentage = (value * 100) / max;
    rangeSliderRef.current.style.background = `linear-gradient(to right, #00b8f5 0%, #00b8f5 ${percentage}%, rgba(16, 16, 16, 0.1) ${percentage}%, rgba(16, 16, 16, 0.1) 100%)`;
  }, [value]);

  return (
    <div className={styles.wrapper}>
      <input
        ref={rangeSliderRef}
        type="range"
        name="vol"
        min={min}
        max={max}
        step={step}
        onChange={e => {
          onChange(e.target.value);
        }}
        value={value}
        className={styles.inputRange}
      />
    </div>
  );
};

export default InputRange;
