.inputRange {
  -webkit-appearance: none;
  width: 100%;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type='range'] {
    width: 100%;
    border-radius: 10px;
    background-color: rgba(16, 16, 16, 0.1);
  }

  input[type='range']::-webkit-slider-runnable-track {
    height: 3px;
    -webkit-appearance: none;
  }

  input[type='range']::-webkit-slider-thumb {
    width: 15px;
    -webkit-appearance: none;
    height: 15px;
    border-radius: 10px;
    margin: -6px 0 0 0;
    box-shadow: 0 2px 5px 0 rgba(16, 16, 16, 0.2);
    background: #fff;
  }
}
