import React, { useState, useMemo } from 'react';
import classNames from 'classnames';

import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { SWIPE_MODE } from '@src/utils/Constants';
import CustomSwipeLayout from '@src/components/CustomSwipeLayout/CustomSwipeLayout';
import { deepTypeHelper } from '@src/utils/navigationUtil';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';

import styles from './PromotionalBanner.scss';

const PromotionalBanner = () => {
  const promotionalBannerId = STORE_FRONT_IDS.PROMOTIONAL_BANNER_ID;
  const [currentIdx, setCurrentIdx] = useState(0);

  const { isLoading, data } = useEtfStoreFront();

  const swiperLength = useMemo(
    () => data?.[promotionalBannerId][0].items.length || 0,
    [data],
  );

  const handleCardClick = () => {
    const { url_type: urlType, url } = data?.[promotionalBannerId][0].items[
      currentIdx
    ];
    deepTypeHelper(urlType, url);
  };

  const handleSwipeEvent = event => {
    if (event === SWIPE_MODE.SWIPE_LEFT) {
      const idx = currentIdx + 1;

      if (idx < swiperLength) {
        setCurrentIdx(idx);
      } else {
        setCurrentIdx(0);
      }
    } else if (event === SWIPE_MODE.SWIPE_RIGHT) {
      const idx = currentIdx - 1;

      if (idx > -1) {
        setCurrentIdx(idx);
      } else {
        setCurrentIdx(swiperLength - 1);
      }
    }
  };

  if (isLoading) return <></>;

  return (
    <>
      {swiperLength ? (
        <div className={styles.root}>
          <CustomSwipeLayout onSlideChange={handleSwipeEvent}>
            <img
              src={data?.[promotionalBannerId][0].items[currentIdx]?.image_url}
              alt=""
              onClick={handleCardClick}
            />
          </CustomSwipeLayout>
          <div className={styles.indicatorWrapper}>
            {new Array(swiperLength).fill(0).map((_, idx) => (
              <div
                key={idx}
                className={classNames(styles.indicator, {
                  [styles.activeIndicator]: idx === currentIdx,
                })}
              />
            ))}
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default PromotionalBanner;
