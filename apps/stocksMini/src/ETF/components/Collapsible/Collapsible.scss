.collapsibleContainer {
  .collapsibleContent {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .shouldExpand {
    overflow: initial;
    text-overflow: initial;
    display: initial;
    -webkit-box-orient: initial;
  }

  .viewButton {
    display: flex;
    align-items: center;
    background-color: inherit;
    font-size: 1rem;
    font-weight: 600;
    bottom: -2rem;
    left: 0;
    padding: 0.3rem 0;
    margin-top: 1rem;
    color: map-get($colors, Black5);

    > div:first-child {
      margin-right: 0.5rem;
    }

    > div:nth-child(2) {
      display: grid;
      place-content: center;
    }
  }
}
