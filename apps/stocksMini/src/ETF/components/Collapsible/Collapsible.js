import React, { useEffect, useState } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import downArrow1 from '@src/assets/etf/arrow_down1.svg';
import upArrow1 from '@src/assets/etf/arrow_up1.svg';

import styles from './Collapsible.scss';
import Button from '../Button/Button';
import { COLLAPSIBLE_STRINGS } from './enum';

/**
 * Note: This is only supported in webkit
 *
 * Todo: Add a fallback
 */
const Collapsible = ({ children, customClass, lineClamp, ...restProps }) => {
  const [viewMore, setViewMore] = useState(false);
  const [isTextClamped, setIsTextClamped] = useState(false);

  const viewButtonClickHandler = () => {
    setViewMore(prevState => !prevState);
  };

  useEffect(() => {
    const textElement = document.getElementById('collapsibleText');
    setIsTextClamped(textElement.scrollHeight > textElement.clientHeight);
  }, [children]);

  const getCurrentStyle = value => (viewMore ? 'initial' : value);
  return (
    <div
      className={cx(styles.collapsibleContainer, customClass)}
      {...restProps}
    >
      <div
        style={{
          WebkitLineClamp: getCurrentStyle(lineClamp),
        }}
        className={cx(styles.collapsibleContent, {
          [styles.shouldExpand]: viewMore,
        })}
        id="collapsibleText"
      >
        {children}
      </div>
      {isTextClamped && (
        <Button
          customClass={styles.viewButton}
          onClick={viewButtonClickHandler}
        >
          {viewMore ? (
            <>
              <div>{COLLAPSIBLE_STRINGS.VIEW_LESS}</div>
              <div>
                <img src={upArrow1} alt="arrow-down" />
              </div>
            </>
          ) : (
            <>
              <div>{COLLAPSIBLE_STRINGS.VIEW_MORE}</div>
              <div>
                <img src={downArrow1} alt="arrow-up" />
              </div>
            </>
          )}
        </Button>
      )}
    </div>
  );
};

Collapsible.defaultProps = {
  customClass: '',
  lineClamp: 2,
};

Collapsible.propTypes = {
  children: PropTypes.oneOfType([PropTypes.element, PropTypes.string])
    .isRequired,
  customClass: PropTypes.string,
  lineClamp: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default Collapsible;
