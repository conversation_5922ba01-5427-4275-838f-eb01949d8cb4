import { ETF_HOME } from '@src/ETF/config/etfHomeConfig';
import React from 'react';
import { isEmpty } from 'lodash';

import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';
import SectionHeader from '@src/ETF/components/SectionHeader/SectionHeader';

import newsIcon from '@src/assets/etf/news_icon.svg';

import styles from './NewAndStories.scss';
import Block from './partial/Block/Block';

const NewAndStories = () => {
  const { isLoading, data } = useEtfStoreFront();
  const newsAndStories = data?.[STORE_FRONT_IDS.NEWS_AND_STORIES_ID][0].items;
  if (isLoading || isEmpty(newsAndStories)) return <></>;

  return (
    <div className={styles.root}>
      <SectionHeader icon={newsIcon} title={ETF_HOME.NEWS_AND_STORIES.TITLE} />
      <div className={styles.blockContainer}>
        {newsAndStories.map((blog, index) => (
          <Block data={blog} key={`NewAndStories-${index}`} />
        ))}
      </div>
    </div>
  );
};

export default NewAndStories;
