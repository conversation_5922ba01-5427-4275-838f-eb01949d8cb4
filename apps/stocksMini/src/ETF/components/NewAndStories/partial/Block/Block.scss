.root {
  border-radius: 10px;
  border: solid 1px map-get($colors, BlackOpacity1);
}

.container {
  padding: 15px 10px;
}

.thumbnail {
  width: 264px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.title {
  @include ipoTypography(heading4B1);
  margin-bottom: 10px;
}

// .detail {
//   display: flex;
//   margin-bottom: 15px;
// }

// .text {
//   @include ipoTypography(subTitle2);
//   color: map-get($colors, BlackHalfOpacity);
// }

// .dot {
//   width: 4px;
//   height: 4px;
//   flex-grow: 0;
//   margin: 5px 5px 3px;
//   background-color: map-get($colors, ETFGray2);
//   border-radius: 2px;
// }

// .footer {
//   margin-bottom: 15px;
//   display: flex;

//   > div:last-child {
//     margin-left: auto;
//   }
// }

// .footerContent {
//   @include ipoTypography(subTitle2);
//   display: flex;
//   align-items: center;

//   > img {
//     height: 15px;
//     margin-right: 6px;
//   }

//   > div {
//     line-height: 2;
//   }
// }
