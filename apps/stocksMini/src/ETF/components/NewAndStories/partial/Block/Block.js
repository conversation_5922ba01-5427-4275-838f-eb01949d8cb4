import React from 'react';

import { deepTypeHelper } from '@src/utils/navigationUtil';

// import Share from '../../../../../assets/etf/share.svg';
// import Comment from '../../../../../assets/etf/speech_bubble.svg';

import styles from './Block.scss';

const Block = ({ data }) => {
  const handleCardClick = () => {
    deepTypeHelper(data.url_type, data.url);
  };
  return (
    <div className={styles.root} onClick={() => handleCardClick(data.url)}>
      <img className={styles.thumbnail} src={data.image_url} alt="" />
      <div className={styles.container}>
        <div className={styles.title}>{data.title}</div>
        {/* <div className={styles.detail}>
      <div className={styles.text}>March 17, 2021</div>
      <div className={styles.dot} />
      <div className={styles.text}>Personal Finace</div>
    </div> */}
        {/* temporarily comment out share and comment icon */}
        {/* <div className={styles.footer}>
  <div className={styles.footerContent}>
    <img src={Comment} alt="" />
    <div>Comment</div>
  </div>
  <div className={styles.footerContent}>
    <img src={Share} alt="" />
    <div>Share</div>
  </div>
</div> */}
      </div>
    </div>
  );
};

export default Block;
