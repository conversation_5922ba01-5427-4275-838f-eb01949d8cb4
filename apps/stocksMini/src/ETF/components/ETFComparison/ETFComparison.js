import React from 'react';
import { isEmpty } from 'lodash';

import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { deepTypeHelper } from '@src/utils/navigationUtil';

import ETFComparisonIcon from '../../../assets/etf/etf_comparison.svg';
import ETFComparisonHeader from '../SectionHeader/SectionHeader';
import { ETF_HOME } from '../../config/etfHomeConfig';
import styles from './ETFComparison.scss';

const ETFComparison = () => {
  const etfComparisonId = '318366';

  const { isLoading, data } = useEtfStoreFront();
  const comparisonData = data?.[etfComparisonId][0]?.items[0];

  if (isLoading || isEmpty(comparisonData)) return <></>;

  const { image_url, url, url_type } = comparisonData;

  return (
    <>
      {image_url && (
        <section className={styles.container}>
          <ETFComparisonHeader
            icon={ETFComparisonIcon}
            title={ETF_HOME.ETF_COMPARISON.TITLE}
          />
          <div
            className={styles.featuredETFBody}
            onClick={() => deepTypeHelper(url_type, url)}
          >
            <img src={image_url} alt="" />
          </div>
        </section>
      )}
    </>
  );
};

export default ETFComparison;
