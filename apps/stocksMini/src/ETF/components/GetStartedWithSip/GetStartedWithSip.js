import React, { useState } from 'react';
// import PropTypes from 'prop-types';

import InputBox from '@src/components/InputBox/InputBox';
import getStartedWithSip from '@src/assets/etf/get_started_with_sip.svg';

import styles from './GetStartedWithSip.scss';
import Card from '../Card/Card';
import Button from '../Button/Button';
import { ETF_HOME } from '../../config/etfHomeConfig';

const INITIAL_INPUT_VALUE = 5000;

const GetStartedWithSip = () => {
  const [sipAmount, setSipAmount] = useState(INITIAL_INPUT_VALUE);

  const onStartSipButtonClick = () => {};

  const onSipAmountChange = event => {
    const {
      target: { value },
    } = event;

    setSipAmount(value);
  };

  const incrementSipAmount = amount =>
    setSipAmount(prevAmount => prevAmount + amount);

  return (
    <Card customClass={styles.getStartedWithSipCardContainer}>
      <div className={styles.header}>
        <div>
          <div className={styles.title}>
            {ETF_HOME.GET_STARTED_WITH_SIP.TITLE}
          </div>
          <div className={styles.desc}>
            {ETF_HOME.GET_STARTED_WITH_SIP.DESC}
          </div>
        </div>
        <div>
          <img src={getStartedWithSip} alt="get-started-with-sip-icon" />
        </div>
      </div>
      <div className={styles.inputContainer}>
        <InputBox
          type="number"
          value={sipAmount}
          onChangeHandler={onSipAmountChange}
        />
      </div>
      <div className={styles.amountContainer}>
        {ETF_HOME.GET_STARTED_WITH_SIP.DATA.SIP_AMOUNT.map(({ amount, id }) => (
          <Button
            key={id}
            onClick={() => incrementSipAmount(amount)}
            customClass={styles.amount}
          >{`+${amount}`}</Button>
        ))}
      </div>
      <div>
        <Button
          type="cyan"
          customClass={styles.startSipButton}
          onClick={onStartSipButtonClick}
        >
          {ETF_HOME.GET_STARTED_WITH_SIP.CTA}
        </Button>
      </div>
    </Card>
  );
};

GetStartedWithSip.propTypes = {};

export default GetStartedWithSip;
