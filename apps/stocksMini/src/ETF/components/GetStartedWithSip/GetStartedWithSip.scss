.getStartedWithSipCardContainer {
  font-family: Inter;
  margin: 25px 15px;
  padding: 15px;
  border-radius: 10px;
  border: solid 1px rgba(0, 184, 245, 0.2);
  background-color: map-get($colors, LightGrayishBg);

  .header {
    display: flex;

    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 5px;
    }

    .desc {
      @include ipoTypography(subTitle1, map-get($colors, BlackHalfOpacity));
      margin-right: 20px;
      line-height: 1.5;
    }
  }

  .inputContainer {
    position: relative;
    margin-bottom: 15px;

    input {
      padding-left: 17px;
      color: #101010;
      font-family: 'Inter';
      font-size: 28px;
      border-bottom: 1px solid rgba(16, 16, 16, 0.1);
    }

    &::before {
      position: absolute;
      top: -1.5px;
      left: 0;
      content: '₹';
      color: #101010;
      font-size: 28px;
    }
  }

  .amountContainer {
    margin-bottom: 15px;

    .amount {
      font-family: 'Inter';
      font-weight: 600;
      padding: 6px 12px;
      background-color: inherit;
      border-radius: 100px;
      border: solid 1px rgba(16, 16, 16, 0.1);

      &:not(:last-child) {
        margin-right: 10px;
      }
    }
  }

  .startSipButton {
    background-color: map-get($colors, ETFBlue);
    font-weight: 700;
    width: 100%;
    padding-block: 12px;
  }
}
