import React from 'react';
import { isEmpty } from 'lodash';

import { etfNavigateTo } from '@src/ETF/utils/utilFunctions';
import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';

import FeaturedETFSectionHeader from '../SectionHeader/SectionHeader';
import FeaturedETFBody from './partials/FeaturedETFBody';
import styles from './FeaturedETF.scss';
import featuredETFIcon from '../../../assets/etf/featured_etfs.svg';
import { ETF_HOME } from '../../config/etfHomeConfig';
import { ETF_ROUTES } from '../../config/urlConfig';

const FeaturedETF = () => {
  const { isLoading, data } = useEtfStoreFront();

  const featuredETFButtonProps = {
    buttonTitle: 'View All',
    onButtonClick: () => etfNavigateTo({ base: ETF_ROUTES.FEATURED_ETFS }),
    isWhiteButton: true,
  };
  const featuredItems = data?.[STORE_FRONT_IDS.FEATURED_ETF_ID][0]?.items;
  if (isLoading || isEmpty(featuredItems)) return <></>;

  return (
    <section className={styles.container}>
      <FeaturedETFSectionHeader
        icon={featuredETFIcon}
        title={ETF_HOME.FEATURED_ETFS.TITLE}
        description={ETF_HOME.FEATURED_ETFS.DESC}
        buttonProps={featuredETFButtonProps}
      />
      <FeaturedETFBody items={featuredItems} />
    </section>
  );
};

FeaturedETF.propTypes = {};

export default FeaturedETF;
