.featuredETFBody {
  margin-top: 1.5rem;
  display: grid;
  margin: 1.5rem -0.5rem 0 -0.5rem;
  grid-template-columns: repeat(3, 1fr);
}

.featuredETFCard {
  padding: 0 1rem;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  font-size: 1.2rem;
  font-weight: 700;
  box-shadow: 0 2px 6px 0 rgba(108, 78, 254, 0.1);
  margin: 5px;

  div {
    width: 100%;
    img {
      max-width: 100%;
    }
  }

  .cardImageContainer {
    flex-grow: 1;
    display: grid;
    place-content: center;
    padding: 5px 5px 0;
  }

  .featuredETFCardLabel {
    display: grid;
    place-content: center;
    height: 50px;
  }
}
