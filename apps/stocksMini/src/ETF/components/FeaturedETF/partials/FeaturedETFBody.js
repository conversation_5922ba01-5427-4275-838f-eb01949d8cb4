import React from 'react';

import {
  etfNavigateTo,
  normalizeTextToURL,
} from '@src/ETF/utils/utilFunctions';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import Card from '../../Card/Card';
import styles from './FeaturedETFBody.scss';

const FeaturedETFBody = ({ items }) => {
  const onCardClick = id => {
    etfNavigateTo({
      base: `${ETF_ROUTES.FEATURED_ETFS}?category=${normalizeTextToURL(id)}`,
    });
  };

  return (
    <div className={styles.featuredETFBody}>
      {items.slice(0, 6).map(({ id, secondary_image, name }, idx) => (
        <Card
          onClick={() => onCardClick(name)}
          customClass={styles.featuredETFCard}
          key={id + idx}
        >
          <div className={styles.cardImageContainer}>
            <img src={secondary_image} alt={name} />
          </div>
          <div className={styles.featuredETFCardLabel}>{name}</div>
        </Card>
      ))}
    </div>
  );
};

export default FeaturedETFBody;
