import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';

import styles from './Button.scss';

const Button = ({
  children,
  type,
  isFooter,
  height,
  width,
  customClass,
  ...props
}) => {
  const btnClass = classnames(styles.root, customClass, {
    [styles.cyan]: type === 'cyan',
    [styles.green]: type === 'green',
    [styles.white]: type === 'white',
    [styles.whiteCyan]: type === 'whiteCyan',
    [styles.redWhite]: type === 'redWhite',
    [styles.blue]: type === 'blue',
    [styles.etfFooter]: isFooter,
    [styles.large]: height === 'large',
    [styles.fullWidth]: width === 'full',
    [styles.whiteBlue]: type === 'whiteBlue',
  });

  return (
    <button className={btnClass} {...props}>
      {children}
    </button>
  );
};

Button.defaultProps = {
  customClass: '',
  type: 'white',
  isFooter: false,
  height: null,
  width: null,
};

Button.propTypes = {
  type: PropTypes.oneOfType([
    'cyan',
    'green',
    'white',
    'whiteCyan',
    'redWhite',
    'blue',
  ]),
  isFooter: PropTypes.bool,
  height: PropTypes.oneOfType([null, 'large']),
  width: PropTypes.oneOfType([null, 'full']),
  customClass: PropTypes.string,
};

export default Button;
