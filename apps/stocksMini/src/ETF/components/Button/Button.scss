.root {
  cursor: pointer;
  padding: 8px 15px;
  border-radius: 24px;
  border: none;
  @include ipoTypography(subTitle1);
}

.cyan {
  background-color: map-get($colors, ETFBlue2);
  color: map-get($colors, PureWhite);
}

.green {
  background-color: map-get($colors, Green5);
  color: map-get($colors, PureWhite);
}

.white {
  background-color: map-get($colors, PureWhite);
  color: map-get($colors, BlackHalfOpacity);
}

.whiteCyan {
  background-color: map-get($colors, PureWhite);
  color: map-get($colors, ETFBlue);
}

.redWhite {
  background-color: map-get($colors, ETFRed);
  color: map-get($colors, PureWhite);
}

.blue {
  background-color: map-get($colors, ETFBlue);
  color: map-get($colors, PureWhite);
}

.large {
  height: 40px;
}

.fullWidth {
  width: 100%;
}

.etfFooter {
  width: 126px;
  height: 48px;
  @include typography(heading4B1);
}

.whiteBlue {
  background-color: map-get($colors, PureWhite);
  color: map-get($colors, ETFBlue3);
}
