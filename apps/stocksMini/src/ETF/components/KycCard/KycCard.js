import React, { useEffect, useMemo, useState } from 'react';
import { isEmpty } from 'lodash';
import { ETF_HOME, KYC_CARD_STATUS } from '@src/ETF/config/etfHomeConfig';

// import { useIRApi } from '@utils/IRutils';
import history from '@src/history';
import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { useStockFeed } from '@src/utils/Equities/hooks';
import { ChangeWithPercent } from '@src/components/Prices';
import { etfNavigateTo, fetchPCloseData } from '@src/ETF/utils/utilFunctions';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import { MINI_APP_ROUTES } from '@config/urlConfig';
import { STATUS } from '@src/config/irConfig';
import { STOCK_CARD } from '@src/pages/MyStocks/config/enums';
import Icon, { ICONS_NAME } from '@src/components/Icon';
import { navigateTo } from '@src/services/coreUtil';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';
import IndianNumberingSystem from '@src/components/IndianNumberingSystem/IndianNumberingSystem';
import Shimmer from '@src/components/Shimmer/Shimmer';

import KycLogo from '@src/assets/etf/etf_kyc_card.svg';
import GreenTick from '@src/assets/etf/green_tick.svg';
import PendingIcon from '@src/assets/etf/etf_kyc_submitted.svg';
import arrowRight from '@src/assets/etf/arrow_right_white.png';

import Button from '../Button/Button';
import styles from './KycCard.scss';

const KycCard = ({ irDone, irData /* , investmentDone */ }) => {
  const [descText, setDescText] = useState('');
  const {
    data: storefrontData,
    isLoading: isStorefrontLoading,
  } = useEtfStoreFront();

  const kycCardDetails =
    storefrontData?.[STORE_FRONT_IDS.KYC_CARD_ID][0].items[0];

  const { ltp } = useStockFeed({
    exchange: kycCardDetails?.exchange,
    securityId: parseInt(kycCardDetails?.security_id, 10),
  });

  // const { data: portfolioData } = useCombinedPortfolio();
  const portfolioData = {};

  useEffect(() => {
    if (storefrontData && ltp) {
      fetchPCloseData(kycCardDetails.pml_id).then(response => {
        const pClose = response[2].pcloseValue;
        const data = (100000 / pClose) * ltp;
        setDescText(`${data.toFixed(2)}.`);
      });
    }
  }, [storefrontData, ltp]);

  const cardType = useMemo(() => {
    if (irDone) {
      // if (!investmentDone) {
      return KYC_CARD_STATUS.NOT_INVESTED;
      // }
      // return KYC_CARD_STATUS.INVESTED;
    }
    const pendingStatusArr = [
      STATUS.INVERIFICATION,
      STATUS.SUBMITTED,
      STATUS.VERIFIED,
    ];
    const buckets = irData?.buckets;
    if (
      pendingStatusArr.includes(buckets?.[buckets?.order[0]]?.status) &&
      pendingStatusArr.includes(buckets?.[buckets?.order[1]]?.status) &&
      pendingStatusArr.includes(buckets?.[buckets?.order[2]]?.status) &&
      pendingStatusArr.includes(buckets?.[buckets?.order[3]]?.status)
    ) {
      return KYC_CARD_STATUS.PENDING_IR; // Pending with Backend. User Actions completed.
    }
    return KYC_CARD_STATUS.NON_IR; // Either not started KYC or user actions pending in KYC
  }, [irData]);

  const cardClicked = () => {
    if (cardType === KYC_CARD_STATUS.INVESTED) {
      navigateTo(history, MINI_APP_ROUTES.MY_STOCKS);
    }
  };

  const ctaHandler = () => {
    switch (cardType) {
      case KYC_CARD_STATUS.INVESTED:
        break;
      case KYC_CARD_STATUS.NOT_INVESTED:
        etfNavigateTo({
          base: `${ETF_ROUTES.DETAIL}?id=${kycCardDetails.pml_id}`,
        });
        break;
      case KYC_CARD_STATUS.PENDING_IR:
        navigateTo(history, MINI_APP_ROUTES.EQUITY);
        break;
      case KYC_CARD_STATUS.NON_IR:
        navigateTo(history, MINI_APP_ROUTES.CHECK_KYC);
        break;
      default:
        break;
    }
  };

  if (isStorefrontLoading || isEmpty(kycCardDetails)) return <></>;

  return (
    <div className={styles.root} onClick={cardClicked}>
      <div className={styles.container}>
        <div className={styles.currentWrapper}>
          {cardType === KYC_CARD_STATUS.INVESTED ? (
            <>
              <div className={styles.currentValueHeading}>
                {STOCK_CARD.CURRENT_VALUE}
              </div>
              <IndianNumberingSystem
                className={styles.currentValue}
                number={portfolioData?.stocks?.totalCurrentValue}
              />
              <div className={styles.return}>{STOCK_CARD.RETURNS}</div>
              <div className={styles.returnValue}>
                <ChangeWithPercent
                  value={portfolioData?.stocks?.totalReturns}
                  changeWithSign
                  withRupee
                  percent={portfolioData?.stocks?.totalReturnsPercentage}
                  className={styles.returnText}
                />
                <Icon
                  name={
                    portfolioData?.stocks?.totalReturns > 0
                      ? ICONS_NAME.ETF_PROFIT
                      : portfolioData?.stocks?.totalReturns < 0
                      ? ICONS_NAME.ETF_LOSE
                      : ''
                  }
                  width={12}
                  iconStyles={styles.arrow}
                />
              </div>
            </>
          ) : (
            <>
              <div className={styles.title}>Invest in ETFs</div>
              <div className={styles.desc}>
                {kycCardDetails?.description}
                <span className={styles.descReturn}>
                  {!descText ? (
                    <Shimmer type="line" width="50px" height="12px" />
                  ) : (
                    descText
                  )}
                </span>
              </div>
              <Button
                type={ETF_HOME.KYC_CARD[cardType].BUTTON_TYPE}
                onClick={ctaHandler}
              >
                {ETF_HOME.KYC_CARD[cardType].CTA}
              </Button>
            </>
          )}
        </div>
        <img className={styles.img} src={KycLogo} alt="" />
      </div>
      <div className={styles.divider} />
      <div
        className={styles.footer}
        style={
          cardType === KYC_CARD_STATUS.INVESTED
            ? { justifyContent: 'space-between' }
            : {}
        }
      >
        {cardType === KYC_CARD_STATUS.NOT_INVESTED && (
          <img className={styles.footerIcon} src={GreenTick} alt="" />
        )}
        {cardType === KYC_CARD_STATUS.PENDING_IR && (
          <img className={styles.footerIcon} src={PendingIcon} alt="" />
        )}
        <div>{ETF_HOME.KYC_CARD[cardType].FOOTER}</div>
        {cardType === KYC_CARD_STATUS.INVESTED && (
          <div>
            Top-Up Investments
            <img className={styles.arrowRight} src={arrowRight} alt="" />
          </div>
        )}
      </div>
    </div>
  );
};

export default KycCard;
