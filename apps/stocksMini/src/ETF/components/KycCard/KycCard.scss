
.root {
  border-radius: 10px;
  background: linear-gradient(to right, #29007a 0%, #6c4efe 100%), linear-gradient(62deg, #31007a -10%, #00acef 100%);
  margin: 20px 15px 0px;
}

.container {
  display: flex;
  justify-content: space-between;
  padding: 20px 15px;
}

.title {
  @include ipoTypography(heading3B, map-get($colors, PureWhite));
  margin-bottom: 5px;
}

.desc {
  @include ipoTypography(subTitle1, map-get($colors, BlackOpacity6));
  margin-bottom: 15px;
  margin-right: 10px;
  opacity: 0.6;
  line-height: 1.5;
  .descReturn {
    display: inline-block;
  }
}

.img {
  width: 100px;
}

.divider {
  height: 1px;
  border: solid 1px map-get($colors, ETFGrey);
}

.footer {
  display: flex;
  padding: 8px 15px;
  @include ipoTypography(subTitle2, map-get($colors, PureWhite));
  .arrowRight {
    margin-left: 5px;
    vertical-align: middle;
  }
}

.currentValueHeading {
  @include ipoTypography(heading4, map-get($colors, PureWhite));
}

.footerIcon {
  width: 12px;
  margin-right: 5px;
}

.currentValue {
  font-family: Lato;
  font-size: 28px;
  font-weight: bold;
  line-height: 1.5;
  color: #fff;
}

.return {
  margin-top: 11px;
  font-family: Intersh;
  font-size: 12px;
  line-height: 1.5;
  color: #fff;
}

.returnValue {
  display: flex;
  align-items: center;
}

.returnText {
  line-height: 1.43;
  align-items: center;
  font-size: 14px;

  > div:first-child {
    padding-right: 3px;
  }
}

.arrow {
  padding-left: 5px;
}
