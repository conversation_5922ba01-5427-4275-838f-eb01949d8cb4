import React from 'react';
import cx from 'classnames';
import styles from './ToggleButton.scss';

const ToogleButton = ({ config, activeBtn, onClick }) => (
  <div className={styles.wrapper}>
    {config.map(v => (
      <div
        className={cx(styles.btn, { [styles.activeBtn]: activeBtn === v.key })}
        key={v.key}
        onClick={() => onClick && onClick(v.key)}
      >
        <div className={styles.label}>{v.label}</div>
      </div>
    ))}
  </div>
);

export default ToogleButton;
