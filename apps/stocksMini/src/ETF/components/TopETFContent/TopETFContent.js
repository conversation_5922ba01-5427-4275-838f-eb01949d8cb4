import React, { useEffect, useState } from 'react';
// import PropTypes from 'prop-types';
import { getQueryString } from '@src/utils/navigationUtil';
import { useEtfData } from '@src/query/stocksQuery';
import InfiniteScroll from '@src/components/InfiniteScroll/InfiniteScroll';
import Shimmer from '@src/components/Shimmer/Shimmer';
import { etfNavigateTo } from '@src/ETF/utils/utilFunctions';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import Tab from '../Tab/Tab';
import noData from '../../../assets/images/noData.png';
import TopETFCard from '../TopETFCard/TopETFCard';
import styles from './TopETFContent.scss';
import {
  tabs,
  DEFAULT_TAB,
  ETF_TYPE_FILTER,
  ETF_FILTER_PARAMS,
} from '../../config/topETFConfig';
import { TOP_ETF_CONTENT } from '../enum';

const TopETFContent = () => {
  const { category = DEFAULT_TAB } = getQueryString(window.location.search);

  // states
  const [totalPageNumber, setTotalPageNumber] = useState(0);

  const getQueryParams = pageNumber => {
    const queryParams = {
      i: 'ETF',
      p: pageNumber,
      ps: 20,
      sort: 'ONE_YEAR_RETURN',
      so: 'DESC',
      x: 'NSE',
    };

    switch (category) {
      case ETF_TYPE_FILTER.GOLD:
        queryParams.sc = ETF_FILTER_PARAMS.GOLD;
        break;
      case ETF_TYPE_FILTER.DEBT:
        queryParams.sc = ETF_FILTER_PARAMS.DEBT;
        break;
      case ETF_TYPE_FILTER.GLOBAL:
        queryParams.sc = ETF_FILTER_PARAMS.GLOBAL;
        break;
      default:
        queryParams.sc = ETF_FILTER_PARAMS.EQUITY;
        break;
    }

    return queryParams;
  };

  // hooks
  const {
    isLoading,
    data: etfDataPages,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useEtfData(null, category, getQueryParams, true);

  useEffect(() => {
    const totalPage = Math.ceil(
      (etfDataPages?.pages[0]?.total_count || 0) / 20,
    );
    setTotalPageNumber(totalPage);
  }, [etfDataPages?.pages[0]?.total_count]);

  const getShimmerArray = noOfRows => {
    const shimmerArray = [];
    Array.from(Array(noOfRows).keys()).forEach(() => {
      shimmerArray.push(
        {
          height: '26px',
          row: ['80%'],
          margin: '0 0 16px 20px',
        },
        {
          height: '20px',
          row: ['100px'],
          margin: '0px 0 9px 20px',
        },
      );
    });
    return shimmerArray;
  };

  const handleTabChange = tab => {
    if (tab === tabs[0]) {
      etfNavigateTo({ base: ETF_ROUTES.TOP_ETFS });
    } else {
      etfNavigateTo({
        base: `${ETF_ROUTES.TOP_ETFS}?category=${tab}`,
        method: 'replace',
      });
    }
  };

  return (
    <>
      <Tab
        tabHeaderContainerCustomClass={styles.tabHeaderContainer}
        tabs={tabs}
        activeTab={category}
        tabOnClick={handleTabChange}
      >
        {etfDataPages?.pages?.length ? (
          <InfiniteScroll
            pageStart={0}
            initialLoad={false}
            loadMore={(!isFetchingNextPage && fetchNextPage) || null}
            hasMore={hasNextPage}
            loader={
              <Shimmer
                key={`${InfiniteScroll}-${Math.random(totalPageNumber)}`}
                type="card"
                cardArray={getShimmerArray(1)}
              />
            }
          >
            <div className={styles.tabContent}>
              {etfDataPages?.pages.map(etfData =>
                etfData.results.map(data => <TopETFCard data={data} />),
              )}
            </div>
          </InfiniteScroll>
        ) : !isLoading ? (
          <div className={styles.noFilterData}>
            <section>
              <img className={styles.noDataIcon} src={noData} alt="" />
              <p>{TOP_ETF_CONTENT.NO_DATA}</p>
            </section>
          </div>
        ) : (
          <Shimmer
            key={`${InfiniteScroll}-${Math.random(totalPageNumber)}`}
            type="card"
            cardArray={getShimmerArray(12)}
          />
        )}
      </Tab>
    </>
  );
};

TopETFContent.propTypes = {};

export default TopETFContent;
