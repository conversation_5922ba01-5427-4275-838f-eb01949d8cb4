.topETFCard {
  padding: 1.5rem 2rem;
  border: solid 1px rgba(16, 16, 16, 0.13);
  margin-bottom: 1rem;

  .cardTop {
    display: flex;
    width: 100%;
    margin-bottom: 1.5rem;
    align-items: center;

    .etfIcon,
    .title {
      margin-right: 1rem;
    }

    .etfIcon {
      display: flex;
    }

    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 70%;
      font-size: 1.4rem;
      font-weight: 600;
      color: #101010;
    }

    .tag {
      background-color: rgba(1, 42, 114, 0.1);
      color: #012a72;
    }

    .arrowForwardIcon {
      margin-left: auto;
      display: inline-block;
    }
  }

  .cardBottom {
    display: flex;
    justify-content: space-between;

    .priceContainer,
    .returnsContainer,
    .expenseRatioContainer {
      display: flex;
      flex-direction: column;

      > span:first-child {
        color: map-get($colors, BlackHalfOpacity);
        margin-bottom: 0.2rem;
      }

      > span:last-child {
        font-size: 1.2rem;
        font-weight: 600;
      }

      .price {
        font-size: 1.2rem;
        font-weight: 600;
      }

      .up {
        color: map-get($colors, Green5);
      }

      .down {
        color: map-get($colors, ETFRed);
      }
    }
  }
}

.row {
  margin-bottom: '9px';
}
