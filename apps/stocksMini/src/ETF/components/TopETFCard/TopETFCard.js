import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import arrowForward from '@src/assets/etf/arrow_forward.svg';
import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import { etfNavigateTo } from '@src/ETF/utils/utilFunctions';
import Shimmer from '@src/components/Shimmer/Shimmer';
import Icon, { LOGO_SIZE } from '@src/components/Icon';
import { useStockFeed } from '@src/utils/Equities/hooks';
import { roundValue } from '@src/utils/commonUtils';
import Card from '../Card/Card';
import styles from './TopETFCard.scss';
import Tag from '../Chip/Chip';
import { TOP_ETF_CARD } from '../enum';

const TopETFCard = ({ data = {} }) => {
  const {
    id,
    name,
    tag,
    one_year_return: returns,
    expense_ratio: expenseRatio,
    exchange,
    tracking_error: error,
    security_id: securityId,
  } = data;

  const handleCardClick = etfId => {
    etfNavigateTo({ base: `${ETF_ROUTES.DETAIL}?id=${etfId}` });
  };

  return (
    <Card
      key={id}
      customClass={styles.topETFCard}
      onClick={() => handleCardClick(id)}
    >
      <div className={styles.cardTop}>
        <span className={styles.etfIcon}>
          <Icon name={id} companyName={name} logoSize={LOGO_SIZE.MEDIUM} />
        </span>
        <span className={styles.title}>{name}</span>
        {!isEmpty(tag) && <Tag customClass={styles.tag}>{tag}</Tag>}
        <span className={styles.arrowForwardIcon}>
          <img src={arrowForward} alt="arrow-forward" />
        </span>
      </div>
      <div className={styles.cardBottom}>
        <div className={styles.priceContainer}>
          <span>{TOP_ETF_CARD.PRICE}</span>
          <EtfPriceBroadcast exchange={exchange} securityId={securityId} />
        </div>
        <div className={styles.returnsContainer}>
          {returns !== undefined ? (
            <>
              <span>{TOP_ETF_CARD.RETURN}</span>
              <span
                className={classNames(
                  styles.price,
                  returns > 0 ? styles.up : styles.down,
                )}
              >
                {returns}
              </span>
            </>
          ) : (
            <>
              <span>{TOP_ETF_CARD.TRACKING_ERROR}</span>
              <span className={classNames(styles.price)}>
                {roundValue(error)}
              </span>
            </>
          )}
        </div>
        <div className={styles.expenseRatioContainer}>
          <span>{TOP_ETF_CARD.EXPENSE_RATIO}</span>
          <span>{expenseRatio}%</span>
        </div>
      </div>
    </Card>
  );
};

TopETFCard.propTypes = {
  data: PropTypes.object,
};

TopETFCard.defaultProps = {
  data: {},
};

const EtfPriceBroadcast = ({ exchange, securityId }) => {
  const { ltp } = useStockFeed({
    exchange,
    securityId,
  });

  return (
    <>
      <div className={styles.row}>
        {ltp === undefined ? (
          <Shimmer height="15px" width="50px" />
        ) : (
          <>
            <span className={styles.price}> {`₹${roundValue(ltp)}`}</span>
          </>
        )}
      </div>
    </>
  );
};

export default TopETFCard;
