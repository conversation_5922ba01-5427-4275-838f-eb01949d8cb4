import Icon from '@src/components/Icon';
import React, { lazy, Suspense } from 'react';

import Drawer, { useDrawer } from '@src/components/Drawer';
import If from '@src/components/If';
import { validInstrumentTypeForSIP } from '@src/pages/CompanyDetail/partials/PriceAlertAndStartSip/utils';
import Button from '../Button/Button';
import styles from './ETFFooter.scss';
import EtfFooterOption from '../../../assets/etf/etf_footer_option.png';
import { ETF_FOOTER_STRINGS } from '../enum';

const StartSip = lazy(() =>
  import(
    /* webpackChunkName: 'CompanyDetailsStartSip' */ '@src/pages/CompanyDetail/partials/StartSip'
  ),
);

const ETFFooter = ({ sell, buy, companyDetail }) => {
  const { isOpen, onClose, onOpen } = useDrawer();
  const enableStartSip = validInstrumentTypeForSIP({
    instrumentType: companyDetail.instrument_type,
  });
  return (
    <>
      <div className={styles.footer}>
        <Button type="redWhite" isFooter onClick={sell}>
          {ETF_FOOTER_STRINGS.SELL}
        </Button>
        <Button type="green" isFooter onClick={buy}>
          {ETF_FOOTER_STRINGS.BUY}
        </Button>
        <Icon
          url={EtfFooterOption}
          className={styles.icon}
          width="28px"
          onClick={onOpen}
        />
      </div>
      <Drawer
        title={
          <If test={enableStartSip}>
            <Suspense fallback={<></>}>
              <StartSip
                companyDetail={companyDetail}
                containerClass={styles.startSip}
              />
            </Suspense>
          </If>
        }
        isOpen={isOpen}
        onClose={onClose}
      />
    </>
  );
};

export default ETFFooter;
