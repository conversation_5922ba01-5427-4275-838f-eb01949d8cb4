.container {
  font-family: Inter;
  margin: 1.5rem;
  padding: 1.5rem;
  background-color: map-get($colors, ETFBlue2);
  border-radius: 1rem;
  border: 1px solid #e0f6fa;

  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;

    .title {
      font-weight: 700;
      font-size: 1.6rem;
    }

    .viewAllButton {
      display: flex;
      font-weight: 700;
      font-size: 1rem;
      padding-right: 0;
      background-color: inherit;

      .arrowRight {
        align-self: center;
        margin-left: 0.46rem;
      }
    }
  }

  .bottom {
    .desc {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .manageSipCard {
      display: flex;
      align-items: center;
      box-shadow: 0 4px 10px 0 rgba(16, 16, 16, 0.05);
      background-color: map-get($colors, PureWhite);
      padding: 1rem;
      margin-bottom: 1rem;

      .cardTitle {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 0.2rem;
      }

      > div:nth-child(2) {
        // flex-grow: 0.7;
        max-width: 70%;
        margin-left: 0.5rem;
        margin-right: auto;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .date,
      .stockExchange {
        color: map-get($colors, ETFGray2);
      }

      .date {
        display: flex;

        span:first-child {
          align-self: center;
          margin-right: 0.2rem;
        }
      }

      .quantity {
        font-size: 1.2rem;

        .count {
          font-weight: 700;
          margin-left: 0.5rem;
        }
      }

      .stockExchange {
        text-align: end;
      }
    }
  }
}
