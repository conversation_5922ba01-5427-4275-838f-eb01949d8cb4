import React from 'react';
import { noop } from 'lodash';
import ArrowRightIcon from '@src/assets/etf/arrow_right.svg';
import CalendarIcon from '@src/assets/etf/calendar.svg';
import Card from '../Card/Card';
import { ETF_HOME } from '../../config/etfHomeConfig';
import styles from './ManageSip.scss';
import Button from '../Button/Button';

const ManageSip = () => (
  <section className={styles.container}>
    <div className={styles.top}>
      <span className={styles.title}>{ETF_HOME.MANAGE_SIP.TITLE}</span>
      <Button
        customClass={styles.viewAllButton}
        type="whiteCyan"
        onClick={noop}
      >
        View All
        <img
          className={styles.arrowRight}
          src={ArrowRightIcon}
          alt="arrow-right"
        />
      </Button>
    </div>
    <div className={styles.bottom}>
      <div className={styles.desc}>{ETF_HOME.MANAGE_SIP.DESC}</div>
      {ETF_HOME.MANAGE_SIP.DATA.map(
        ({ id, title, quantity, date, stockExchange }) => (
          <Card customClass={styles.manageSipCard} key={id}>
            <div className={styles.icon}>
              {/** Todo: change source image */}
              <img src={CalendarIcon} alt="" />
            </div>
            <div>
              <div className={styles.cardTitle}>{title}</div>
              <div className={styles.date}>
                <span>
                  <img src={CalendarIcon} alt="calendar" />
                </span>
                <span className={styles.dateText}>{date}</span>
              </div>
            </div>
            <div>
              <div className={styles.quantity}>
                <span>Qty.</span>
                <span className={styles.count}>{quantity}</span>
              </div>
              <div className={styles.stockExchange}>{stockExchange}</div>
            </div>
          </Card>
        ),
      )}
    </div>
  </section>
);

ManageSip.propTypes = {};

export default ManageSip;
