.featuredETFLandingContent {
  margin: 2.5rem 1.5rem;

  .featuredETFLandingCard {
    display: flex;
    height: 10rem;
    padding: 1rem;
    border-radius: 1.5rem;
    border: solid 1px map-get($colors, BlackOpacity1);

    &:not(:last-child) {
      margin-bottom: 1.5rem;
    }

    .cardLeft {
      display: grid;
      place-content: center;
      border-radius: 1rem;
      padding: 1rem;
      background-color: map-get($colors, LightGrayishBg);
      margin-right: 1.5rem;
      height: 8rem;
      width: 8rem;

      img {
        max-width: 8rem;
      }
    }

    .cardRight {
      &Top {
        font-size: 1.6rem;
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;

        > span:last-child img {
          width: 1.2rem;
          height: 1.2rem;
        }
      }

      &Bottom {
        font-size: 1.2rem;
        font-weight: 700;
        color: rgba(16, 16, 16, 0.5);
        overflow: hidden;

        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        word-break: break-word;
      }
    }
  }
}