.featureETFTop {
  display: flex;
  justify-content: space-between;
  margin: 2.5rem 1.5rem 0 1.5rem;

  .desc {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
  }

  .itemsCount {
    font-size: 1.2rem;
    color: map-get($colors, ETFBlackHalfOpacityGray4);
  }

  .filterButton {
    width: 3.2rem;
    height: 3.2rem;
    border: solid 1px rgba(16, 16, 16, 0.13);
    border-radius: 50%;
    display: grid;
    place-content: center;
  }
}

.tabContent {
  padding: 1.5rem;
}

.active {
  color: map-get($colors, ETFBlue);
}

.drawerContainer {
  margin: 0.5rem 2rem 2.5rem 2rem;

  .drawerHeader {
    display: flex;
    justify-content: space-between;

    > span {
      font-size: 1.6rem;
      font-weight: 700;
    }

    .filterResetButton {
      font-weight: 700;
      padding-right: 0.3rem;
    }
  }

  .filterOptions {
    font-size: 1.2rem;
    font-weight: 600;

    .filter {
      display: flex;
      justify-content: space-between;
      padding: 1.5rem 0;

      img {
        margin-top: 0.2rem;
      }

      .filterLeft,
      .filterRight {
        display: flex;
        align-items: center;
      }

      .filterLeft {
        > :first-child {
          margin-right: 1rem;
        }
        > :last-child {
          margin-left: 0.5rem;
        }
      }

      .filterRight {
        > :first-child {
          margin-right: 0.2rem;
          // color: map-get($colors, ETFBlue);
        }

        .active {
          color: map-get($colors, ETFBlue);
        }
      }
    }

    .filter:not(:last-child) {
      border-bottom: 1px solid map-get($colors, BlackOpacity1);
    }
  }
}