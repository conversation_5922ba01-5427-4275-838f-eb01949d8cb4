import React, { useEffect, useState } from 'react';
// import PropTypes from 'prop-types';

import { ETF_HEADER } from '@src/ETF/config/etfHomeConfig';
import { getQueryString } from '@src/utils/navigationUtil';
import { useEtfStoreFront } from '@src/ETF/query/storeFrontQuery';
import { getFeaturedEtfConfigUrl } from '@src/ETF/config/featuredETFConfig';
import { useFeaturedEtfConfig } from '@src/ETF/query/etfQuery';
import { STORE_FRONT_IDS } from '@src/ETF/utils/constants';
import FeaturedETFHeader from './FeaturedETFHeader';
import FeaturedETFLandingContent from './FeaturedETFLandingContent';
import FeaturedETFSubContent from './FeaturedETFSubContent';

import styles from './FeaturedETFPage.scss';

const FeaturedETFPage = () => {
  const { category } = getQueryString(window.location.search);
  const { data: landingData } = useEtfStoreFront(!category);
  const landingHeaderInfo = {
    title: ETF_HEADER.FEATURED_ETFS.title,
    description: ETF_HEADER.FEATURED_ETFS.description,
  };

  const { data } = useFeaturedEtfConfig(
    category,
    getFeaturedEtfConfigUrl(category),
    !!category,
  );

  const [categoryDetail, setCategoryDetail] = useState({});

  useEffect(() => {
    if (category && data) {
      setCategoryDetail(data);
    }
  }, [category, data]);

  if (!category)
    return (
      <>
        <FeaturedETFHeader
          titleCustomClass={styles.titleMain}
          headerInfo={landingHeaderInfo}
          isLandingPage
        />
        <FeaturedETFLandingContent
          data={landingData?.[STORE_FRONT_IDS.FEATURED_ETF_ID][0]?.items}
        />
      </>
    );

  return (
    <>
      <FeaturedETFHeader
        Header
        headerInfo={categoryDetail}
        isLandingPage={false}
      />
      <FeaturedETFSubContent list={categoryDetail?.etf} />
    </>
  );
};

FeaturedETFPage.propTypes = {};

export default FeaturedETFPage;
