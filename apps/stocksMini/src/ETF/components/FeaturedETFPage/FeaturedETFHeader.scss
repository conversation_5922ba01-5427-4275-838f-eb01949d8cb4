.featuredETFHeaderContainer {
  z-index: 9999;
  position: sticky;
  top: -1px;
  background-color: map-get($colors, LightGrayishBg);
  padding: 1.5rem 2rem 2.5rem 2rem;

  .headerTop {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;

    .headerTopLeft {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .title {
        font-size: 2.4rem;
        font-weight: 700;
      }
    }

    .icon {
      > img {
        width: 12.4rem;
        height: 12.4rem;
      }
    }
  }

  .headerBottom {
    font-size: 1.2rem;
    color: map-get($colors, BlackHalfOpacity);
    line-height: 1.5;
  }
}
