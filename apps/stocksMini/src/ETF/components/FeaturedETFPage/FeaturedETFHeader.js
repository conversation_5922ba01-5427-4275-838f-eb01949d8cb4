import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';

import Collapsible from '@src/ETF/components/Collapsible/Collapsible';
import history from '@src/history';

import styles from './FeaturedETFHeader.scss';
import backIcon from '../../../assets/etf/arrow_back_black.svg';

const FeaturedETFHeader = ({
  titleCustomClass,
  headerInfo: { title, icon, description },
  isLandingPage,
}) => {
  const handleBackPress = () => history.goBack();

  return (
    <div className={styles.featuredETFHeaderContainer}>
      <div className={styles.headerTop}>
        <div className={styles.headerTopLeft}>
          <div>
            <img onClick={handleBackPress} src={backIcon} alt="back-icon" />
          </div>
          <div className={cx(styles.title, titleCustomClass)}>{title}</div>
        </div>
        {!isEmpty(icon) && (
          <div className={styles.icon}>
            <img src={icon} alt={title} />
          </div>
        )}
      </div>
      {isLandingPage ? (
        <div className={styles.headerBottom}>{description}</div>
      ) : (
        <Collapsible customClass={styles.headerBottom}>
          {description}
        </Collapsible>
      )}
    </div>
  );
};

FeaturedETFHeader.defaultProps = {
  titleCustomClass: '',
};

FeaturedETFHeader.propTypes = {
  titleCustomClass: PropTypes.string,
  headerInfo: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    icon: PropTypes.object,
  }).isRequired,
};

export default FeaturedETFHeader;
