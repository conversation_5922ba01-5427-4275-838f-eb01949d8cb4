import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
// import PropTypes from 'prop-types';
import Drawer, { useDrawer } from '@src/components/Drawer';
import filterIcon from '@src/assets/etf/filter.svg';
import downArrow from '@src/assets/etf/arrow_down.svg';
import upArrow from '@src/assets/etf/arrow_up.svg';
import infoIcon from '@src/assets/etf/info.svg';
import { useCompanyDetails } from '@src/query/stocksQuery';
import { useBackPress } from '../../../utils/Equities/hooks';
import { defaultFilterOptions } from '../../config/featuredETFConfig';
import Button from '../Button/Button';
import TopETFCard from '../TopETFCard/TopETFCard';
import { FEATURED_ETF_SUBCONTENT } from './enum';

import styles from './FeaturedETFSubContent.scss';

const FeaturedETFContent = ({ list = [] }) => {
  const [filterOptions, modifyFilterOptions] = useState(defaultFilterOptions);
  const { isOpen, onClose, onToggle } = useDrawer();
  const { data: { data: companyData } = {} } = useCompanyDetails(
    list.toString(),
    false,
    false,
    !!list.length,
  );
  const [filteredData, setfilteredData] = useState([]);
  const { popStack } = useBackPress();

  const onFilterResetClick = () => {
    modifyFilterOptions(defaultFilterOptions);
    // closing the drawer
    popStack();
  };

  const onFilterClick = index => {
    const tempFilterOption = JSON.parse(JSON.stringify(defaultFilterOptions));
    const currentValue = filterOptions[index].value;
    const newValue = tempFilterOption[index].value;
    if (currentValue.LOW_TO_HIGH) {
      newValue.LOW_TO_HIGH = false;
      newValue.HIGH_TO_LOW = true;
    } else if (currentValue.HIGH_TO_LOW) {
      newValue.LOW_TO_HIGH = false;
      newValue.HIGH_TO_LOW = false;
    } else {
      newValue.LOW_TO_HIGH = true;
    }
    modifyFilterOptions(tempFilterOption);
    // closing the drawer
    popStack();
  };

  useEffect(() => {
    if (companyData?.results) {
      setfilteredData(companyData.results);
    }
  }, [companyData]);

  useEffect(() => {
    if (companyData?.results) {
      const unsortedData = JSON.parse(JSON.stringify(companyData.results));
      unsortedData.sort((a, b) => {
        for (let index = 0; index < filterOptions.length; index += 1) {
          switch (true) {
            case filterOptions[index].value.LOW_TO_HIGH:
              return (
                a[`${filterOptions[index].info}`] -
                b[`${filterOptions[index].info}`]
              );
            case filterOptions[index].value.HIGH_TO_LOW:
              return (
                b[`${filterOptions[index].info}`] -
                a[`${filterOptions[index].info}`]
              );
            default:
              break;
          }
        }
        return null;
      });
      setfilteredData(unsortedData);
    }
  }, [filterOptions]);

  const getFilterByValue = value => {
    switch (true) {
      case value.LOW_TO_HIGH:
        return (
          <>
            <span className={styles.active}>
              {FEATURED_ETF_SUBCONTENT.LOW_TO_HIGH}
            </span>
            <span>
              <img src={downArrow} alt="down-arrow" />
            </span>
          </>
        );
      case value.HIGH_TO_LOW:
        return (
          <>
            <span className={styles.active}>
              {FEATURED_ETF_SUBCONTENT.HIGH_TO_LOW}
            </span>
            <span>
              <img src={upArrow} alt="up-arrow" />
            </span>
          </>
        );
      default:
        return (
          <>
            <span>{FEATURED_ETF_SUBCONTENT.LOW_TO_HIGH}</span>
            <span>
              <img src={downArrow} alt="down-arrow" />
            </span>
          </>
        );
    }
  };

  const renderFeaturedETFCards = () => (
    <div className={styles.tabContent}>
      {filteredData.map(data => (
        <TopETFCard data={data} />
      ))}
    </div>
  );

  return (
    <>
      <div className={styles.featureETFTop}>
        <div>
          <div className={styles.desc}>
            {FEATURED_ETF_SUBCONTENT.IN_THIS_LIST}
          </div>
          <div className={styles.itemsCount}>
            {list.length} {FEATURED_ETF_SUBCONTENT.ITEMS}
          </div>
        </div>
        <Button
          customClass={styles.filterButton}
          onClick={onToggle}
          typ="white"
        >
          <img src={filterIcon} alt="filter" />
        </Button>
      </div>
      {renderFeaturedETFCards()}
      <Drawer isOpen={isOpen} onClose={onClose} showCross={false}>
        <div className={styles.drawerContainer}>
          <div className={styles.drawerHeader}>
            <span>{FEATURED_ETF_SUBCONTENT.SORT_BY}</span>
            <Button
              customClass={styles.filterResetButton}
              type="whiteCyan"
              onClick={onFilterResetClick}
            >
              {FEATURED_ETF_SUBCONTENT.RESET}
            </Button>
          </div>
          <div className={styles.filterOptions}>
            {filterOptions.map(({ id, imgSrc, title, info, value }) => (
              <div key={id} className={styles.filter}>
                <div className={styles.filterLeft}>
                  <span>
                    <img src={imgSrc} alt={title} />
                  </span>
                  <span>{title}</span>
                  {!isEmpty(info) && (
                    <span>
                      <img src={infoIcon} alt={title} />
                    </span>
                  )}
                </div>
                <div
                  onClick={() => onFilterClick(id)}
                  className={styles.filterRight}
                >
                  {getFilterByValue(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Drawer>
    </>
  );
};

FeaturedETFContent.propTypes = {};

export default FeaturedETFContent;
