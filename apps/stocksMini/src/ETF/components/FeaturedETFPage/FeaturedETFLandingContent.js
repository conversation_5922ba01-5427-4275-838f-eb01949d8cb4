import React from 'react';
// import PropTypes from 'prop-types';
import arrowRight from '@src/assets/etf/arrow_right.svg';
import {
  etfNavigateTo,
  normalizeTextToURL,
} from '@src/ETF/utils/utilFunctions';

import Card from '../Card/Card';
import { ETF_ROUTES } from '../../config/urlConfig';

import styles from './FeaturedETFLandingContent.scss';

const FeaturedETFLandingContent = ({ data }) => {
  const onCardClick = id =>
    etfNavigateTo({
      base: `${ETF_ROUTES.FEATURED_ETFS}?category=${normalizeTextToURL(id)}`,
    });

  return (
    <div className={styles.featuredETFLandingContent}>
      {data?.map(
        ({ id, secondary_image: icon, name: title, description }, idx) => (
          <Card
            key={id + idx}
            customClass={styles.featuredETFLandingCard}
            onClick={() => onCardClick(title)}
          >
            <div className={styles.cardLeft}>
              <img src={icon} alt={title} />
            </div>
            <div className={styles.cardRight}>
              <div className={styles.cardRightTop}>
                <span>{title}</span>
                <span>
                  <img src={arrowRight} alt="arrow-right" />
                </span>
              </div>
              <div className={styles.cardRightBottom}>{description}</div>
            </div>
          </Card>
        ),
      )}
    </div>
  );
};

FeaturedETFLandingContent.propTypes = {};

export default FeaturedETFLandingContent;
