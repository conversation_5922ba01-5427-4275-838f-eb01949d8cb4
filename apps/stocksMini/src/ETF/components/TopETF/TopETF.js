import React from 'react';

import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import { etfNavigateTo } from '@src/ETF/utils/utilFunctions';

import TopETFHeader from '../SectionHeader/SectionHeader';
import TopETFBody from './partials/TopETFBody';

import styles from './TopETF.scss';
import topETFIcon from '../../../assets/etf/top_etf.svg';
import { ETF_HOME } from '../../config/etfHomeConfig';

const TopETF = () => {
  const onButtonClick = () => {
    etfNavigateTo({ base: ETF_ROUTES.TOP_ETFS });
  };

  const TopETFButtonProps = {
    buttonTitle: 'View All',
    onButtonClick,
  };

  return (
    <section className={styles.container}>
      <TopETFHeader
        icon={topETFIcon}
        title={ETF_HOME.TOP_ETFS.TITLE}
        description={ETF_HOME.TOP_ETFS.DESC}
        buttonProps={TopETFButtonProps}
      />
      <TopETFBody />
    </section>
  );
};

TopETF.propTypes = {};

export default TopETF;
