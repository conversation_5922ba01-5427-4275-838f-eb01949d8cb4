.topETFCardStacker {
  margin-right: 1rem;

  .topETFCard {
    font-size: 1.4rem;
    padding: 1.5rem 4rem 1.5rem 1.5rem;
    max-width: 25.2rem;
    border: 1px solid #e0f6fa;
    background-color: map-get($colors, ETFBlue2);

    .cardTop,
    .cardBottom {
      display: flex;
    }

    .cardTop {
      align-items: center;
      font-weight: 600;
      margin-bottom: 1.5rem;

      .cardImage {
        margin-right: 0.5rem;

        .iconWrapper {
          width: 2.6rem;
          height: 2.6rem;
          border-radius: 50%;
          overflow: hidden;
          border: 1px solid map-get($colors, Grey17);

          img {
            height: 2.6rem;
            width: 2.6rem;
          }

          span {
            border: none;
            font-size: 2rem;
          }
        }
      }

      .cardTopLabel {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .cardBottom {
      .topETFPrice {
        margin-right: 7.4rem;
      }

      .topETFPrice,
      .topETFReturns {
        div:first-child {
          @include typography(text, map-get($colors, DGREY5));
          font-size: 1.2rem;
          font-weight: 400;
          margin-bottom: 0.5rem;
        }
      }

      .topETFReturns {
        .isPositive,
        .isNegative {
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 600;

          > :first-child {
            margin-right: 0.5rem;
          }
        }

        .isPositive {
          color: map-get($colors, ETFGreen);
        }

        .isNegative {
          color: map-get($colors, Red3);
        }
      }
    }

    &:first-child {
      margin-bottom: 1rem;
    }
  }

  &:last-child {
    margin-right: 1.5rem;
  }
}

.topETFChip {
  display: flex;
  align-items: center;
  color: map-get($colors, BlackHalfOpacity);
  border: solid 1px rgba(16, 16, 16, 0.1);
  border-radius: 9.6rem;
  font-size: 1.2rem;
  font-weight: 400;
  padding: 0.2rem 1rem;
  margin-right: 1rem;

  &:last-child {
    margin-right: 1.5rem;
  }

  &.activeChip {
    background-color: map-get($colors, ETFBlue);
    color: map-get($colors, PureWhite);
    font-weight: 700;
  }

  img {
    margin-right: 0.5rem;
  }
}
