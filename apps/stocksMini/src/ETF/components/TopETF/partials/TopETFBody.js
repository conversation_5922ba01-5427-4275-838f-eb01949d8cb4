import React, { useState } from 'react';
import cx from 'classnames';
// import PropTypes from 'prop-types';

import { useEtfData } from '@src/query/stocksQuery';
import Icon, { LOGO_SIZE } from '@src/components/Icon';
import positiveReturns from '@src/assets/etf/positive.svg';
import negativeReturns from '@src/assets/etf/negative.svg';

import { ETF_ROUTES } from '@src/ETF/config/urlConfig';
import Shimmer from '@src/components/Shimmer/Shimmer';

import HorizontalScroll from '../../HorizontalScroll/HorizontalScroll';
import Card from '../../Card/Card';
import Chip from '../../Chip/Chip';
import Tab from '../../Tab/Tab';

import styles from './TopETFBody.scss';
import { DEFAULT_TAB } from '../../../config/topETFConfig';
import { ETF_HOME } from '../../../config/etfHomeConfig';
import {
  amountToIndianFormat,
  etfNavigateTo,
  getQueryParams,
  isNegative,
} from '../../../utils/utilFunctions';
import { TOP_ETF_BODY } from '../../enum';

const TopETFBody = () => {
  const [currentTab, setCurrentTab] = useState(DEFAULT_TAB);

  const {
    DATA: { chipData },
  } = ETF_HOME.TOP_ETFS;

  const { data: etfDataPages, isLoading } = useEtfData(
    null,
    currentTab,
    getQueryParams(currentTab),
    true,
  );

  const currentCardData = etfDataPages?.pages[0].results || [];

  const onChipClick = id => {
    setCurrentTab(id);
  };

  const onCardClick = id => {
    etfNavigateTo({ base: `${ETF_ROUTES.DETAIL}?id=${id}` });
  };

  const shimmerArray = [
    {
      height: '122.66px',
      row: ['250px'],
      margin: '0 10px 0 0',
    },
    {
      height: '122.66px',
      row: ['250px'],
      margin: '0px 10px 0 0',
    },
    {
      height: '122.66px',
      row: ['250px'],
    },
  ];

  const renderCustomHeader = () => (
    <HorizontalScroll>
      {chipData.map(({ id, imgSrc, label }) => (
        <Chip
          onClick={() => onChipClick(id)}
          className={cx(styles.topETFChip, {
            [styles.activeChip]: id === currentTab,
          })}
          key={id}
        >
          <img src={imgSrc} alt={label} />
          {label}
        </Chip>
      ))}
    </HorizontalScroll>
  );

  const renderCard = cardData => {
    const {
      id,
      name: label,
      p_close: price,
      one_year_return: oneYearReturns,
    } = cardData;

    return (
      <Card onClick={() => onCardClick(id)} customClass={styles.topETFCard}>
        <div className={styles.cardTop}>
          <div className={styles.cardImage}>
            <Icon
              name={id}
              companyName={label}
              logoSize={LOGO_SIZE.MEDIUM}
              className={styles.iconWrapper}
            />
          </div>
          <div className={styles.cardTopLabel}>{label}</div>
        </div>
        <div className={styles.cardBottom}>
          <div className={styles.topETFPrice}>
            <div>{TOP_ETF_BODY.PRICE}</div>
            <div>{amountToIndianFormat(price)}</div>
          </div>
          <div className={styles.topETFReturns}>
            <div>{TOP_ETF_BODY.RETURN}</div>
            <div
              className={cx(styles.isPositive, {
                [styles.isNegative]: isNegative(oneYearReturns),
              })}
            >
              <span>{`${oneYearReturns}%`}</span>
              {!isNegative(oneYearReturns) ? (
                <img src={positiveReturns} alt="positive-returns" />
              ) : (
                <img src={negativeReturns} alt="negative-returns" />
              )}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  const renderCards = () => {
    const cards = [];
    if (!isLoading) {
      for (let index = 0; index < currentCardData.length; index += 2) {
        const { id } = currentCardData[index];
        cards.push(
          <div className={styles.topETFCardStacker} key={id}>
            {renderCard(currentCardData[index])}
            {index + 1 < currentCardData.length &&
              renderCard(currentCardData[index + 1])}
          </div>,
        );
      }
      return cards;
    }
    return <Shimmer type="card" cardArray={shimmerArray} />;
  };

  return (
    <div>
      <Tab
        customHeader={renderCustomHeader()}
        activeTab={currentTab}
        tabOnClick={setCurrentTab}
      >
        <HorizontalScroll>{renderCards()}</HorizontalScroll>
      </Tab>
    </div>
  );
};

TopETFBody.propTypes = {};

export default TopETFBody;
