import equity from '@src/assets/etf/equity.svg';
import goldSilver from '@src/assets/etf/gold_silver.svg';
import debt from '@src/assets/etf/debt.svg';
import international from '@src/assets/etf/international.svg';
import { ETF_TYPE_FILTER } from './topETFConfig';

export const ETF_HEADER = {
  HOME: 'ETFs',
  TOP_ETFS: 'Top ETFs',
  FEATURED_ETFS: {
    title: 'Featured ETFs',
    description:
      'Invest in top ETFs across popular categories. These ETFs are grouped by their investment ideology, returns and risk involved for your ease of selection.',
  },
};

export const KYC_CARD_STATUS = {
  NOT_INVESTED: 'NOT_INVESTED',
  INVESTED: 'INVESTED',
  PENDING_IR: 'PENDING_IR',
  NON_IR: 'NON_IR',
};

export const ETF_HOME = {
  KYC_CARD: {
    TITLE:
      'Investment of Rs.1 lakh in Nasdaq ETF would have been Rs zz lakh in the last 10 years',
    INVESTED: {
      TITLE: '',
      CTA: '',
      FOOTER: 'Have money? Top-up your portfolio',
    },
    PENDING_IR: {
      CTA: 'Explore',
      FOOTER: 'Your KYC verificaion is in progress…',
      BUTTON_TYPE: 'whiteBlue',
    },
    NOT_INVESTED: {
      CTA: 'Invest Now',
      FOOTER: 'You are ready to invest now!',
      BUTTON_TYPE: 'green',
    },
    NON_IR: {
      CTA: 'Complete KYC',
      FOOTER: 'You are few steps away to start investing.',
      BUTTON_TYPE: 'whiteBlue',
    },
  },
  ETF_BENEFIR: {
    TITLE: 'ETFs help in diversifying your portfolio',
    DESC:
      'ETFs provide investors with exposure to broad segments of the equity markets.',
  },
  SIP: {
    TITLE: 'Start SIP with just ₹40!',
    SUB_TITLE: 'Explore top rated safe funds',
    CTA: 'Start SIP Now',
  },
  VIDEO_LEARN: {
    TITLE: 'Learn more about ETFs',
    DESCRIPTION: 'How to Invest in Stocks. Lorem ipsum dolor sit amet',
  },
  GET_STARTED_WITH_SIP: {
    TITLE: 'Get Started with SIP',
    DESC:
      'Investment of Rs.1 lakh in NIFTY ETF would have been Rs zz lakh in the last 10 yrs.',
    CTA: 'Start SIP Now',
    DATA: {
      SIP_AMOUNT: [
        { id: 1, amount: 5000 },
        { id: 2, amount: 10000 },
        { id: 3, amount: 25000 },
      ],
    },
  },
  NEWS_AND_STORIES: {
    TITLE: 'News & Stories',
  },
  FEATURED_ETFS: {
    TITLE: 'Featured ETFs',
    DESC: 'Invest in ETFs from these popular categories',
  },
  TOP_ETFS: {
    TITLE: 'Top ETFs',
    DESC: 'ETFs that have given the best returns in last 3 years',
    DATA: {
      chipData: [
        { id: ETF_TYPE_FILTER.EQUITY, imgSrc: equity, label: 'Equity' },
        {
          id: ETF_TYPE_FILTER.GOLD,
          imgSrc: goldSilver,
          label: 'Gold / Silver',
        },
        { id: ETF_TYPE_FILTER.DEBT, imgSrc: debt, label: 'Debt' },
        {
          id: ETF_TYPE_FILTER.GLOBAL,
          imgSrc: international,
          label: 'International',
        },
      ],
    },
  },
  RECENT_ETFS: {
    TITLE: 'Recent ETFs',
    DESC: 'Explore recently launched ETFs',
  },
  ETF_COMPARISON: {
    TITLE: 'ETF Comparison',
  },
  MANAGE_SIP: {
    TITLE: 'Manage SIP',
    DESC: 'Next Investment',
    DATA: [
      {
        id: 1,
        imgScr: '',
        title: 'SBI Small Cap Fund SBI Small Cap Fund',
        quantity: 12,
        date: '03 Feb 2022 (In 23 days)',
        stockExchange: 'NSE',
      },
      {
        id: 2,
        imgScr: '',
        title: 'Nippon India ETF Bank BeES',
        quantity: 4,
        date: '03 Feb 2022 (In 23 days)',
        stockExchange: 'NSE',
      },
    ],
  },
};
