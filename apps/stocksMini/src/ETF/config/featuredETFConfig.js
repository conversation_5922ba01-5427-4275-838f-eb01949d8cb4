import expenseRatioIcon from '@src/assets/etf/expense_ratio.svg';
import trackingErrorIcon from '@src/assets/etf/tracking_error.svg';
import { FEATURED_ETF_CONFIG } from './urlConfig';

const defaultFilterOptions = [
  {
    id: 0,
    imgSrc: expenseRatioIcon,
    title: 'Expense Ratio',
    info: 'expense_ratio',
    value: {
      LOW_TO_HIGH: false,
      HIGH_TO_LOW: false,
    },
  },
  {
    id: 1,
    imgSrc: trackingErrorIcon,
    title: 'Tracking Error',
    info: 'tracking_error',
    value: {
      LOW_TO_HIGH: false,
      HIGH_TO_LOW: false,
    },
  },
];

const FEATURED_ETF_CATEGORIES = {
  LARGECAP: 'Largecap',
  GOVT_AND_PSU_DEBT: 'Govt and PSU Debt',
  SECTORAL: 'Sectoral',
  SMART_BETA: 'Smart Beta',
  INTERNATIONAL: 'International',
  BROAD_MARKET: 'Broad Market',
  THEMATIC: 'Thematic',
  GOLD_SILVER: 'Gold/Silver',
};

const getFeaturedEtfCategoryData = category => {
  switch (category) {
    case 'largecap':
      return FEATURED_ETF_CATEGORIES.LARGECAP;
    case 'govt-and-psu-debt':
      return FEATURED_ETF_CATEGORIES.GOVT_AND_PSU_DEBT;
    case 'sectoral':
      return FEATURED_ETF_CATEGORIES.SECTORAL;
    case 'smart-beta':
      return FEATURED_ETF_CATEGORIES.SMART_BETA;
    case 'international':
      return FEATURED_ETF_CATEGORIES.INTERNATIONAL;
    case 'broad-market':
      return FEATURED_ETF_CATEGORIES.BROAD_MARKET;
    case 'thematic':
      return FEATURED_ETF_CATEGORIES.THEMATIC;
    case 'gold-silver':
      return FEATURED_ETF_CATEGORIES.GOLD_SILVER;

    default:
      return null;
  }
};

const getFeaturedEtfConfigUrl = category => {
  switch (category) {
    case 'largecap':
      return FEATURED_ETF_CONFIG.LARGECAP;
    case 'govt-and-psu-debt':
      return FEATURED_ETF_CONFIG.GOVT_AND_PSU_DEBT;
    case 'sectoral':
      return FEATURED_ETF_CONFIG.SECTORAL;
    case 'smart-beta':
      return FEATURED_ETF_CONFIG.SMART_BETA;
    case 'international':
      return FEATURED_ETF_CONFIG.INTERNATIONAL;
    case 'broad-market':
      return FEATURED_ETF_CONFIG.BROAD_MARKET;
    case 'thematic':
      return FEATURED_ETF_CONFIG.THEMATIC;
    case 'gold-silver':
      return FEATURED_ETF_CONFIG.GOLD_SILVER;

    default:
      return null;
  }
};

export {
  defaultFilterOptions,
  FEATURED_ETF_CATEGORIES,
  getFeaturedEtfCategoryData,
  getFeaturedEtfConfigUrl,
};
