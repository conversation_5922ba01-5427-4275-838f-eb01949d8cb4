import { BASE_URL } from '@src/config/envConfig';

const { EQ_HOST } = BASE_URL;

export const ETF_ROUTES = {
  HOME: '/home',
  DETAIL: '/detail',
  TOP_ETFS: '/top-etfs',
  FEATURED_ETFS: '/featured-etfs',
};

export const STORE_FRONT_URL = userId =>
  `${BASE_URL.STOREFRONT}v2/h/etf-dashboard?client=android&user_id=${userId}`;

export const SIP = {
  CHECK: `${EQ_HOST}sip/instruction/api/v1/view`,
};

export const FEATURED_ETF_CONFIG = {
  LARGECAP: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/Largecap.json`,
  GOVT_AND_PSU_DEBT: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/Debt.json`,
  SECTORAL: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/Sectoral.json`,
  SMART_BETA: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/SmartBeta.json`,
  INTERNATIONAL: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/International.json`,
  BROAD_MARKET: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/BroadMarket.json`,
  THEMATIC: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/Thematic.json`,
  GOLD_SILVER: `${BASE_URL.STATIC_FILE_DATA_ETF}featuredetf/GoldSilver.json`,
};
