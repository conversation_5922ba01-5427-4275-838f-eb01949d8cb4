export const ETF_DETAIL = {
  RETURN_CALCULATOR: {
    TITLE: 'Return Calculator',
    RESULTANT_AMOUNT: 'Resultant Amount',
    RETURNS: 'Returns',
    INIT_AMOUNT: 1000,
    TF_ARR: [
      { label: '1Y', value: 1 },
      { label: '3Y', value: 3 },
      { label: '5Y', value: 5 },
    ],
    INIT_TF_INDEX: 1,
    DATA_ARR: [
      { id: 1, label: 'This ETF', totalAmount: 0, change: 12.23 },
      { id: 2, label: 'Saving Bank', totalAmount: 0, change: 3 },
      {
        id: 3,
        label: 'Fixed Deposit',
        totalAmount: 0,
        change: 5.5,
      },
    ],
    CHIP_VALUE: [
      { label: '+1,000', value: 1000 },
      { label: '+5,000', value: 5000 },
      { label: '+10,000', value: 10000 },
    ],
    TYPE_LIST: [
      { label: 'Absolute', key: 'absolute', id: 1 },
      { label: 'CAGR', key: 'cagr', id: 2 },
    ],
    NOTE: 'Past returns may not repeat in the future',
  },
  ABOUT: {
    TITLE: 'About',
  },
  PORTFOLIO: {
    TITLE: 'In your Portfolio',
    INVESTMENT_LABEL: 'Investment',
    SHARE_DETAIL_LABEL: 'Shares x Avg Price',
    CURRENT_VALUE_LABEL: 'Current Value',
    RETURN_LABEL: 'Overall Returns',
  },
  MANAGE_SIP: {
    TITLE: 'Manage SIPs',
  },
  HOLDING_SUMMARY: {
    TITLE: 'ETF Holding Summary',
    TAB_LIST: [
      { label: 'Sectors', key: 'sectors' },
      { label: 'Companies', key: 'companies' },
    ],
    FUND_SIZE_LABEL: 'Fund size (Cr)',
    AS_ON_DATE_LABEL: 'As on date',
  },
  VIDEO_CARD: {
    NOTE: 'Watch this video to understand how you can easily invest in ETF’s',
  },
  ETF_CATEGORY: {
    TITLE: 'ETFs in this Category',
    VIEW_ALL: 'View all',
  },
};
