import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

import {
  ETF_TYPE_FILTER,
  ETF_FILTER_PARAMS,
} from '@src/ETF/config/topETFConfig';
import { navigateTo } from '@src/services/coreUtil';
import history from '@src/history';
import { pClose } from '@src/actions/stockActions';

export const calculateCagr = (initialPrice, currentPrice, year) =>
  ((currentPrice / initialPrice) ** (1 / year) - 1) * 100;

export const calculateAbsolute = (initialPrice, currentPrice) =>
  ((currentPrice - initialPrice) / Math.abs(initialPrice)) * 100;

const ETF = '/etf';

export const getQueryParams = currentTab => () => {
  const queryParams = {
    i: 'ETF',
    p: 0,
    ps: 8,
    sort: 'ONE_YEAR_RETURN',
    so: 'DESC',
    x: 'NSE',
  };

  if (!isEmpty(currentTab)) {
    switch (currentTab) {
      case ETF_TYPE_FILTER.EQUITY:
        queryParams.sc = ETF_FILTER_PARAMS.EQUITY;
        break;
      case ETF_TYPE_FILTER.GOLD:
        queryParams.sc = ETF_FILTER_PARAMS.GOLD;
        break;
      case ETF_TYPE_FILTER.DEBT:
        queryParams.sc = ETF_FILTER_PARAMS.DEBT;
        break;
      case ETF_TYPE_FILTER.GLOBAL:
        queryParams.sc = ETF_FILTER_PARAMS.GLOBAL;
        break;
      default:
        break;
    }
  }

  return queryParams;
};

export const isNegative = num => num < 0;

export const normalizeTextToURL = text => {
  // *Note: more separators can be added
  const separators = [' ', '/'];

  return text
    .toLowerCase()
    .split(new RegExp(separators.join('|'), 'g'))
    .join('-');
};

export const etfNavigateTo = ({
  base,
  path = '',
  method = 'push',
  data = {},
}) =>
  navigateTo(history, `${ETF}${base}${path ? `/${path}` : ''}`, data, method);

export const amountToIndianFormat = (amount, decimals = 2) =>
  amount.toLocaleString('en-IN', {
    maximumFractionDigits: decimals,
    style: 'currency',
    currency: 'INR',
  });

export async function fetchPCloseData(pmlId) {
  dayjs.extend(customParseFormat);
  const threeYearBackDate = dayjs()
    .subtract(3, 'year')
    .endOf('month')
    .format('DD-MM-YYYY');

  const oneYearBackDate = dayjs()
    .subtract(1, 'year')
    .endOf('week')
    .format('DD-MM-YYYY');

  const fiveYearBackDate = dayjs()
    .subtract(5, 'year')
    .format('DD-MM-YYYY');

  const response = await pClose([
    {
      date: oneYearBackDate,
      pmlId,
    },
    {
      date: threeYearBackDate,
      pmlId,
    },
    {
      date: fiveYearBackDate,
      pmlId,
    },
  ]);
  const pCloseData = response.data.data.results.map((pcloseData, index) => {
    switch (index) {
      case 0:
        return { year: 1, pcloseValue: pcloseData.p_close };
      case 1:
        return { year: 3, pcloseValue: pcloseData.p_close };
      case 2:
        return { year: 5, pcloseValue: pcloseData.p_close };
      default:
        return [];
    }
  });
  return pCloseData;
}

export const getThreeMonthsPclose = async pmlId => {
  dayjs.extend(customParseFormat);
  const threeYearBackDate = dayjs()
    .subtract(3, 'month')
    .format('DD-MM-YYYY');

  const response = await pClose([
    {
      date: threeYearBackDate,
      pmlId,
    },
  ]);
  return response.data.data.results[0]?.p_close;
};

export const getOneDayPclose = async pmlId => {
  dayjs.extend(customParseFormat);
  const oneDayBackDate = dayjs()
    .subtract(1, 'day')
    .format('DD-MM-YYYY');

  const response = await pClose([
    {
      date: oneDayBackDate,
      pmlId,
    },
  ]);
  return response.data.data.results[0]?.p_close;
};

export const getOverViewReturns = async (pmlId, currentPrice) => {
  const threeMonthPclose = await getThreeMonthsPclose(pmlId);
  if (threeMonthPclose) {
    const threeMonthReturn = calculateAbsolute(threeMonthPclose, currentPrice);
    return { label: '3M RETURN', value: threeMonthReturn };
  }
  const oneDayPclose = await getOneDayPclose(pmlId);
  if (oneDayPclose) {
    const oneDayReturn = calculateAbsolute(oneDayPclose, currentPrice);
    return { label: '1D RETURN', value: oneDayReturn };
  }

  return null;
};
