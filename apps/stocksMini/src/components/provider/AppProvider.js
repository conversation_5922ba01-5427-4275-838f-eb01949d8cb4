/* eslint-disable import/no-named-as-default */
/* eslint-disable no-underscore-dangle */
import { useDocumentHide } from '@components/App';
import { APPEARANCE_TYPES } from '@components/Toast/enums';
import ToastController from '@components/Toast/partials/ToastController';
import { IR_STATUS_ENUM } from '@config/common';
import AppContext from '@context/AppContext';
import AppReducer, { appInitialState } from '@context/AppReducer';
import HoldingsContext from '@context/HoldingsContext';
import MarketStatus from '@context/MarketStatusContext';
import OrdersBookContext, { useReconnect } from '@context/OrdersBookContext';
import ScrollPosition from '@context/ScrollPositionContext';
import BridgeUtilContext from '@context/bridgeUtilContext';
import {
  getAppVersion,
  getCookieValue,
  getDeviceID,
  getDeviceName,
  getOsVersion,
  getSSOToken,
  getUserId,
} from '@services/coreUtil';
import ErrorBoundary from '@src/ErrorBoundary/ErrorBoundary';
import PortfolioProvider from '@src/Paytm-BBC/context/Portfolio/PortfolioProvider';
import CartProvider from '@src/Paytm-BBC/context/cart/CartProvider';
import { RootProvider } from '@src/Paytm-BBC/context/root/RootContext';
import PortfolioSummaryContext from '@src/context/PortfolioSummaryContext';
import CaptureLocationHOC from '@src/pages/CaptureLocationHOC';
import { exitApp } from '@src/utils/bridgeUtils';
import { ACTIONS, COOKIES } from '@utils/Constants';
import { useIRApi } from '@utils/IRutils';
import { getPlatformValue } from '@utils/apiUtil';
import { log } from '@utils/commonUtils';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import useFirstTradeEligible from '../ProfileProgress/useFirstTradeEligible';
import CommonProvider from './CommonProvider';
import styles from './index.scss';
import { useStoreFront } from '@src/query/storeFrontQuery';
import { downloadNudgeSF } from '@src/config/urlConfig';

function getAuthDetails() {
  return [
    getUserId(), // user_id
    '', // Authorization
    'paytmmoney', // x-pmngx-key
    getSSOToken(), // x-sso-token
    getPlatformValue(), // platform
    getAppVersion(), // app_version,
    getDeviceID(), // device_id
    getDeviceName(), // model
    getOsVersion(), // os_version
    getUserId(), // user_id
  ];
}

function useIrStatus(enabled = true) {
  const data = useIRApi(enabled);
  const irStatus = useMemo(() => data?.irData?.irStatus, [data]);
  return { irStatus, setIrStatus: () => {} };
}

function AppProvider(props) {
  const { isModuleFlow, splashScreenTimeout } = props;
  const { params } = props.route;

  const [Context, dispatch] = useReducer(AppReducer, appInitialState);
  const [dataFeed, setDataFeed] = useState(null);
  const [isDataFeedReady, setIsDataFeedReady] = useState(false);
  const [toastList, setToastList] = useState({});
  const [orderFeed, setOrderFeed] = useState(null);
  const [isOrderFeedReady, setIsOrderFeedReady] = useState(false);
  const [userData, setUserData] = useState({ boid: '', panNumber: '' });
  const [bucketsStatus, setBucketsStatus] = useState([]);
  const [showExitPop, setShowExitApp] = useState(false);
  const broadCastRef = useRef({
    dataFeedRef: false,
    isDataFeedReadyRef: false,
  });
  const {
    isLoading: isFirstTradeDataLoading,
    isFirstTradeEligible,
  } = useFirstTradeEligible();
  const irStatus = useIrStatus(!isModuleFlow);

  const { data: storeFrontData } = useStoreFront({
    name: 'downloadNudge',
    url: downloadNudgeSF(),
  });

  const isAppDownloadBlocker =
    storeFrontData?.page
      ?.find(el => el?.id === 339265)
      ?.views?.find(el => el?.id === 339265)?.items?.length > 0;

  const isAppDownloadCompanyBanner =
    storeFrontData?.page
      ?.find(el => el?.id === 339266)
      ?.views?.find(el => el?.id === 339266)?.items?.length > 0;

  useDocumentHide({
    onPause: () => {},
    onResume: () => {
      const ssoToken = getCookieValue(COOKIES.SSO_TOKEN);
      const userAgent = getCookieValue(COOKIES.USER_AGENT);
      log('check login status', ssoToken, userAgent);
      if (!(ssoToken && userAgent)) {
        exitApp();
      }
    },
  });

  const pauseFeed = () => {
    if (
      broadCastRef.current.dataFeedRef &&
      broadCastRef.current.isDataFeedReadyRef
    ) {
      setIsDataFeedReady(false);
      broadCastRef.current.dataFeedRef.pauseConnection();
    }
  };

  const resumeFeed = useCallback(() => {
    if (
      broadCastRef.current.dataFeedRef &&
      !broadCastRef.current.isDataFeedReadyRef
    ) {
      broadCastRef.current.dataFeedRef.reInitializeSocket();
      setIsDataFeedReady(true);
    }
  }, [dataFeed, isDataFeedReady]);

  const userReadinessStatus = useMemo(() => {
    if (props?.userBootData?.userReadinessStatusResponseDTO) {
      return props.userBootData.userReadinessStatusResponseDTO;
    }
    return null;
  }, [props?.userBootData?.userReadinessStatusResponseDTO]);

  window._dispatch = dispatch;
  window._context = Context;

  const reconnect = useCallback(() => {
    orderFeed.reconnectSocket();
  }, [orderFeed]);

  useReconnect(reconnect);

  useDocumentHide({ onPause: pauseFeed, onResume: resumeFeed });

  useEffect(() => {
    // update broadcast status ref
    broadCastRef.current.isDataFeedReadyRef = isDataFeedReady;
    broadCastRef.current.dataFeedRef = dataFeed;
  }, [isDataFeedReady, dataFeed]);

  useEffect(() => {
    import('../../services/equities').then(({ feeds, eventTypes }) => {
      const { data = null } = feeds;
      if (data) {
        data.initializeStream(getAuthDetails());
        data.addEventListener(eventTypes.connected, () => {
          setIsDataFeedReady(true);
        });
        setDataFeed(feeds?.data);
      }
    });
  }, []);

  useEffect(() => {
    if (irStatus?.irStatus === IR_STATUS_ENUM.ACTIVE && !orderFeed) {
      import('../../services/equities').then(({ feeds, eventTypes }) => {
        feeds.order.initializeStream(getAuthDetails());
        feeds.order.addEventListener(eventTypes.connected, () => {
          setIsOrderFeedReady(true);
        });
        feeds.order.addEventListener(eventTypes.log, data => {
          log([
            {
              level: 'info',
              key: 'socket_connection',
              timestamp: new Date().toISOString(),
              version: window.pmVersion,
              data: JSON.stringify({ type: 'order', ...data.log }),
            },
          ]);
        });
        setOrderFeed(feeds.order);
      });
    }
  }, [irStatus?.irStatus, orderFeed]);

  useEffect(() => {
    if (props.userBootData) {
      dispatch({
        type: ACTIONS.SET_USER_INFO,
        value: props.userBootData,
      });
    }

    if (props.onPasscordStatus) {
      dispatch({
        type: ACTIONS.ON_PASSCORD_STATUS,
        value: props.onPasscordStatus,
      });
    }

    if (props.isTwoFA) {
      dispatch({
        type: ACTIONS.IS_TWO_FA,
        value: props.isTwoFA,
      });
    }
  }, [props?.isTwoFA]);

  const addToast = options => {
    setToastList({
      message: options.message,
      type: options.type,
      onClick: options.onClick,
      label: options.label,
      labelAction: options.labelAction,
      closeButton: options.closeButton,
      customStyle: options.customStyle,
      autoDelete: options.autoDelete,
      customIcon: options.customIcon,
      hideIcon: options.hideIcon || false,
      customStyles: options.customStyles,
      toastStyle: options.toastStyle,
      component: options.component,
      dismissTime: options.dismissTime,
      fontStyle: options.fontStyle,
      labelStyle: options.labelStyle,
    });
  };

  const removeToast = () => {
    setToastList({});
  };
  return (
    <AppContext.Provider
      value={{
        Context,
        dispatch,
        dataFeed: isDataFeedReady ? dataFeed : null,
        orderFeed: isOrderFeedReady ? orderFeed : null,
        userReadinessStatus,
        addToast,
        toastList,
        clearToastStack: () => setToastList({}),
        hasInvestedStatus: props.hasInvestedStatus || null,
        irStatus,
        userData,
        setUserData,
        bucketsStatus,
        setBucketsStatus,
        showExitPop,
        setShowExitApp,
        isFirstTradeEligible,
        isFirstTradeDataLoading,
        splashScreenTimeout,
        isAppDownloadBlocker,
        isAppDownloadCompanyBanner,
      }}
    >
      <CommonProvider>
        <MarketStatus>
          <HoldingsContext>
            <OrdersBookContext isModuleFlow={isModuleFlow}>
              <ScrollPosition>
                <CaptureLocationHOC isModuleFlow={isModuleFlow}>
                  <BridgeUtilContext>
                    <RootProvider {...params}>
                      <PortfolioProvider {...props}>
                        <CartProvider {...props}>
                          <PortfolioSummaryContext
                            dataFeed={dataFeed}
                            isDataFeedReady={isDataFeedReady}
                            isModuleFlow={isModuleFlow}
                          >
                            <ErrorBoundary>{props.children}</ErrorBoundary>
                          </PortfolioSummaryContext>
                        </CartProvider>
                      </PortfolioProvider>
                    </RootProvider>
                  </BridgeUtilContext>
                </CaptureLocationHOC>
              </ScrollPosition>
            </OrdersBookContext>
          </HoldingsContext>
        </MarketStatus>
      </CommonProvider>
      <div
        className={styles.toastContainer}
        style={{
          position: 'fixed',
          bottom: '10px',
          zIndex: 99999,
          ...toastList?.toastStyle,
        }}
      >
        {Object.keys(toastList).length ? (
          <ToastController dismiss={removeToast} {...toastList} />
        ) : null}
      </div>
    </AppContext.Provider>
  );
}

function useAppContext() {
  return useContext(AppContext);
}
const useToast = () => {
  const ctx = useContext(AppContext);
  return {
    addToast: ctx.addToast,
    toastStack: [],
    appearanceType: APPEARANCE_TYPES,
    clearToastStack: ctx.clearToastStack,
  };
};

function useUserData() {
  const { userData, setUserData } = useContext(AppContext);

  return { userData, setUserData };
}

function useFirstTrade() {
  const { isFirstTradeEligible, isFirstTradeDataLoading } = useContext(
    AppContext,
  );

  return { isFirstTradeEligible, isFirstTradeDataLoading };
}

function useIRBucketsStatusData() {
  const { bucketsStatus, setBucketsStatus } = useContext(AppContext);

  return { bucketsStatus, setBucketsStatus };
}

function useExitApp() {
  const { showExitPop, setShowExitApp } = useContext(AppContext);

  const exitApp = () => {
    setShowExitApp(true);
  };

  const cancelExitApp = () => {
    setShowExitApp(false);
  };

  return { isOpen: showExitPop, exitApp, cancelExitApp };
}

export {
  AppProvider as default,
  useAppContext,
  useExitApp,
  useFirstTrade,
  useIRBucketsStatusData,
  useIrStatus,
  useToast,
  useUserData,
};
