/* eslint-disable */

import get from 'lodash/get';
// import queryString from 'query-string';
import DeviceInfoProvider from './utils/Providers/DeviceInfoProvider';
import { CLIENT_TYPE } from './utils/Constants';
import { processQuarryPrams, log } from './utils/commonUtils';

export function initApp(isBridge) {
  log('### initi app function called!!');
  if (isBridge) {
    DeviceInfoProvider.setInfo('client_type', CLIENT_TYPE.H5);
    localStorage.setItem('isH5', 1);
  } else {
    try {
      localStorage.setItem('isH5', 0);
      localStorage.setItem('device_type', CLIENT_TYPE.mWeb);
      DeviceInfoProvider.setInfo('client_type', CLIENT_TYPE.mWeb);
      const tokens = processQuarryPrams(window.location.search);
      DeviceInfoProvider.setInfo('device_type', CLIENT_TYPE.mWeb);
      let sessionInfo;
      if (typeof localStorage !== 'undefined') {
        sessionInfo = localStorage.getItem('sessionValue');
        sessionInfo = sessionInfo || '{}';
        sessionInfo = JSON.parse(sessionInfo);
      }
      DeviceInfoProvider.setInfo('isLogin', true);
      DeviceInfoProvider.setInfo(
        'sso_token_enc',
        'eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..NFUIXxRXn9LNliEs.4A4TsJQRv4sAIe822ySCdII6rWQ3M8OIPx2tRTI67fhqC_oUHsA-OpDCbJoNknBzkFaujG1t_jiqsKVdPKN8jq_ZVtiYcJqu0miWyVeOv3loNR9X3T2KhmxVkny6FyAjaZlA7yamV2_o1bC-eYdShvwZ2GwxF6Uy4oN5itd53fAa2W2JG4BdnKuaf4VI3w0Q7AWMSSwFNPzYyZ_sHHmuWJBudpW9ragruAIqvg8BxgGjrIUdMG1k32uh37CzKKj2gM9DH8CrfAYzjUjLZFlHox9TOpYorNxaViPBRk66ZVZ88JLSH8oWkQ1a_w.H8iiq8hqjdP6dqO0wNR4cg5200',
        true,
      );
      DeviceInfoProvider.setInfo(
        'sso_token',
        '87d98b3f-aef2-4dc7-8a8b-a0158ec03500',
        false,
      );
      DeviceInfoProvider.setInfo(
        'Authorization',
        'Basic cGF5dG0tbW9uZXk6c05VcnBaMnc3MjJJdXZVMnBnNnhPTThhbGRnWGtSOW0=',
        false,
      );
      DeviceInfoProvider.setInfo('device_type', '296db049af616c4', false);
      DeviceInfoProvider.setInfo(
        'appVersionName',
        '2.1.2602-development',
        false,
      );
      DeviceInfoProvider.setInfo('deviceName', 'motorola Moto G (5S)', false);
      DeviceInfoProvider.setInfo('userId', '231478903', false);
    } catch (e) {
      log(e);
    }
  }
}
