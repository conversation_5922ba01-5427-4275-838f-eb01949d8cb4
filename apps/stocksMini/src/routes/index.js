import { ROUTES } from '@src/Paytm-BBC/routes/config/urlConfig';
import { MINI_APP_ROUTES } from '@config/urlConfig';
// import { doLogin } from '../utils/bridgeUtils';
// import DeviceInfoProvider from '../utils/Providers/DeviceInfoProvider';
import learnRoutes from '../learn/routes';
import etfRoutes from '../ETF/routes';
import bbcRoutes from '../Paytm-BBC/routes';
// import { log } from '../utils/commonUtils';

// The top-level (parent) route
const routes = {
  path: `/`,

  // Keep in mind, routes are evaluated in order
  children: [
    // {
    //   path: MINI_APP_ROUTES.PORTFOLIO,
    //   load: () => import(/* webpackChunkName: 'Portfolio' */ './Portfolio'),
    // },
    // {
    //   path: MINI_APP_ROUTES.COMPANY_FUNDAMENTALS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'CompanyFundamentals' */ './CompanyFundamentals'
    //     ),
    // },
    {
      path: MINI_APP_ROUTES.COMBINED_DASHBOARD,
      load: () =>
        import(
          /* webpackChunkName: 'CombinedDashboardPage' */ './CombinedDashboardPage'
        ),
    },
    // {
    //   path: MINI_APP_ROUTES.REVAMP_COMBINE,
    //   load: () =>
    //     import(/* webpackChunkName: 'RevampCombine' */ './RevampCombine'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ORDER_PAD_LITE,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrderPadLite' */ './PlaceOrder/OrderPadLite'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.EQUITY,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'MiniAppDashboardLanding' */ './MiniAppDashboardLanding'
    //     ),
    // },
    {
      path: MINI_APP_ROUTES.EQUITY_DASHBOARD,
      load: () =>
        import(
          /* webpackChunkName: 'EquityDashboardLanding' */ './EquityDashboardLanding'
        ),
    },
    {
      path: MINI_APP_ROUTES.COMPANY,
      load: () =>
        import(/* webpackChunkName: 'CompanyDetail' */ './CompanyDetail'),
    },
    {
      path: MINI_APP_ROUTES.COMPANY_REVAMP,
      load: () =>
        import(
          /* webpackChunkName: 'CompanyDetailRevamp' */ './Revamp4.0/CompanyDetail'
        ),
    },
    // {
    //   path: MINI_APP_ROUTES.LEDGER_HISTORY_REVAMP,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'LedgerHistoryRevamp' */ './LedgerHistoryRevamp'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PWC,
    //   load: () => import(/* webpackChunkName: 'pwc' */ './pwc'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ADD_MONEY,
    //   load: () => import(/* webpackChunkName: 'AddMoney' */ './AddMoney'),
    // },
    // {
    //   path: MINI_APP_ROUTES.AUTO_PAY,
    //   load: () => import(/* webpackChunkName: 'AutoPay' */ './AutoPay'),
    // },
    // {
    //   path: MINI_APP_ROUTES.INITIATE_AUTO_PAY,
    //   load: () =>
    //     import(/* webpackChunkName: 'IntitateAutoPay' */ './InitiateAutoPay'),
    // },
    // {
    //   path: MINI_APP_ROUTES.BANK_ACCOUNTS,
    //   load: () =>
    //     import(/* webpackChunkName: 'BankAccounts' */ './BankAccounts'),
    // },
    // {
    //   path: MINI_APP_ROUTES.AUTO_PAY_CONFIRMATION,
    //   load: () =>
    //     import(/* webpackChunkName: 'AutoPay' */ './AutoPayConfirmation'),
    // },
    // {
    //   // path: MINI_APP_ROUTES.TRANSACTION_RECEIPT,
    //   path: MINI_APP_ROUTES.ORDER_CONFIRMATION,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrderConfirmation' */ './OrderConfirmation'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.ORDER_CONFIRMATION_POPUP,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrderConfirmationPopup' */ './OrderConfirmationPopup'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PRICE_ALERT_SUMMARY,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'PriceAlertSummaryPage' */ './PriceAlertAndSipSummary'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PINSTOCKS,
    //   load: () => import(/* webpackChunkName: 'PinStocksPage' */ './PinStocks'),
    // },
    // {
    //   path: MINI_APP_ROUTES.FO_RISK_DISCLOSURE,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'FnoRiskDisclosure' */ './FnoRiskDisclosure'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.FIRST_INVESTMENT_ORDER_CONFIRMATION,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrderConfirmationFirstTrade' */ './FirstInvestment/OrderConfirmation'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.FIRST_INVESTMENT,
    //   load: () =>
    //     import(/* webpackChunkName: 'FirstInvestment' */ './FirstInvestment'),
    // },
    // // {
    // //   path: '/my-funds',
    // //   load: () => import(/* webpackChunkName: 'CompanyDetail' */ './MyFunds'),
    // // },
    // {
    //   path: '/transaction-receipt',
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'TransactionReceipt' */ './TransactionReceipt'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.TRANSFER_MONEY,
    //   load: () =>
    //     import(/* webpackChunkName: 'TransferMoney' */ './TransferMoney'),
    // },
    // // TODO: Uncomment For testing the common widgets.
    // {
    //   path: '/common',
    //   load: () =>
    //     import(/* webpackChunkName: 'CommonComponents' */ './CommonComponents'),
    // },
    // {
    //   path: '/test',
    //   load: () =>
    //     import(/* webpackChunkName: 'TestAnotherPage' */ './TestAnotherPage'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PASSCODE,
    //   load: () => import(/* webpackChunkName: 'Passcode' */ './Passcode'),
    // },
    // {
    //   path: MINI_APP_ROUTES.TWO_FA_OTP,
    //   load: () => import(/* webpackChunkName: 'TwoFaOTP' */ './TwoFaOTP'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MANAGE_PASSCODE,
    //   load: () =>
    //     import(/* webpackChunkName: 'ManagePasscode' */ './ManagePasscode'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MY_STOCKS,
    //   load: () => import(/* webpackChunkName: 'MyStocks' */ './MyStocks'),
    // },
    // {
    //   path: MINI_APP_ROUTES.KYC,
    //   load: () => import(/* webpackChunkName: 'KycLandingPage' */ './KYC'),
    //   children: [],
    // },
    // {
    //   path: MINI_APP_ROUTES.PLACE_ORDER_BUY,
    //   load: () =>
    //     import(/* webpackChunkName: 'PlaceOrderBuy' */ './PlaceOrder/Buy'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PLACE_ORDER_SELL,
    //   load: () =>
    //     import(/* webpackChunkName: 'PlaceOrderSell' */ './PlaceOrder/Sell'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ORDER_PAD,
    //   load: () =>
    //     import(/* webpackChunkName: 'OrderPad' */ './PlaceOrder/OrderPad'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PLACE_ORDER_ROUTE_RESOLVER,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'PlaceOrderResolver' */ './PlaceOrderResolver'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.EDIS_AUTH,
    //   load: () =>
    //     import(/* webpackChunkName: 'EDISAuth' */ './PlaceOrder/EDISAuth'),
    // },
    // {
    //   path: MINI_APP_ROUTES.LEARN,
    //   children: learnRoutes,
    // },
    // {
    //   path: MINI_APP_ROUTES.ORDERS,
    //   load: () => import(/* webpackChunkName: 'MyOrders' */ './MyOrders'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PAYMENT,
    //   load: () => import(/* webpackChunkName: 'Payments' */ './Payments'),
    // },
    // {
    //   path: MINI_APP_ROUTES.BANK_TRANSFER,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'BankTransferDetails' */ './BankTransferDetails'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.ETF_HOME,
    //   load: () =>
    //     import(/* webpackChunkName: 'EtfHomeRevampHome' */ './EtfHome'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ETF,
    //   load: () => import(/* webpackChunkName: 'ETFLanding' */ './ETFLanding'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ETF_REVAMP,
    //   load: () => import(/* webpackChunkName: 'EtfRevamp' */ './EtfRevamp'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ALL_EVENTS,
    //   load: () => import(/* webpackChunkName: 'AllEvents' */ './AllEvents'),
    // },
    // {
    //   path: MINI_APP_ROUTES.KYC_PENDING,
    //   load: () =>
    //     import(/* webpackChunkName: 'KycPending' */ './KycPendingScreen'),
    // },
    // {
    //   path: '',
    //   load: () => import(/* webpackChunkName: 'KycPricing' */ './KycPricing'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PRICING,
    //   load: () => import(/* webpackChunkName: 'KycPricing' */ './KycPricing'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PROFILE_EDIT,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'Kyc Profile Edit' */ './KycProfile/ProfileEdit'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PROFILE,
    //   load: () => import(/* webpackChunkName: 'Kyc Profile' */ './KycProfile'),
    // },
    // {
    //   path: MINI_APP_ROUTES.DEMAT_ACCOUNT,
    //   load: () =>
    //     import(/* webpackChunkName: 'Demat Account' */ './DematAccount'),
    // },
    // {
    //   path: MINI_APP_ROUTES.NOTIFICATION_PREFERENCES,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'NotificationPreferences' */ './NotificationPreferences'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.IPV,
    //   load: () => import(/* webpackChunkName: 'IPV' */ './IPV'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MANAGE_COMMUNICATION,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'ManageCommunication' */ './ManageCommunication'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.ACCOUNT_STATEMENTS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'AccountStatements' */ './AccountStatements'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.STATEMENTS_PAGE,
    //   load: () =>
    //     import(/* webpackChunkName: 'StatementsPage' */ './StatementsLanding'),
    // },
    // {
    //   path: MINI_APP_ROUTES.DOC_DOWNLOAD_CENTER,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'DocumentsDownloadCenter' */ './DocDownloadCenter'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.BULK_AUTHORISATION,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'BulkAuthorisation' */ './BulkAuthorisation'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.POSITIONS,
    //   load: () => import(/* webpackChunkName: 'Positions' */ './Positions'),
    // },
    // {
    //   path: MINI_APP_ROUTES.POSITION_DETAILS,
    //   load: () =>
    //     import(/* webpackChunkName: 'PositionDetails' */ './PositionDetails'),
    // },
    // {
    //   path: MINI_APP_ROUTES.CONVERT_ORDER,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'ConvertOrder' */ './PlaceOrder/ConvertOrder'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.SIP,
    //   load: () => import(/* webpackChunkName: 'Sip' */ './Sip'),
    // },
    // {
    //   path: MINI_APP_ROUTES.SIP_CONFIRMATION,
    //   load: () =>
    //     import(/* webpackChunkName: 'SipConfirmation' */ './SipConfirmation'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MY_SIP,
    //   load: () => import(/* webpackChunkName: 'MySip' */ './MySip'),
    // },
    // {
    //   path: MINI_APP_ROUTES.UPCOMING_SIP,
    //   load: () => import(/* webpackChunkName: 'UpcomingSip' */ './UpcomingSip'),
    // },
    // {
    //   path: MINI_APP_ROUTES.CHECK_KYC,
    //   load: () => import(/* webpackChunkName: 'CheckKYC' */ './CheckKyc'),
    // },
    // {
    //   path: '/etf',
    //   children: etfRoutes,
    // },
    // {
    //   path: '/bbc',
    //   children: bbcRoutes,
    // },
    // {
    //   path: MINI_APP_ROUTES.COMPANY_INSTRUCTIONS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'CompanyInstructions' */ './CompanyInstructions'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.COMPANY_INSTRUCTIONS_REVAMP,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'CompanyInstructionsRevamp' */ './CompanyInstructionsRevamp'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.FNO,
    //   load: () => import(/* webpackChunkName: 'Fno' */ './FNO'),
    //   children: [],
    // },
    // {
    //   path: MINI_APP_ROUTES.PRODUCT_VIEW_ALL,
    //   // eslint-disable-next-line import/no-unresolved
    //   load: () =>
    //     import(/* webpackChunkName: 'ProcutViewAll' */ './ProductViewAll'),
    //   children: [],
    // },
    // {
    //   path: '/charts',
    //   load: () => import(/* webpackChunkName: 'ChartsTest' */ './Charts'),
    //   children: [],
    // },
    // {
    //   path: MINI_APP_ROUTES.FUND_DETAILS,
    //   load: () => import(/* webpackChunkName: 'fundDetails' */ './FundDetails'),
    // },
    // {
    //   path: MINI_APP_ROUTES.EXIT_POSITIONS,
    //   load: () =>
    //     import(/* webpackChunkName: 'exitPositions' */ './ExitPositions'),
    // },
    // {
    //   path: MINI_APP_ROUTES.EXIT_POSITIONS_V2,
    //   load: () =>
    //     import(/* webpackChunkName: 'exitPositionsV2' */ './ExitPositionsV2'),
    // },
    // {
    //   path: MINI_APP_ROUTES.LEDGER_HISTORY,
    //   load: () =>
    //     import(/* webpackChunkName: 'LedgerHistory' */ './LedgerHistory'),
    // },
    // {
    //   path: MINI_APP_ROUTES.BROKERAGE_CALCULATOR,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'BrokerageCalculator' */ './BrokerageCalculator'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.MARGIN_CALCULATOR,
    //   load: () =>
    //     import(/* webpackChunkName: 'MarginCalculator' */ './MarginCalculator'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MARGIN_PLEDGE,
    //   load: () =>
    //     import(/* webpackChunkName: 'MarginPledge' */ './MarginPledge'),
    //   children: [],
    // },
    // {
    //   path: MINI_APP_ROUTES.FIRST_FUND_ADDITION_STATUS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'FirstFundAddStatus' */ './FirstFundAddStatus'
    //     ),
    //   children: [],
    // },
    // {
    //   path: ROUTES.STOREFRONT.path,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'Storefront' */ `./${ROUTES.STOREFRONT.component}`
    //     ),
    // },
    // {
    //   path: ROUTES.MARKET_UPDATES.path,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'MarketUpdates' */ `./${ROUTES.MARKET_UPDATES.component}`
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.OTP_CONSENT,
    //   load: () => import(/* webpackChunkName: 'OTPConsent' */ './OTPConsent'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MARKET_INDICES,
    //   load: () =>
    //     import(/* webpackChunkName: 'MarketIndices' */ './MarketIndices'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MARKET_INDICES_REVAMP,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'MarketIndicesRevamp' */ './MarketIndicesRevamp'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.MANAGE_PINSTOCKS,
    //   load: () =>
    //     import(/* webpackChunkName: 'ManagePinStocks' */ './ManagePinStocks'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MARKET_MOVERS,
    //   load: () =>
    //     import(/* webpackChunkName: 'MarketMovers' */ './MarketMovers'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MOST_BOUGHT,
    //   load: () => import(/* webpackChunkName: 'MostBought' */ './MostBought'),
    // },
    // {
    //   path: MINI_APP_ROUTES.MINI_APP_DEEPLINK,
    //   load: () =>
    //     import(/* webpackChunkName: 'DeeplinkHandler' */ './DeeplinkHandler'),
    // },
    // {
    //   path: MINI_APP_ROUTES.EDIT_WATCHLIST,
    //   load: () =>
    //     import(/* webpackChunkName: 'EditWatchlist' */ './EditWatchlist'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PNL,
    //   load: () => import(/* webpackChunkName: 'PNL' */ './PNL'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PNL_DETAILS,
    //   load: () => import(/* webpackChunkName: 'PnlDetails' */ './PnlDetails'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ERROR_PAGE,
    //   load: () => import(/* webpackChunkName: 'ErrorBoundary' */ './ErrorPage'),
    // },
    // {
    //   path: MINI_APP_ROUTES.ALL_TRANSACTION_HISTORY,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'AllTransactionHistory' */ './AllTransactionHistory'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.MY_FUNDS_V2,
    //   load: () => import(/* webpackChunkName: 'My Funds' */ './MyFunds'),
    // },
    // {
    //   path: MINI_APP_ROUTES.POPULAR_MF,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'PopularMutualFundLanding' */ './PopularMutualFundLanding'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PRE_IR_TRADE,
    //   load: () => import(/* webpackChunkName: 'PreIrTrade' */ './PreIrTrade'),
    // },
    // {
    //   path: MINI_APP_ROUTES.PRE_IR_TRADE_ORDER_CONFIRMATION,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrderConfirmationPreIrTrade' */ './PreIrTrade/OrderConfirmation'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.PRE_IR_TRADE_ORDERS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'OrdersPreIrTrade' */ './PreIrTrade/Orders'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.HOLDING_PAGE_COMPANY,
    //   load: () =>
    //     import(/* webpackChunkName: 'CompanyHolding' */ './CompanyHolding'),
    //   children: [],
    // },
    // {
    //   path: MINI_APP_ROUTES.CHAT_BOT,
    //   load: () => import(/* webpackChunkName: 'ChatBot' */ './ChatBot'),
    // },
    // {
    //   path: MINI_APP_ROUTES.RESULT_VS_EXPECTATIONS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'resultVsExpectationPage' */ './ResultVsExpectationRoute'
    //     ),
    // },
    // {
    //   path: MINI_APP_ROUTES.EQUITY_TRANSACTION_DETAILS,
    //   load: () =>
    //     import(
    //       /* webpackChunkName: 'equityTransactionDetails' */ './EquityTransactionDetails'
    //     ),
    // },
    {
      path: MINI_APP_ROUTES.PML_APP_DOWNLOAD,
      load: () =>
        import(/* webpackChunkName: 'PML_APP_DOWNLOAD' */ './PML_APP_DOWNLOAD'),
    },
  ],
  async action({ next }) {
    // Execute each child route until one of them return the result
    let route = await next();
    route = route || {};
    route.title = route.title || '';
    route.description = route.description || '';

    return route;
  },
};

if (__DEV__) {
  /**
   * @TODO remove comment after __DEV__ issue,
   */
  // routes.children.unshift({
  //   path: '/error',
  //   action: require('./ErrorPage').default,
  // });
}

export default routes;
