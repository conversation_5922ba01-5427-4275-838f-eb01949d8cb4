import React, { useEffect, useMemo, useState } from 'react';
import { navigateToPositions } from '@src/pages/Positions/utils';
import { useIRApi } from '@src/utils/IRutils';
import {
  companyPageNavigation,
  getQueryParam,
  combineDashboardDeepTypeHelper,
  navigateUserProfile,
  deepLinkNeedHelp,
} from '@utils/navigationUtil';
import { useUserReadiness } from '@src/query/readinessQuery';
import history from '@src/history';
import { isIosBuild } from '@src/utils/commonUtils';
import { reLogin, getBridge, isBridge, isH5 } from '@src/utils/bridgeUtils';
import { useBackPress } from '@src/utils/Equities/hooks';
import YouTubePlayer from '@src/components/VideoPlayers';
import PinStocksOverview from '@src/components/PinStocksOverview/PinStocksOverview';
import { setPinStocksEnabled } from '@src/actions/genericActions';
import { useCommonContext } from '@src/components/provider/CommonProvider';
import { getIRStatusForDigio, useKycRoutes } from '@src/utils/utilFunctions';
import { MINI_APP_ROUTES } from '@src/config/urlConfig';
import { useIrData } from '@src/query/kycQuery';
import { IR_STATUS_FOR_MSG, REVOKE_SUB_TYPE } from '@src/config/irConfig';
import { useCurrentPlan } from '@src/query/generalQuery';
import { IR_STATUS_ENUM } from '@src/config/common';
import { HAS_INVESTED_STATUS } from '@src/utils/Constants';
import Loader from '@src/components/Loader/Loader';

import { useAppContext } from '../../components/provider/AppProvider';
import { QUERY_STRING } from '../../config/homeDashboardConfig';
import { getUserAgent } from '../../utils/apiUtil';
import {
  getNormalSSOToken,
  navigateTo,
  sendEventToBridge,
} from '../../services/coreUtil';
import Footer from './Footer';
import FOBlocked from './FOBlocked';

// eslint-disable-next-line import/no-unresolved, import/extensions
const PMLFnoApp = React.lazy(() => import('PMLCOMMON/App'));

const authDetails = {
  'x-sso-token': getNormalSSOToken(),
  'x-user-agent': getUserAgent(),
};

const FNOLanding = () => {
  const { dataFeed, Context } = useAppContext();
  const { isRevampUser } = Context;
  const { irData, refetch, isRefetchingIr, isRefetchingJourney } = useIRApi();
  const { data: journeyData } = useIrData();
  const [showFOblocked, setShowFOblocked] = useState(false);
  const fnoActivationSuccess = history.location.state?.fnoActivationSuccess;
  const [isDataRefetching, setIsDataRefetching] = useState(true);

  const isPrelook = sessionStorage.getItem('prelook');
  const FOreadinessData = useMemo(
    () =>
      !isPrelook && irData?.data?.EQUITY.find(item => item.subProduct === 'FO'),
    [irData],
  );
  const FOirStatus = FOreadinessData?.irStatus;
  const isUserInvested =
    FOreadinessData?.hasInvested === HAS_INVESTED_STATUS.HAS_INVESTED;

  const isFnoReady = true; // FOirStatus === IR_STATUS_ENUM.ACTIVE;
  const isCashReady = true // useMemo(
  //   () =>
  //     !isPrelook &&
  //     irData?.data?.EQUITY.find(item => item.subProduct === 'CASH').irStatus ===
  //       'ACTIVE',
  //   [irData],
  // );

  const { data: subscriptionData } = useCurrentPlan();
  const irStatus = useMemo(
    () =>
      ![
        IR_STATUS_ENUM.ACTIVE,
        IR_STATUS_ENUM.DORMANCY_IN_PROGRESS,
        IR_STATUS_ENUM.DORMANT_REVOKED,
        IR_STATUS_ENUM.REKYC_IN_PROGRESS,
      ].includes(FOirStatus)
        ? journeyData?.data?.irStatus
        : FOirStatus,
    [FOirStatus],
  );

  const IRinvestmentStatus = `irStatus=${irStatus} | isUserInvested=${isUserInvested}`;

  // checks whether KYC is available
  // for gracefully handling if KYC app is down
  // useKycRoutes();
  const navigate = value => {
    // added to prevent events listener getting invoked multiple times
    value.stopImmediatePropagation();
    const { toCompany, toPositions, toSearch, url, authError } = value.detail;
    if (toSearch) {
      navigateTo(history, QUERY_STRING.SEARCH, { source: 'fno' });
    }
    if (toPositions) {
      navigateToPositions();
    }
    if (toCompany) {
      companyPageNavigation(getQueryParam(url, 'id'), false);
    }
    if (authError) {
      reLogin();
    }
  };

  useEffect(() => {
    window.addEventListener('redirectEvent', navigate);
    return () => {
      window.removeEventListener('redirectEvent', navigate);
    };
  }, []);

  useEffect(()=>{
   const refetchData =  () => {
      if (fnoActivationSuccess && refetch) {
        setIsDataRefetching(true);
         refetch();
        setIsDataRefetching(false);
      } else {
        setIsDataRefetching(false);
      }
    }
    refetchData();
  }, []);

  // useEffect(() => {
  //   const handleNaviagation = () => {
  //     if (!isPrelook) {
  //       if (irData && !isCashReady) {
  //         const IR_STATUS = getIRStatusForDigio(
  //           journeyData?.normalizeData,
  //           false,
  //           false,
  //         );

  //         switch (IR_STATUS) {
  //           case IR_STATUS_FOR_MSG.DORMANT_REVOKED:
  //           case IR_STATUS_FOR_MSG.REKYC_IN_PROGRESS:
  //           case IR_STATUS_FOR_MSG.DORMANCY_IN_PROGRESS:
  //           case IR_STATUS_FOR_MSG.REVOKED:
  //           case IR_STATUS_FOR_MSG.E_SIGN_PENDING:
  //           case IR_STATUS_FOR_MSG.E_SIGN_IN_VERIFICATION:
  //           case IR_STATUS_FOR_MSG.REGISTRATION_PENDING:
  //             navigateUserProfile();
  //             break;
  //           default:
  //             navigateTo(history, MINI_APP_ROUTES.CHECK_KYC, {}, 'replace');
  //         }
  //       } else if (irData && isCashReady && !isFnoReady) {
  //         if (
  //           FOirStatus === IR_STATUS_FOR_MSG.REVOKED &&
  //           FOreadinessData?.revokeSubType ===
  //             REVOKE_SUB_TYPE.SEGMENT_DEACTIVATED
  //         ) {
  //           setShowFOblocked(true);
  //         } else {
  //           navigateTo(history, '/kyc/fno-onboarding', {}, 'replace');
  //         }
  //       }
  //     }
  //   };
  //   if (fnoActivationSuccess) {
  //     if (!isDataRefetching && !isRefetchingIr && !isRefetchingJourney) {
  //       handleNaviagation();
  //     }
  //   } else {
  //     handleNaviagation();
  //   }
  // }, [
  //   irData,
  //   isCashReady,
  //   isFnoReady,
  //   isDataRefetching,
  //   isRefetchingIr,
  //   isRefetchingJourney,
  // ]);

  const {
    state: { currentTab },
  } = useCommonContext();

  useEffect(() => {
    if (currentTab === 0) {
      setPinStocksEnabled(false);
    }
  }, [currentTab]);

  const getPinOverviewCard = () =>
    Context.pinStocksEnabled ? <PinStocksOverview /> : <></>;

  const pulseEventsUtils = {
    isBridge,
    sendEventToBridge,
    IRinvestmentStatus,
  };

  if (
    fnoActivationSuccess &&
    (isDataRefetching || isRefetchingIr || isRefetchingJourney)
  ) {
    return <Loader />;
  }

  if (showFOblocked) {
    return <FOBlocked />;
  }

  const { data: readinessData } = useUserReadiness(true);

  return (
    <>
      {getPinOverviewCard()}
      <PMLFnoApp
        dataFeed={dataFeed}
        authDetails={authDetails}
        appName="MINI_APP"
        YouTubePlayer={YouTubePlayer}
        useBackPress={useBackPress}
        isFnoReady={isFnoReady}
        isIosBuild={isIosBuild}
        isH5={isH5}
        getBridge={getBridge}
        Footer={Footer}
        readinessData={readinessData}
        currentTab={currentTab}
        combineDashboardDeepTypeHelper={combineDashboardDeepTypeHelper}
        pulseEventsUtils={pulseEventsUtils}
        subscriptionData={subscriptionData}
        deepLinkNeedHelp={deepLinkNeedHelp}
        isRevampUser={isRevampUser}
      />
    </>
  );
};
export default FNOLanding;
