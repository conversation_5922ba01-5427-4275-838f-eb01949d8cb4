/* eslint-disable no-undef */
import { PRODUCT, SUB_PRODUCT } from '@utils/Constants';
import { isProduction } from '@src/setting';
import {
  BASE_URL,
  PWC_VALUE,
  env,
  LOGIN_API_HOST,
  LOGIN_HOST,
} from './envConfig';

const {
  EQ_HOST,
  PM_API_HOST,
  PAYMENT_API_HOST,
  KYC_API_HOST,
  STATIC_HOST,
  STATIC_HOST_V2,
  PASSWORD_VERIFIER,
  ORDERS_API_HOST,
  EDTECH_HOST,
  LOGGER,
  EQUITY_PML,
  PLATFORM,
} = BASE_URL;

export const getPlanId = () => {
  if (__ENV__ === 'production') {
    return 1;
  } else if (__ENV__ === 'staging') {
    return 1;
  }
  return 1;
};

export const GENERIC_API_URL = {
  APP_LOG: `${LOGGER}logger/log`,
  TNC_ACCEPT: `${KYC_API_HOST}userprofile/v1/user/tnc-accept`,
  USER_BOOT: userId =>
    `${PM_API_HOST}pm/api/v2/users/boot/${userId}?details=personalDetails`,
  GET_CA_APPROVED_DATA: date =>
    `${EQ_HOST}holdings/v1/get-ca-approved-data?startDate=${date}&endDate=${date}`,
  GET_UPCOMING_CA_APPROVED_DATA: (startDate, endDate) =>
    `${EQ_HOST}holdings/v1/get-ca-approved-data?startDate=${startDate}&endDate=${endDate}`,
  RECENT: type => `${EQ_HOST}data/v2/recent/${type}`,
  SUGGEST: `${EQ_HOST}data/v2/suggest`,
  P_CLOSE: `${EQ_HOST}data/v2/pclose`,
  POPULAR: `${EQ_HOST}data/v2/popular`,
  FULL_READINESS: `${KYC_API_HOST}userprofile/user/user_id/v5/readiness?product=EQUITY,MUTUAL_FUND`,
  RECENT_SEARCHES: `${EQ_HOST}data/v2/recent/searched`,
  READINESS: `${KYC_API_HOST}userprofile/user/user_id/v5/readiness?product=MUTUAL_FUND,EQUITY`,
  READINESS_V5: `${KYC_API_HOST}userprofile/user/user_id/v5/readiness?product=MUTUAL_FUND,EQUITY,NPS`,
  KYC_MESSAGE: `${PM_API_HOST}aggr/kyc/v1/messages`,
  ETF: `${EQ_HOST}data/v2/scrips-list`,
  ETF_POPULAR: `${EQ_HOST}data/v3/popular-by-instrument`,
  GET_ETF_FILTERS: `${EQ_HOST}data/v2/etf-filters`,
  GET_UPCOMING_SIP_INSTRUCTION: `${EQ_HOST}sip/instruction/api/v1/upcoming`,
  GET_UPCOMING_MF_SIP: userID =>
    `${PM_API_HOST}mftransaction/v3/${userID}/upcoming-investment`,
  SCHEDULE_SIP: userID =>
    `${PM_API_HOST}mftransaction/v1/${userID}/sip-schedule`,
  GET_SLEEK_CARD_INFO: `${STATIC_HOST}miniapp-messages.json`,
  GET_AGGREGATOR_DATA: `${PM_API_HOST}aggr/equity/v1/popup`,
  GET_HOME_CONFIG: `https://static.paytmmoney.com/mini-app/home/<USER>
  GET_BRAND_CONFIG: `https://static.paytmmoney.com/data/v1/production/topstocks.json`,
  GET_MARKET_INDICES_CONFIG: `https://static.paytmmoney.com/data/v1/indices/market-indices.json`,
  GET_MARKET_INDICES: `${EQ_HOST}data/v1/indices`,
  POST_SEACH_CLICK: userId =>
    `${EQ_HOST}data/v1/user-event?stockid=${userId}&type=s`,
  CHECK_USER_ACCESS: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/early-access/MINI_CONTROLLED_BETA_FLOW/access/status`,
  PLAN_SELECTION: userId =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/product/${PRODUCT}/platform`,
  INITIATE_PAYMENT: `${KYC_API_HOST}subscription/v2/event/pg`,
  QUERY_PAYMENT_STATUS: clientUniqueTxnId =>
    `${KYC_API_HOST}subscription/v2/query-payment-status/${clientUniqueTxnId}`,
  GET_PLAN_DETAILS: planId =>
    `${KYC_API_HOST}subscription/plan-detail/${planId}`,
  // GET_PLAN_ID: `${KYC_API_HOST}subscription/v2/plan/list`,
  GET_PLAN_ID: `${KYC_API_HOST}subscription/plan?productType=EQ&source=MINI`,
  GET_SUBSCRIPTION_CHARGES: userId =>
    `${KYC_API_HOST}subscription/v3/subscription-data/list/${userId}`,
  VALIDATE_COUPON: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/coupon/validate`,
  CANCEL_PAYMENT: transactionId =>
    `${PAYMENT_API_HOST}payments/api/cancel-transaction/${transactionId}`,
  OPT_IN: userId =>
    `${KYC_API_HOST}onboarding/v6/user/${userId}/product/EQUITY/journey/optin`,
  GET_DEMAT_DATA: ` ${EQ_HOST}backoffice/userInfo/v1/getUser`,
  GET_DOCUMENTS: userId =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/product/EQUITY/documents`,
  SEND_DOCUMENT_EMAIL: (userId, doc_type) =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/product/EQUITY/documents/${doc_type}/send`,
  GET_MODIFICATION_STATUS: (userId, type) =>
    `${KYC_API_HOST}pf-kyc/v1/modify/${userId}/status?type=${type}`,
  SUBMIT_MODIFICATION_DETAILS: userId =>
    `${KYC_API_HOST}pf-kyc/v1/modify/${userId}/submit`,
  GET_KYC_USER_DATA: userId => `${PM_API_HOST}kyc/v1/${userId}`,
  UPDATE_PROFILE_DATA: userId => `${PM_API_HOST}pm/api/v1/users/${userId}`,
  IPV_OTP: userId => `${KYC_API_HOST}pf-kyc/v1/otp/user/${userId}/docType/IPV`,
  GET_OTP: `${KYC_API_HOST}pf-kyc/v1/modify/requestOTP`,
  VALIDATE_OTP: `${KYC_API_HOST}pf-kyc/v1/modify/validateOTP`,
  GET_ADDITIONAL_COMMUNICATION_DATA: (userId, type) =>
    `${KYC_API_HOST}pf-kyc/v1/communication/${userId}/step/${type}/status`,
  IMG_UPLOAD: userId => `${KYC_API_HOST}pf-kyc/v2/${userId}/documents`,
  IR: userId =>
    `${KYC_API_HOST}onboarding/v4/fe/user/${userId}/product/EQUITY/buckets/journey`,
  THEME_BASED: `${BASE_URL.STATIC_FILE_DATA}miniapp-theme-widget.json`,
  CURRENT_PLAN: userId =>
    `${KYC_API_HOST}subscription/v4/get-current-plan/${userId}`,
  GET_CUSTOMER_PLAN: (userId, productType) =>
    `${KYC_API_HOST}subscription/customer/${userId}/plan?productType=${productType}`,
  GET_ACCOUNT_MODIFICATION_STATUS: userId =>
    `${KYC_API_HOST}pf-kyc/v2/${userId}/account-modification/status`,
  EQUITY_INFO_CARD: `${STATIC_HOST}equity-infocard.json`,
  MINI_APP_BLOCKER_LANDING_PAGE: `${STATIC_HOST}mini-app-blocker-landing-page_v4.json`,
  LOGOUT: `${LOGIN_API_HOST}api/auth/logout`,
  EQUITY_DOWNTIME: `${BASE_URL.EQUITY_DOWNTIME}frontend-config.json`,
  GET_ACCOUNT_TIMELINE: (userId, product) =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/product/${product}/account/timeline`,
  GET_PENDING_ORDERS: userId =>
    `${EQ_HOST}orderhold/v1/order/get/regular?clientId=${userId}`,
  GET_ACCESS: `${LOGIN_API_HOST}api/agg/getAccess`,
  GET_REVAMP_COHORT: `https://static.paytmmoney.com/data/v1/production/miniapp-cohort_v5.json`,
  IPO_CURRENT: `${EQ_HOST}/ipo/v1/ipo-current`,
  GET_ACCOUNT_TIMELINE_V2: (userId, product) =>
    `${KYC_API_HOST}onboarding/v2/fe/user/${userId}/product/${product}/account/timeline`,
  GET_PRE_IR_ORDERS: (userId, product) =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/product/${product}/preIr-order-details`,
};

export const INCOME_RAGE_URLS = {
  GET_INCOME_RANGE_LIST: `${PM_API_HOST}kyc/v2/master/incomerange`,
  GET_INCOME_UPDATE_REQUIRED: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/profile-confirmation`,
  UPDATE_INCOME: userId =>
    `${KYC_API_HOST}pf-kyc/v1/${userId}/account-modification/forceupdate/income`,
};

export const SEARCH_REVAMP = {
  RECENT: `${EQ_HOST}data/v3/recent/searched`,
  POPULAR: `${EQ_HOST}data/v4/popular`,
  RESULTS: (query, scope) =>
    `${EQ_HOST}data/v3/suggest?is-advanced-user=true&search-scope=${scope}&q=${query}`,
};

export const PASSCODE_API_URLS = {
  IS_PASSCODE_EXISTS: userId =>
    `${KYC_API_HOST}passcode/v2/user/${userId}/passcode`,
  CREATE_PASSCODE: userId =>
    `${KYC_API_HOST}passcode/v2/user/${userId}/passcode`,
  VALIDATE_PASSCODE: userId =>
    `${KYC_API_HOST}passcode/v2/user/${userId}/passcode/validate`,
};

export const MY_SIP = {
  MF_GENERATE_OTP: userId =>
    `${PM_API_HOST}/mftransaction/v1/${userId}/generateOTP`,
  MF_DELETE_SIP: (userID, sip) =>
    `${PM_API_HOST}/mftransaction/v2/${userID}/sip/${sip}`,
  BUY_MF_V7: userID => `${PM_API_HOST}/mftransaction/v7/${userID}/purchase`,
};
export const PASSWORD_AUTH_API_URLS = {
  VERIFIER_INIT: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/paytm/password/verifier/init`,
  DO_VIEW: `${PASSWORD_VERIFIER}risk/identify/nonstandard/doView`,
  DO_VERIFY: `${PASSWORD_VERIFIER}risk/identify/nonstandard/doVerify`,
};

export const MANAGE_PASSCODE_API_URLS = {
  RESET_PASSCODE: userId =>
    `${KYC_API_HOST}passcode/v2/user/${userId}/passcode/reset`,
  GENERATE_OTP: userId => `${KYC_API_HOST}passcode/v2/user/${userId}/sendOTP`,
  VALIDATE_OTP: (userId, otp) =>
    `${KYC_API_HOST}passcode/v2/user/${userId}/otp/${otp}/validate`,
};

export const PAYMENT_API_URLS = {
  INITIATE_FUND_FMS: `${EQ_HOST}fms/api/v3/funds/payin/initiate`,
  INITIATE_PAYMENT: `${EQ_HOST}fms/api/v1/funds/payin/initiate`,
  INITIATE_TRANSFER: `${EQ_HOST}fms/api/v1/funds/payout/initiate`,
  IS_FIRST_TRANSACTION: txnId =>
    `${EQ_HOST}fms/api/v1/user/payin/isfirsttxn?transaction_id=${txnId}`,
  PAYMENT_OPTIONS: `${PAYMENT_API_HOST}payments/api/v10/payment-option/`,
  PAYMENT_OPTIONS_FIRST_FUND: `${PAYMENT_API_HOST}payments/api/v12/payment-option`,
  PAYMENT_OPTIONS_MF: `${PAYMENT_API_HOST}payments/api/v8/payment-option/`,
  BALANCE_INFO: `${PAYMENT_API_HOST}payments/api/v3/balance-info`,
  MAKE_PAYMENT: `${PAYMENT_API_HOST}payments/api/v4/make-payment`,
  BANK_LIST: userId =>
    `${PM_API_HOST}pm/api/v2/users/${userId}/equity/payout/bank-accounts`,
  PAYMENT_NUDGE: paymentsTxnId =>
    `${PAYMENT_API_HOST}payments/api/v4/pg/status/query/${paymentsTxnId}`,
  VALIDATE_VPA: `${PAYMENT_API_HOST}payments/api/v4/validate-vpa`,
  GET_AMOUNT_LIMIT_SUGGESTION: `${EQ_HOST}fms/api/v4/funds/txn/suggestions`,
  FETCH_UPI_APPS_IOS:
    'https://static.paytmmoney.com/mini-app/data/fetchUPIOptionsIOS.json',
  GET_WITHDRAW_INFO: `${BASE_URL.STATIC_FILE_DATA_ETF}withdraw_infocard.json`,
  GET_PAYOUT_TIMELINE: `${BASE_URL.STATIC_FILE}mini-app/data/withdrawTimelinesConfig.json`,
  INTENT_FLOW_INFO: `${BASE_URL.STATIC_FILE}mini-app/data/intentFlowPayment.json`,
  PAYMENT_OPTIONS_FMS: `${EQ_HOST}fms/api/v1/payment-options`,
};

export const PAYMENT_PAYIN_API_URLS = {
  PAYMENT_OPTIONS: `${PAYMENT_API_HOST}payments/api/v8/payment-option/`,
  PAYMENT_OPTIONS_MF: `${PAYMENT_API_HOST}payments/api/v8/payment-option/`,
  MAKE_PAYMENT: `${PAYMENT_API_HOST}payments/api/v5/make-payment`,
};

export const AUTO_PAY_API_URLS = {
  MANDATE_REG_OPTIONS: userId =>
    `${PM_API_HOST}mandate/api/v2/otm/${userId}/mandate-reg-options`,
  GET_OTM_LIST: userId =>
    `${PM_API_HOST}mandate/api/v2/users/${userId}/otm-list`,
  INITIATE_SUBSCRIPTION: `${PM_API_HOST}mandate/api/v2/otm/initiate-subscription/paytmpg`,
  VALIDATE_VPA: (userId, vpa) =>
    `${PM_API_HOST}mandate/api/v2/user/${userId}/validate-vpa?vpa=${vpa}`,
  INITIATE_MANDATE: `${PM_API_HOST}mandate/api/v2/otm/initiate-mandate-request`,
  PAUSE_RESUME_SUBSCRIPTION: `${PM_API_HOST}mandate/api/v2/paytm-pg/pause-resume-subscription`,
  DELETE_SUBSCRIPTION: `${PM_API_HOST}mandate/api/v2/paytm-pg/cancel-subscription`,
};

export const AUTO_PAY_API_URLS_BBC = {
  MANDATE_REG_OPTIONS: userId =>
    `${PM_API_HOST}mandate/api/v3/otm/${userId}/mandate-reg-options`,
  INITIATE_SUBSCRIPTION: `${PM_API_HOST}mandate/api/v2/otm/initiate-subscription/paytmpg`,
  INITIATE_SUBSCRIPTION_PUSH: `${PM_API_HOST}mandate/api/v3/otm/initiate-subscription/paytmpg`,
  VALIDATE_VPA: (userId, vpa) =>
    `${PM_API_HOST}mandate/api/v2/user/${userId}/validate-vpa?vpa=${vpa}`,
  INITIATE_MANDATE: `${PM_API_HOST}mandate/api/v2/otm/initiate-mandate-request`,
};

export const BANK_TRANSFER_API_URLS = {
  GET_BANK_ACCOUNTS: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/van`,
};

export const CHAT_BOT = {
  ASK: () => {
    if (__ENV__ === 'production') {
      return 'https://screener-ai-answer-engine.paytmmoney.com/api/v1/ask?streaming=true';
    }
    return 'https://screener-ai-answer-engine-stg.internal.production.gm.paytmmoney.com/api/v1/ask?streaming=true';
  }
};

export const STOCKS_API_URLS = {
  GET_USER_HOLDINGS: `${EQ_HOST}holdings/v1/get-user-holdings-data`,
  GET_HOLDINGS_VALUE: `${EQ_HOST}holdings/v1/get-holdings-value`,
  TOP_TRADED_SCRIPS: `${EQ_HOST}scrips/info/v1/topscrips`,
  GET_CA_APPROVED_DATA: `${EQ_HOST}holdings/v1/get-ca-approved-data`,
  GET_PML_DETAIL: `${EQ_HOST}data/v2/isin-pml-details`,
};

export const ACC_STATEMENTS_API = {
  GET_TRADE_DATES: (start_date, end_date) =>
    `${EQ_HOST}backoffice/ext/statements/v2/trade/dates?start_date=${start_date}&end_date=${end_date}&segment=${SUB_PRODUCT}`,
  SEND_CONTRACT_NOTE: date =>
    `${EQ_HOST}backoffice/ext/statements/v2/send/email?date=${date}&segment=${SUB_PRODUCT}`,
  SEND_LEGDER_STATEMENTS: (fromDate, toDate) =>
    `${EQ_HOST}backoffice/ext/statements/v1/ledger/email?fromDate=${fromDate}&toDate=${toDate}`,
  GET_AUCTION_TRADE_DATES: (fromDate, toDate) =>
    `${EQ_HOST}backoffice/ext/statements/v1/auction/trade/dates?start_date=${fromDate}&end_date=${toDate}`,
  SEND_AUCTION_STATEMENT: date =>
    `${EQ_HOST}backoffice/ext/statements/v1/auction/send/email?date=${date}`,
  SEND_ANUAL_LEGDER_STATEMENTS: () =>
    `${EQ_HOST}backoffice/ext/statements/v3/ledger/send/email`,
  SEND_AGTS_STATEMENTS: year =>
    `${EQ_HOST}backoffice/ext/statements/agts/send/email?year=${year}`,
  SEND_TAX_PNL_STATEMENTS: () =>
    `${EQ_HOST}reports/pl/tax/v1/generate-tax-report`,
  SEND_MONTHLY_STATEMENTS: (month, year) =>
    `${EQ_HOST}backoffice/ext/statements/v1/monthlyGlobalStatements/send/email?month=${month}&year=${year}`,
  GET_MONTH_YEAR_LATEST_BILL: `${EQ_HOST}backoffice/ext/statements/v1/get/monthlystatement/latestmonth`,
  GET_WEEKLY_STATEMENTS: (start_date, end_date) =>
    `${EQ_HOST}backoffice/ext/statements/v2/weekly/historical/weeks?start_date=${start_date}&end_date=${end_date}`,
  SEND_WEEKLY_STATEMENTS: (start_date, end_date) =>
    `${EQ_HOST}backoffice/ext/statements/v2/weekly/send/email?week_start_date=${start_date}&week_end_date=${end_date}`,
  SEND_PNL_STATEMENTS: `${EQ_HOST}reports/external/v3/pl/email/report`,
  EMAIL_TRADE_BOOK: (fromDate, toDate, segment) =>
    `${EQ_HOST}backoffice/ext/statements/v1/tradebook/email?from_date=${fromDate}&to_date=${toDate}&segment=${segment}`,
};

export const NOTIF_PREF_API = {
  GET_NOTIF_GROUP: `${PM_API_HOST}communication/v1/optout/preferences?product=${PRODUCT}`,
  UPDATE_NOTIF_GROUP: `${PM_API_HOST}communication/v1/optout/preferences?product=${PRODUCT}`,
};

export const COMPANY_DETAILS_API = {
  GET_COMPANY_DETAILS: company_id =>
    `${EQ_HOST}data/v2/pml-details?ids=${company_id}`,
  GET_PCLOSE_DETAILS: `${EQ_HOST}data/v2/pclose`,
  GET_PEERS: id => `${EQ_HOST}data/v1/peers?id=${id}`,
  GET_OPTION_CHAIN: `${STATIC_HOST_V2}fno-dashboard-search-scrips.json`,
  GET_BREAKOUT_JSON: `${BASE_URL.STATIC_FILE_DATA}breakout-stocks-content.json`,
  GET_INDEX_STOCK_LIST: id =>
    `${EQ_HOST}marketmovers/api/v1/index/gainersandlosers/list?securities=true&index=${id}`,
  GET_SIMILAR_INDICES: id => `${EQ_HOST}data/v1/index-peers?id=${id}`,
  GET_TRANSACTION_DETAILS: isin =>
    `${EQ_HOST}holdings/v1/get-holdings-transaction-details?isin=${isin}`,
  GET_NUDGE_INFO: (security_id, segment, exchange, isin) => {
    if (isin) {
      return `${EQ_HOST}nudge-info/api/v2/scrip?scrip_id=${security_id}&segment=${segment}&exchange=${exchange}&isin=${isin}`;
    }
    return `${EQ_HOST}nudge-info/api/v2/scrip?scrip_id=${security_id}&segment=${segment}&exchange=${exchange}`;
  },
  GET_FNO_OPTION_DETAILS: `${EQ_HOST}data/v2/symbol-pml-details`,
  GET_PML_ID: (securityId, exchange, segment) =>
    `${EQ_HOST}data/v2/secid-x-pml-details?sec-id=${securityId}&x=${exchange}&s=${segment}`,
  GET_RECOMMENDATION_DETAILS: isin =>
    `${EQ_HOST}company/api/v1/report/recommendation/${isin}`,
  GET_SHARE_HOLDING_PATTERN_DETAILS: pmlId =>
    `${EQ_HOST}company/api/v1/share/holding-pattern?pml_id=${pmlId}`,
  GET_BREAKOUT_DETAILS_FOR_STOCK: (pmlId, pageSize = 50) =>
    `${EQ_HOST}data/v1/breakouts/details?pmlId=${pmlId}&pageSize=${pageSize}`,
  GET_FUNDAMENTALS_TTM_DATA: pmlId =>
    `${EQ_HOST}data/v2/fundamentals-ttm?type=c&id=${pmlId}`,
  GET_SCORE_DATA: pmlId =>
    `${EQ_HOST}company/api/v1/report/score/${pmlId}?dataType=full`,
  GET_NEWS_LIST: isin => `${EQ_HOST}data/v1/conciseEquityNews?isins=${isin}`,
  GET_NEWS_DETAILS: (isin, sno) =>
    `${EQ_HOST}data/v1/detailEquityNews?isin=${isin}&sno=${sno}`,
  GET_PIVOT_POINTS_DETAILS: (type = 'classic', pmlId) =>
    `${EQ_HOST}company/api/v1/technical/pivot/${type}?pml_id=${pmlId}`,
  GET_MOVING_AVERAGE_DETAILS: pmlId =>
    `${EQ_HOST}company/api/v1/technical/ratio?pml_id=${pmlId}`,
  GET_FNO_DETAILS: (symbol, expiry, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v1/top-option?symbol=${symbol}&expiry=${expiry}&exchange=${exchange}`,
  GET_FNO_FUTURE_CONTRACTS_DETAILS: (symbol, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v2/future-contracts?symbol=${symbol}&exchange=${exchange}`,
};

export const FNO_ENDPOINTS = {
  FNO_OPTIONS_CONFIG: (symbol, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v2/option-chain/config?symbol=${symbol}&exchange=${exchange}`,
  FNO_OPTIONS: (symbol, expiry, type, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v2/option-chain?symbol=${symbol}&expiry=${expiry}&type=${type}&exchange=${exchange}`,
  FNO_TRENDS_INDICATOR: (symbol, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v2/futures/builtup?symbol=${symbol}&size=1&exchange=${exchange}`,
};

export const MY_FUNDS_DETAILS_API = {
  GET_FUND_SUMMARY: `${EQ_HOST}fms/api/v5/funds/summary`,
  GET_FUND_SUMMARY_DETAIL: `${EQ_HOST}fms/api/v1/funds/summary/detailed`,
  GET_TRANSACTION_SUMMARY: `${EQ_HOST}fms/api/v2/funds/txns`,
  GET_SINGLE_TRANSACTION_SUMMARY: `${EQ_HOST}fms/api/v2/funds/txn`,
  GET_QUARTERLY_MESSAGE_DETAIL: `${EQ_HOST}fms/api/v1/funds/qs/msg`,
  GET_ORDER_FUNDS_SUMMARY: `${EQ_HOST}fms/api/v1/orderpad/funds/summary`,
  GET_ORDER_FUNDS_SUMMARY_V2: `${EQ_HOST}fms/api/v2/orderpad/funds/summary`,
};
export const EQUITIES = {
  GET_MARKET_STATUS: `${EQ_HOST}exchange/mkt/v2/status`,
  ORDERS_HOLD: {
    PLACE_REGULAR: `${EQ_HOST}orderhold/v1/order/place/regular`,
    CANCEL_REGULAR: `${EQ_HOST}orderhold/v1/order/cancel/regular`,
  },
  ORDERS: {
    PLACE: {
      POST_REGULAR: `${ORDERS_API_HOST}order/txn/v2/place/regular`,
      POST_BRACKET: `${ORDERS_API_HOST}order/txn/v1/place/bracket`,
      POST_COVER: `${ORDERS_API_HOST}order/txn/v1/place/cover`,
    },
    MODIFY: {
      REGULAR: `${ORDERS_API_HOST}order/txn/v2/modify/regular`,
      BRACKET: `${ORDERS_API_HOST}order/txn/v1/modify/bracket`,
      COVER: `${ORDERS_API_HOST}order/txn/v1/modify/cover`,
    },
    CONVERT: {
      POST: `${ORDERS_API_HOST}order/txn/v2/convert/regular`,
    },
    EXIT: {
      BRACKET: `${ORDERS_API_HOST}order/txn/v1/exit/bracket`,
      COVER: `${ORDERS_API_HOST}order/txn/v1/exit/cover`,
    },
  },
  CHARGES_INFO: {
    POST: `${EQ_HOST}fms/api/v1/charges/info`,
  },
  POSITIONS: `${ORDERS_API_HOST}order/info/v1/position`,
  POSITIONS_INTEROP: `${ORDERS_API_HOST}order/info/v1/interops/position`,
  POSITIONS_DETAILS: `${ORDERS_API_HOST}order/info/v1/positiondetails`,
  ORDER_HISTORY_INTEROP: `${ORDERS_API_HOST}order/info/v1/interops/positiondetails`,
  GET_PML_ID: `${EQ_HOST}data/v2/isin-pml-details`,
};

export const PRICE_ALERT = {
  ADD_PRICE_ALERT: `${EQ_HOST}data/v1/price-alert`,
  GET_PRICE_ALERT_DETAILS: id => `${EQ_HOST}data/v1/get-price-alert?id=${id}`,
  DELETE_PRICE_ALERT: id => `${EQ_HOST}data/v1/delete-price-alert?doc_id=${id}`,
  GET_PRICE_ALERT_SUMMARY: `${EQ_HOST}data/v1/get-agg-price-alert`,
};

export const PWC_API_URLS = {
  EVENTS: `https://api.insider.in/paytm-money-wealth-community/homepage?brand_id=${PWC_VALUE.BRAND_ID}&count=5`,
  LIST_EVENTS: `https://api.insider.in/paytm-money-wealth-community/list`,
};

export const MARKET_MOVERS_URLS = {
  GET_CONFIG: `${EQ_HOST}marketmovers/api/v1/config`,
  GET_V2_CONFIG: `${EQ_HOST}marketmovers/api/v2/config`,
  HOME_PAGE: (key, scope, mIndex, exchange) =>
    scope === 'market'
      ? `${EQ_HOST}marketmovers/api/v1/home/<USER>
      : `${EQ_HOST}marketmovers/api/v1/home/<USER>
  MOVERS_TAB: (key, index, exchange) =>
    `${EQ_HOST}marketmovers/api/v1/${key}?exchange=${exchange}&index=${index}&count=25`,
  MOST_INVESTED: `${EQ_HOST}scrips/info/v1/topscrips`,
  TOP_MARKET_MOVERS: (
    moverName,
    scope = 'market',
    count,
    currentSelectedIndex = 'nifty_50',
    currentSelectedExchange = 'nse',
  ) =>
    `${EQ_HOST}marketmovers/api/v1/home/<USER>
  POPULAR_MARKET_MOVERS: (pageNo = 1, instrumentType = 'ES', pageSize = 10) =>
    `${EQ_HOST}data/v3/popular-by-instrument?instrument-type=${instrumentType}&page-size=${pageSize}&page-no=${pageNo}`,
  MRKET_HIGH_LOW_MOVERS: (
    category,
    count = 25,
    exchange = 'nse',
    index = 'nifty_50',
  ) =>
    `${EQ_HOST}marketmovers/api/v2/stocks?exchange=${exchange}&index=${index}&category=${category}&count=${count}`,
};

export const MOST_BOUGHT_URLS = {
  MOST_BOUGHT_CONFIG: (count = 4) =>
    `${PM_API_HOST}aggr/equity/market-movers/v1/widgets?keys=most-bought-widget&itemCounts=${count}`,
  MOST_BOUGHT_CATEGORY_FETCH: (
    category = 'most_bought_eq_delivery_order',
    filter,
  ) => {
    let url = `${PM_API_HOST}aggr/equity/market-movers/v1/securities?identifierName=${category}`;
    // Add filterPrice query param only if filter is provided
    if (filter) {
      url += `&filterPrice=${filter}`;
    }
    return url;
  },
};

export const FAV_API_URLS = {
  GET_ALL_WATCHLIST: () => `${EQ_HOST}marketwatch/api/v2/watchlist?verbose=1`,
  CREATE_WATCHLIST: () => `${EQ_HOST}marketwatch/api/v1/watchlist`,
  RENAME_WATCHLIST: watchlist_id =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/rename`,
  DELETE_WATCHLIST: watchlist_id =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}`,
  REMOVE_SECURITY_FRM_WATCHLIST: (watchlist_id, security_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security/${security_id}`,
  ADD_SECURITY_TO_WATCHLIST: watchlist_id =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security`,
  GET_SECURITIES_IN_WATCHLIST: watchlist_id =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security`,
  GET_SECURITIES_AT_WATCHLIST_INDEX: index =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/index/${index}`,
  REORDER_WATCHLIST: `${EQ_HOST}marketwatch/api/v1/watchlist/reorder`,
  REORDER_WATCHLIST_STOCKS: id =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${id}/security/reorder`,
};

export const PIN_STOCKS_URLS = {
  GET_ALL_PINNED_STOCKS: `${EQ_HOST}pinned-stocks/api/pinned-watchlist`,
  ADD_SECURITY_TO_PINNED_STOCKS: `${EQ_HOST}pinned-stocks/api/v1/pinned-watchlist/security`,
  REORDER_PINNED_STOCKS: `${EQ_HOST}pinned-stocks/api/v1/pinned-watchlist/security/reorder`,
  REMOVE_SECURITY_FRM_PINNED_STOCKS: security_id =>
    `${EQ_HOST}pinned-stocks/api/v1/pinned-watchlist/security/${security_id}`,
  ADD_SECURITY_TO_PINNED_STOCKS_BULK: `${EQ_HOST}pinned-stocks/api/v1/pinned-watchlist/security/bulk`,
  STATIC_PINNED_STOCKS:
    'https://static.paytmmoney.com/mini-app/data/pin-stocks.json',
};

export const LEDGER_HISTORY_API_URL = {
  LEDGER_TRANSACTION: `${EQ_HOST}backoffice/ext/statements/v2/ledger/transactions`,
  MTF_LEDGER_TRANSACTION: (obirStatus, isMtfEnabled) =>
    `${EQ_HOST}backoffice/ext/statements/v1/ledger/mtf/transactions?obir_status=${obirStatus}&is_mtf_enabled=${isMtfEnabled}`,
};

export const PWA_API_URLS = {
  DASHBOARD_DATA: `${EDTECH_HOST}aggr/v2/mkp-dashboard-cards?requestSourceType=EQUITY_MINI_APP`,
};

export const ETF_WIDGET_URL = {
  ETF_WIDGET: 'https://static.paytmmoney.com/mini-app/etf_widget.json',
};

const homeStoreFront = () => {
  if (env === 'production')
    return 'https://storefront.paytmmoney.com/v2/h/paytmstocks-equity-homepage';
  return 'https://storefront.paytmmoney.com/v2/h/stg-paytmstocks-miniapp-homepage-v1?sfnode=node';
};

const homeStoreFrontBackUp = () => {
  if (env === 'production')
    return 'https://static.paytmmoney.com/mini-app/sf-backup-v8.json';
  return 'https://static.paytmmoney.com/mini-app/stg/sf-backup-stg-v7.json';
};

const storeFrontStocksConfig = () => {
  if (env === 'production')
    return 'https://storefront.paytmmoney.com/v2/h/bbc-stocks-config';
  return 'https://storefront.paytmmoney.com/v2/h/bbc-stocks-config?sfnode=node';
};

export const STORE_FRONT_URL = {
  HOME: homeStoreFront(),
  BACKUP: homeStoreFrontBackUp(),
  CONFIG: storeFrontStocksConfig(),
};

/**
 * @TODO remove STORE_FRONT_ID mapping later
 */
export const STORE_FRONT_ID = {
  BANNER: env === 'production' ? 321568 : 321093,
};

export const COUPON = {
  APPLIED: (userId, source) =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/coupon/applied?source=${source}`,
  VALIDATE: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/coupon/validate`,
  REMOVE: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/coupon/remove`,
};

export const PNL_API_URLS = {
  GET_IDENTIFIER: `${EQ_HOST}reports/external/v1/inapp-pl/generate-identifier`,
  GET_PNL_DATA: identifier =>
    `${EQ_HOST}reports/external/v1/inapp-pl/poll?id=${identifier}`,
};

export const downloadNudgeSF = () => {
  if (isProduction) {
    return 'https://storefront.paytmmoney.com/v2/h/add_fund_post_esign';
  }
  return 'https://storefront.paytmmoney.com/v2/h/add_fund_post_esign';
};

export const POPULAR_MUTUAL_FUND_API = (pageNumber, bucket) =>
  `${PM_API_HOST}mf/v1/funds-bucket-wise?pageNumber=${pageNumber}&pageSize=5&bucket=${bucket}`;

export const MF_PORTFOLIO = userId =>
  `${PM_API_HOST}portfolio/v1/${userId}/mf-if`;

export const TWO_FA_URL = {
  POSSESSION_FACTOR_LOGIN: `${KYC_API_HOST}pf-kyc/2fa/possession-factor-login-allowed`,
  GENERATE_OTP: userId => `${KYC_API_HOST}2fa/otp/user/${userId}/otp/send`,
  VERIFY_OTP: `${KYC_API_HOST}2fa/otp/user/otp/verify`,
  REGENERATE_OTP: userId => `${KYC_API_HOST}2fa/otp/user/${userId}/otp/resend`,
};

export const FO_TNC_URL = {
  TNC_ACCEPT: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/tnc-accept`,
  FO_RISK_DISCLOSURE_DATA:
    'https://static.paytmmoney.com/mini-app/data/fno-risk-popup-new.json',
  // 'https://static.paytmmoney.com/mini-app/data/fnoTnc.json',
};

export const AUTOPAY_COLLECT_FLOW_URL = `${PM_API_HOST}payments/api/redirect/paytmpg?postUrl=`;

export const AGGREGATOR_API = {
  COMBINED_POPUP: `${process.env.APP_BASE_URL_3 ||
    'https://api-staging.paytmmoney.com'}/aggr/v1/combined-popup`,
  COMBINED_DASHBOARD: `${process.env.APP_BASE_URL_3 ||
    'https://api-staging.paytmmoney.com'}/aggr/home/<USER>/combined-dashboard`,
  STOCKS_DASHBOARD: `${process.env.APP_BASE_URL_3 ||
    'https://api-staging.paytmmoney.com'}/aggr/equity/stocks/v2/dashboard`,
  COMBINED_DASHBOARD_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/combined-dashboard-v4-fallback.json',
  STOCKS_DASHBOARD_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/equity-dashboard-v2-fallback.json',
  ETF: `${PM_API_HOST}aggr/equity/etf/v1/dashboard`,
  ETF_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/etf-dashboard-v1-fallback.json',
  IPO: `${PM_API_HOST}aggr/equity/ipo/v1/dashboard`,
  IPO_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/ipo-dashboard-v1-fallback.json',
  WIDGETS:`${process.env.APP_BASE_URL_3 ||
    'https://api-staging.paytmmoney.com'}/aggr/journey/v1/widgets?businessType=EQ_TRADE_CNF`
};

export const GET_SCRIPS_LIST = `${EQ_HOST}data/v3/scrips-list`;
export const GET_SCRIPS_LIST_AGGR = `${PM_API_HOST}aggr/equity/market-movers/v1/securities`;
export const GET_CHARTS_IMAGE = (range = '1w') =>
  `${EQUITY_PML}ssr-charts/v4/price?format=png&mode=light&range=${range}`;
export const GET_CHARTS_IMAGE_NEW = `${EQUITY_PML}ssr-charts/v5/price?mode=light`;
export const COMBINED_ADD_FUNDS_ORDER = `${PLATFORM}/onboarding/v1/first-buy-order/initiate`;
export const GET_CHARTS_IMAGE_1W = `${EQUITY_PML}ssr-charts/v4/price?format=png&mode=light&range=1w`;

export const MINI_APP_ROUTES = {
  COMPANY_FUNDAMENTALS: '/company-fundamentals',
  COMBINED_DASHBOARD: '/',
  EQUITY: '/equity',
  EQUITY_DASHBOARD: '/equity-dashboard', // route for revamp equity dashboard
  FUNDS_PAGE: '/equity?activeTab=funds',
  COMPANY: '/company',
  COMPANY_REVAMP: '/company-revamp',
  LEDGER_HISTORY_REVAMP: '/ledger-history-revamp',
  ORDER_CONFIRMATION: '/order-confirmation',
  PRICE_ALERT_SUMMARY: '/price-alert-summary',
  PASSCODE: '/passcode',
  TWO_FA_OTP: '/twofaotp',
  MANAGE_PASSCODE: '/manage-passcode',
  LEARN: '/learn',
  KYC: '/kyc',
  IPV: '/ipv',
  MY_STOCKS: '/my-stocks',
  ORDERS: '/myorders',
  MY_FUNDS: '/my-funds',
  TRANSACTION_RECEIPT: '/transaction-receipt',
  TRANSFER_MONEY: '/transfer-money',
  PAYMENT: '/payment',
  ORDER_PAD: '/order-pad',
  ORDER_PAD_LITE: '/order-pad-lite',
  PLACE_ORDER_BUY: '/place-order-buy',
  PLACE_ORDER_SELL: '/place-order-sell',
  PLACE_ORDER_ROUTE_RESOLVER: '/place-order-resolver',
  EDIS_AUTH: '/edis-auth',
  ETF: '/etf',
  ETF_HOME: '/etf-home',
  ETF_REVAMP: '/etf-revamp',
  KYC_PENDING: '/kyc-pending',
  PRICING: '/pricing',
  PROFILE_EDIT: '/profile-edit',
  MANAGE_COMMUNICATION: '/manage-communication',
  NOTIFICATION_PREFERENCES: '/notification-preferences',
  DEMAT_ACCOUNT: '/demat-account',
  ACCOUNT_STATEMENTS: '/account-statements',
  STATEMENTS_PAGE: '/statements-download',
  DOC_DOWNLOAD_CENTER: '/docs-download-center',
  PROFILE: '/profile',
  CHECK_KYC: '/check-kyc',
  PWC: '/pwc',
  BULK_AUTHORISATION: '/bulk-auth',
  ADD_MONEY: '/add-money',
  POSITIONS: '/positions',
  POSITION_DETAILS: '/position-details',
  CONVERT_ORDER: '/convert-order',
  SIP: '/sip',
  SIP_CONFIRMATION: '/sip-confirmation',
  COMPANY_INSTRUCTIONS: '/company-instructions',
  COMPANY_INSTRUCTIONS_REVAMP: '/company-instructions-revamp',
  AUTO_PAY: '/auto-pay',
  BANK_ACCOUNTS: '/bank-accounts',
  INITIATE_AUTO_PAY: '/init-auto-pay',
  AUTO_PAY_CONFIRMATION: '/auto-pay-confirmation',
  BANK_TRANSFER: '/bank-transfer',
  FUND_DETAILS: '/fund-details',
  LEDGER_HISTORY: '/ledger-history',
  FNO: '/fno',
  FNO_DASHBOARD: '/fno/dashboard',
  BROKERAGE_CALCULATOR: '/brokerage-calculator',
  MARGIN_CALCULATOR: '/margin-calculator',
  EXIT_POSITIONS: '/exit-positions',
  EXIT_POSITIONS_V2: '/exit-positions-v2',
  MARGIN_PLEDGE: '/margin-pledge',
  HOLDING_PAGE_COMPANY: '/company-holding',
  CHARTS: '/charts',
  PINSTOCKS: '/pinstocks',
  OTP_CONSENT: '/otp-consent',
  MARKET_INDICES: '/market-indices',
  MARKET_INDICES_REVAMP: '/market-indices-revamp',
  MARKET_MOVERS: '/market-movers',
  MOST_BOUGHT: '/most-bought',
  ERROR_PAGE: '/error',
  MY_SIP: '/my-sip',
  UPCOMING_SIP: '/upcoming-sip',
  MINI_APP_DEEPLINK: '/mini-app-deeplink',
  FO_RISK_DISCLOSURE: '/fo-risk-disclosure',
  EDIT_WATCHLIST: '/edit-watchlist',
  ORDER_CONFIRMATION_POPUP: '/order-confirmation-popup',
  REVAMP_COMBINE: '/revamp-combine',
  REVAMP_STOCKS: '/revamp-stocks',
  MANAGE_PINSTOCKS: '/manage-pinstocks',
  FIRST_INVESTMENT: '/first-investment',
  FIRST_FUND_ADDITION_STATUS: '/first-fund-add-status',
  COOKIE_VALIDATION: '/cookie-test',
  FIRST_INVESTMENT_ORDER_CONFIRMATION: '/first-order-confirmation',
  PORTFOLIO: '/portfolio',
  PNL: '/pnl',
  PNL_DETAILS: '/pnl-details',
  ALL_TRANSACTION_HISTORY: '/transaction-history',
  MY_FUNDS_V2: '/myfunds',
  IPO_HOME: '/ipo-home',
  PRODUCT_VIEW_ALL: '/allProducts',
  POPULAR_MF: '/popular-mf',
  ALL_EVENTS: '/all-events',
  PRE_IR_TRADE: '/pre-ir-trade',
  PRE_IR_TRADE_ORDER_CONFIRMATION: '/pre-ir-order-confirmation',
  PRE_IR_TRADE_ORDERS: '/pre-ir-orders',
  CHAT_BOT: '/chatbot',
  RESULT_VS_EXPECTATIONS: '/result-expectation-page',
  EQUITY_TRANSACTION_DETAILS: '/equity-transaction-detail',
  PML_APP_DOWNLOAD: '/pml-app-download',
};

export const GOLD_DEEPLINK = 'paytmmp://gold';
export const PWC_EVENT_DEEPLINK =
  'paytmmp://events?insiderH5Url=https://h5.insider.in/frontstage';

// export const FNO_TNC_DEEPLINK =
//   'paytmmp://mini-app?aId=11a4273a4ae946f78898bc0a09e1cd3a&data=ewogICJwYXJhbXMiOiAiP3N0c0Jhckh0PXRydWUiLAogICJwYXRoIjogImZuby10bmMiLAogICJzcGFyYW1zIjogewogICAgInNob3dUaXRsZUJhciI6IGZhbHNlLAogICAgImNhblB1bGxEb3duIjogZmFsc2UKICAgIH0KfQ==';

export const SCREEN_NAME = {
  [MINI_APP_ROUTES.EQUITY]: 'stocks homepage',
  [MINI_APP_ROUTES.COMBINED_DASHBOARD]: 'combined homepage',
  SEARCH: 'Search',
  [MINI_APP_ROUTES.ETF]: 'ETF Page',
  FAVOURITE: 'Favourite page',
  MY_STOCKS: 'My Stocks page',
  [MINI_APP_ROUTES.COMPANY]: 'Company page',
  [MINI_APP_ROUTES.MARKET_INDICES]: 'Market Indices Page',
};

export const BUYBACKS_EVOTING_DEEPLINK = `paytmmp://mini-app?aId=11a4273a4ae946f78898bc0a09e1cd3a&data=ewogICJwYXJhbXMiOiAiP3N0c0Jhckh0PXRydWUiLAogICJwYXRoIjogIiIsCiAgInNwYXJhbXMiOiB7CiAgICAic2hvd1RpdGxlQmFyIjogZmFsc2UsCiAgICAiY2FuUHVsbERvd24iOiBmYWxzZQogICAgfQp9`;
export const PML_DOWNLOAD_APP_LINK =
  'https://paytmmoney.onelink.me/9L59/hp26c91p';

export const getKycConfigURL = () => {
  const CONFIG_URL_PROD =
    'https://static.paytmmoney.com/mini-app/kyc/hybrid-kyc.json';
  const CONFIG_URL_BETA =
    'https://static.paytmmoney.com/mini-app/kyc/hybrid-kyc.json';
  const CONFIG_URL_STG =
    'https://static.paytmmoney.com/mini-app/kyc/stg-hybrid-kyc.json';
  if (__ENV__ === 'production') {
    return CONFIG_URL_PROD;
  } else if (__ENV__ === 'beta') {
    return CONFIG_URL_BETA;
  } else if (__ENV__ === 'staging') {
    return CONFIG_URL_STG;
  }
  return CONFIG_URL_PROD;
};

export const PII_MASKED_URLS = {
  VALIDATE_VPA: `${PM_API_HOST}mandate/api/v2/user/userId/validate-vpa?vpa=######`,
};

export const LOGIN_URL = `${LOGIN_HOST}?returnUrl={redirectURL}`;

export const TRADING_IDEAS_URL = {
  JAMOON_FALLBACK_URL:
    env === 'staging'
      ? 'https://static.paytmmoney.com/data/staging/v1/jamoon_fallback.json'
      : 'https://static.paytmmoney.com/data/production/v1/jamoon_fallback.json',
  TRADING_IDEAS_URL: `${EQ_HOST}advisor-api/jamoon/getResult`,
  ADVISOR_JSON:
    env === 'staging'
      ? 'https://static.paytmmoney.com/data/v1/stage/advisors.json'
      : 'https://static.paytmmoney.com/data/v1/production/advisors.json',

  GET_TNC_STATUS: userId =>
    `${KYC_API_HOST}userprofile/v1/user/${userId}/tnc-accept/verify?tncType=RAPARTNER`,
  ADVISORY_DEFINATIONS:
    'https://static.paytmmoney.com/data/v1/production/advisory-definitoins.json',
};
export const REVAMP_ACCOUNTS_DEEPLINK =
  'paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=ewogICJwYXJhbXMiOiAiP3N0c0Jhckh0PXRydWUmb3JpZ2luPVBBWVRNIiwKICAicGF0aCI6ICIvbWluaS1hcHAvYWNjb3VudHMiLAogICJzcGFyYW1zIjogewogICAgInNob3dUaXRsZUJhciI6IGZhbHNlLAogICAgImNhblB1bGxEb3duIjogZmFsc2UsCiAgICAgImlzQ2FuY2VsbGFibGUiOmZhbHNlCiAgICB9Cn0=';

export const REVAMP_AUTO_PAY_DEEPLINK =
  'paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=ewogICJwYXJhbXMiOiAiP3N0c0Jhckh0PXRydWUmb3JpZ2luPVBBWVRNIiwKICAicGF0aCI6ICIvbWluaS1hcHAvcGF5bWVudHMvYXV0by1wYXkiLAogICJzcGFyYW1zIjogewogICAgInNob3dUaXRsZUJhciI6IGZhbHNlLAogICAgImNhblB1bGxEb3duIjogZmFsc2UsCiAgICAgImlzQ2FuY2VsbGFibGUiOmZhbHNlCiAgICB9Cn0=';
export const SLEEK_CARD_URL = {
  APP_HOME: 'https://static.paytmmoney.com/data/v2/production/app-home.json',
  EQUITY_INFO_CARD:
    'https://static.paytmmoney.com/data/v1/production/equity-infocard.json',
};

const getPfHost = () => {
  const BASE_API_URL_PF_PROD = 'https://api-pf.paytmmoney.com/';
  const BASE_API_URL_PF_STG = 'https://pf-stg.paytmmoney.com/';
  if (env === 'staging') {
    return BASE_API_URL_PF_STG;
  }
  return BASE_API_URL_PF_PROD;
};
export const PF_HOST = getPfHost();
export const MTF_API_URL = {
  COMBINED_IR_STATUS: `${PM_API_HOST}aggr/equity/home/<USER>/dashboard?type=investor`,
  MTF_IR_STATUS: userID =>
    `${PF_HOST}onboarding/v1/fe/user/${userID}/product/EQUITY/subProduct/MTF/investmentReadiness/details`,
  PLANS: `${PF_HOST}subscription/plan?productType=MTF&source=MAIN`,
  ONBOARDING_STOREFRONT: `https://static.paytmmoney.com/mini-app/data/mtf-sf-data.json`,
  ACTIVATE_MTF: userID =>
    `${PF_HOST}onboarding/v1/fe/user/${userID}/product/EQUITY/mtf/activate`,
  SCRIPS_LIST: `${EQ_HOST}mtf/order/api/v1/scrips`,
  PLEDGE_DETAILS: `${PM_API_HOST}aggr/mtf/v1/pledge/holdings`,
  PLEDGE_CDSL_REQUEST: `${PM_API_HOST}aggr/mtf/v1/pledge/cdsl-request`,
  MTF_SCRIP: `${EQ_HOST}mtf/order/api/v1/scrip`,
  IS_MTF_SCRIP_ELIGIBLE: (security_id, exchange) =>
    `${EQ_HOST}mtf/order/api/v1/scrip?scrip_id=${security_id}&exchange=${exchange}`,
  IS_USER_MTF_ELIGIBLE: userId =>
    `${KYC_API_HOST}onboarding/v1/fe/user/${userId}/account/status`,
};
