/* eslint-disable no-undef */
import React from 'react';

import { MINI_APP_ROUTES } from '@src/config/urlConfig';
import { goBack } from '@src/services/coreUtil';
import history from '@src/history';

import GenericErrorPage from '../components/GenericErrorPage/GenericErrorPage';
import { sendErrorToBackend } from '../actions/runtime';
import { chunkLoadErrorHandler, ERROR_CODES } from '../utils/errorUtils';
import { navigateHome } from '../utils/navigationUtil';
import { exitApp } from '../utils/bridgeUtils';

const windowHeight = window.innerHeight;
const isProd = __BUILD__ === 'prod';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: undefined };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error };
  }

  // eslint-disable-next-line class-methods-use-this
  componentDidCatch(error) {
    if (
      error.name === ERROR_CODES.CHUNK_LOAD.NAME ||
      error.code === ERROR_CODES.CHUNK_LOAD.CODE
    ) {
      chunkLoadErrorHandler();
    }
  }

  handleBackPress = () => {
    this.setState({ hasError: false, error: undefined });
    if (window.location.pathname === '/') {
      exitApp();
    } else if (window.location.pathname === MINI_APP_ROUTES.ERROR_PAGE) {
      history.location.state = null;
      goBack(history);
    } else {
      navigateHome({});
    }
  };

  render() {
    if (this.state.hasError) {
      if (isProd) {
        sendErrorToBackend({
          data: JSON.stringify({
            route: window.location.href,
            error: this.state.error.stack.toString(),
          }),
          level: 'fatal',
          key: history.location.state?.errorKey || 'fatal_error',
        });
      }
      return (
        <GenericErrorPage
          cardHeight={windowHeight - 95}
          error={!isProd && this.state.error.toString()}
          errorBoundary
          noCard
          onBackPress={this.handleBackPress}
        />
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
