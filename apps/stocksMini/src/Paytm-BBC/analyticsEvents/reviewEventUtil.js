import {
  NAMES,
  SCREEN,
  EVENT_TYPE,
  USER_ACTIONS,
} from '@src/Paytm-BBC/analyticsEvents/reviewSipScreen';
import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import { FLOW_TYPES } from '@src/Paytm-BBC/utils/Constants';
import { sendEventToAppsFlyer } from '@src/utils/bridgeUtils';
import { OPT_IN_EVENT_BBC } from '@src/config/common';
import { ROUTE_NAMES } from '../routes/config/urlConfig';
import { IR_STATUS_ENUM } from '../config/enums';

const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

const getStatusFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STATUS_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.STATUS_SCREEN_MF;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.STATUS_SCREEN_COMBO;
      break;

    default:
      break;
  }
  return landingScreen;
};
const getSipSuccessFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STOCK_SIP_SUCCESS;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.MF_SIP_SUCCESS;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.COMBO_SIP_SUCCESS;
      break;

    default:
      break;
  }
  return landingScreen;
};
const getFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.REVIEW_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.REVIEW_SCREEN_MF;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.REVIEW_SCREEN_COMBO;
      break;

    default:
      break;
  }
  return landingScreen;
};
const getVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAMES.STOCK_CT_SUMMARY_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAMES.MF_CT_SUMMARY_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAMES.COMBO_CT_SUMMARY_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
const getStatusVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAMES.STOCK_STATUS_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAMES.MF_STATUS_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAMES.COMBO_STATUS_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
const getSipSuccessVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAMES.STOCK_SIP_SUCCESS;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAMES.MF_SIP_SUCCESS;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAMES.COMBO_SIP_SUCCESS;
      break;

    default:
      break;
  }
  return verticalName;
};
export const sendReviewPageOpenEvent = (
  flowType,
  sipAmount,
  cartDetails,
  mfCartDetails,
  Total_Bucket_value,
  totalBucketQty,
  Total_remaining_Value,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.REVIEW_SIP_PAGE_OPENED,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Mf_cart: mfCartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.REVIEW_SIP_PAGE_OPENED,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.REVIEW_SIP_PAGE_OPENED,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.REVIEW_SIP_PAGE_OPENED,
      { Value_selected: sipAmount },
      { Mf_cart: mfCartDetails },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  }
};
export const sendLearnMoreOpenEvent = (flowType, irStatus) => {
  const isReviewScreens = window.location.pathname.includes(ROUTE_NAMES.REVIEW);
  const getIrstatus = getIrstatusType(irStatus);

  const userAction = isReviewScreens
    ? USER_ACTIONS.LEARN_MORE_CLICKED
    : USER_ACTIONS.LEARN_MORE_CLICK;

  const screenname = isReviewScreens
    ? SCREEN.REVIEW_SCREEN
    : SCREEN.STATUS_SCREEN;
  const verticalName = isReviewScreens
    ? getVerticalNameType(flowType)
    : getStatusVerticalNameType(flowType);

  sendAnalyticsEventBBC(verticalName, screenname, getIrstatus, userAction);

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      screenname,
      getIrstatus,
      userAction,
    );
};
export const sendListOfChargesEvent = (flowType, irStatus) => {
  const isReviewScreens = window.location.pathname.includes(ROUTE_NAMES.REVIEW);
  const getIrstatus = getIrstatusType(irStatus);
  if (isReviewScreens) {
    const landingScreen = getFlowType(flowType);
    const verticalName = getVerticalNameType(flowType);

    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.LIST_OF_CHARGES_CLICKED,
    );

    if (flowType === FLOW_TYPES.STOCKS)
      sendAnalyticsEventBBC(
        NAMES.VERTICAL_NAME,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.LIST_OF_CHARGES_CLICKED,
      );
  } else {
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.OPTIN_MODAL,
      getIrstatus,
      USER_ACTIONS.LIST_OF_CHARGES_CLICKED,
    );
  }
};

export const sendtncClickEvent = (flowType, irStatus) => {
  const isReviewScreens = window.location.pathname.includes(ROUTE_NAMES.REVIEW);
  const getIrstatus = getIrstatusType(irStatus);

  if (isReviewScreens) {
    const landingScreen = getFlowType(flowType);
    const verticalName = getVerticalNameType(flowType);

    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.TNC_CLICKED,
    );

    if (flowType === FLOW_TYPES.STOCKS)
      sendAnalyticsEventBBC(
        NAMES.VERTICAL_NAME,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.TNC_CLICKED,
      );
  } else {
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.OPTIN_MODAL,
      getIrstatus,
      USER_ACTIONS.TNC_CLICKED,
    );
  }
};
export const sendOpenAccountCtaEvent = (
  flowType,
  sipAmount,
  cartDetails,
  mfCartDetails,
  Total_Bucket_value,
  totalBucketQty,
  Total_remaining_Value,
  irStatus,
) => {
  const isReviewScreens = window.location.pathname.includes(ROUTE_NAMES.REVIEW);
  const getIrstatus = getIrstatusType(irStatus);

  if (isReviewScreens) {
    const landingScreen = getFlowType(flowType);
    const verticalName = getVerticalNameType(flowType);

    if (flowType === FLOW_TYPES.COMBO) {
      sendAnalyticsEventBBC(
        verticalName,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.OPEN_FREE_ACCOUNT_CLICKED,
        { Value_selected: sipAmount },
        { Stock_NAME_Qty: cartDetails },
        { Mf_NAME_Qty: mfCartDetails },
        { Total_Bucket_val: Total_Bucket_value },
        { Total_Stock_Unit: totalBucketQty },
        { Total_Mf_Unit: mfCartDetails.length },
        { Total_remaining_Val: Total_remaining_Value },
      );
    } else if (flowType === FLOW_TYPES.STOCKS) {
      sendAnalyticsEventBBC(
        verticalName,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.OPEN_FREE_ACCOUNT_CLICKED,
        { Value_selected: sipAmount },
        { Stock_NAME_Qty: cartDetails },
        { Total_Bucket_val: Total_Bucket_value },
        { Total_Stock_Unit: totalBucketQty },
        { Total_remaining_Val: Total_remaining_Value },
      );
      sendAnalyticsEventBBC(
        NAMES.VERTICAL_NAME,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.OPEN_FREE_ACCOUNT_CLICKED,
        { Value_selected: sipAmount },
        { Stock_NAME_Qty: cartDetails },
        { Total_Bucket_val: Total_Bucket_value },
        { Total_Stock_Unit: totalBucketQty },
        { Total_remaining_Val: Total_remaining_Value },
      );
    } else if (flowType === FLOW_TYPES.MF) {
      sendAnalyticsEventBBC(
        verticalName,
        landingScreen,
        getIrstatus,
        USER_ACTIONS.OPEN_FREE_ACCOUNT_CLICKED,
        { Value_selected: sipAmount },
        { Mf_NAME_Qty: mfCartDetails },
        { Total_Bucket_val: Total_Bucket_value },
        { Total_Mf_Unit: mfCartDetails.length },
        { Total_remaining_Val: Total_remaining_Value },
      );
    }
  } else {
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.OPTIN_MODAL,
      getIrstatus,
      USER_ACTIONS.OPEN_DEMAT_ACCOUNT_CLICKED,
    );
  }
};

export const sendAppsFlyerEvent = () => {
  sendEventToAppsFlyer({
    eventName: OPT_IN_EVENT_BBC.NAME,
    action: OPT_IN_EVENT_BBC.ACTION,
    verticalName: OPT_IN_EVENT_BBC.VERTICAL_NAME,
    screenName: OPT_IN_EVENT_BBC.SCREEN_NAME,
    category: OPT_IN_EVENT_BBC.CATEGORY,
    label6: OPT_IN_EVENT_BBC.LABLE6,
  });
};
export const sendStatusPageOpenEvent = (
  flowType,
  sipAmount,
  cartDetails,
  mfCartDetails,
  Total_Bucket_value,
  totalBucketQty,
  Status,
) => {
  const verticalName = getStatusVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(Status);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STATUS_SCREEN;
      userAction = USER_ACTIONS.STOCK_P2P_PAGE_LANDED;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.STATUS_SCREEN_MF;
      userAction = USER_ACTIONS.MF_P2P_PAGE_LANDED;

      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.STATUS_SCREEN_COMBO;
      userAction = USER_ACTIONS.COMBO_P2P_PAGE_LANDED;
      break;

    default:
      break;
  }
  if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Mf_NAME_Qty: mfCartDetails },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: Status },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: Status },
    );

    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.STOCK_P2P_PAGE_LANDED,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: Status },
    );
  } else if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Stock_NAME_Qty: cartDetails },
      { Mf_NAME_Qty: mfCartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: Status },
    );
  }
};
export const sendDocumentsSubmittedOEvent = (kycFlowtype, kycUserFrom) => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.ESIGN_SCREEN,
    EVENT_TYPE.TYPE,
    USER_ACTIONS.DOCS_SUBMITTED_SUCCESS,
    { Eventname: EVENT_TYPE.KYC_EVENT },
    { kycFlow: kycFlowtype },
    { kycUser: kycUserFrom },
  );
};

export const sendModifyClickOpenEvent = (flowType, sipAmount, irStatus) => {
  const verticalName = getStatusVerticalNameType(flowType);
  const landingScreen = getStatusFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.MODIFY_AMOUNT_SELECTED,
    { Value_selected: sipAmount },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STATUS_SCREEN,
      getIrstatus,
      USER_ACTIONS.MODIFY_AMOUNT_SELECTED,
      { Value_selected: sipAmount },
    );
};
export const sendProceedClickedEvent = (
  flowType,
  sipAmount,
  cartDetails,
  mfCartDetails,
  Total_Bucket_value,
  totalBucketQty,
  irStatus,
) => {
  const verticalName = getStatusVerticalNameType(flowType);
  const landingScreen = getStatusFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.PROCEED_CLICKED_POST_KYC,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Mf_NAME_Qty: mfCartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: irStatus },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.PROCEED_CLICKED_POST_KYC,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: irStatus },
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STATUS_SCREEN,
      getIrstatus,
      USER_ACTIONS.PROCEED_CLICKED_POST_KYC,
      { Value_selected: sipAmount },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: irStatus },
    );
  } else if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.PROCEED_CLICKED_POST_KYC,
      { Value_selected: sipAmount },
      { Mf_NAME_Qty: mfCartDetails },
      { Total_mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Onborading_stage: irStatus },
    );
  }
};

export const sendViewDetailsClickEvent = (flowType, irStatus) => {
  const verticalName = getStatusVerticalNameType(flowType);
  const landingScreen = getStatusFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.VIEW_DETAILS_CLICKED,
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STATUS_SCREEN,
      getIrstatus,
      USER_ACTIONS.VIEW_DETAILS_CLICKED,
    );
};

export const sendSipSuccessPageLandedEvent = (
  flowType,
  cartDetails,
  mfCartDetails,
  dobEpoch,
  transactionAmount,
  userstatus,
  irStatus,
  totalMfAmount,
  flow,
) => {
  const verticalName = getSipSuccessVerticalNameType(flowType);
  const landingScreen = getSipSuccessFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTIONS.STOCK_SIP_PAGE_LANDED;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTIONS.MF_SIP_PAGE_LANDED;

      break;
    case FLOW_TYPES.COMBO:
      userAction = USER_ACTIONS.COMBO_SIP_PAGE_LANDED;
      break;

    default:
      break;
  }

  if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Amount: totalMfAmount },
      { Date: dobEpoch },
      { Status: userstatus },
      { Mf_List: mfCartDetails },
      { Mf_Qty: mfCartDetails.length },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Amount: transactionAmount },
      { Date: dobEpoch },
      { Status: userstatus },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Qty: cartDetails.length },
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STOCK_SIP_SUCCESS,
      getIrstatus,
      USER_ACTIONS.STOCK_SIP_PAGE_LANDED,
      { Amount: transactionAmount },
      { Date: dobEpoch },
      { Status: userstatus },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Qty: cartDetails.length },
    );
  } else if (flowType === FLOW_TYPES.COMBO) {
    if (flow === FLOW_TYPES.MF) {
      sendAnalyticsEventBBC(
        verticalName,
        landingScreen,
        getIrstatus,
        userAction,
        { Amount: totalMfAmount },
        { Date: dobEpoch },
        { Status: userstatus },
        { Stock_NAME_Qty: cartDetails },
        { Total_Stock_Qty: cartDetails.length },
        { Mf_NAME_Qty: mfCartDetails },
        { Mf_Qty: mfCartDetails.length },
      );
    } else {
      sendAnalyticsEventBBC(
        verticalName,
        landingScreen,
        getIrstatus,
        userAction,
        { Amount: transactionAmount },
        { Date: dobEpoch },
        { Status: userstatus },
        { Stock_NAME_Qty: cartDetails },
        { Total_Stock_Qty: cartDetails.length },
        { Mf_NAME_Qty: mfCartDetails },
        { Mf_Qty: mfCartDetails.length },
      );
    }
  }
};

export const sendRedirectPageEvent = (
  flowType,
  cartDetails,
  mfCartDetails,
  userAction,
  totalAmount,
  dobEpoch,
  irStatus,
) => {
  const verticalName = getSipSuccessVerticalNameType(flowType);
  const landingScreen = getSipSuccessFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Amount: totalAmount },
      { Date: dobEpoch },
      '',
      { Mf_NAME_Qty: mfCartDetails },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Amount: totalAmount },
      { Date: dobEpoch },
      '',
      { Stock_NAME_Qty: cartDetails },
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STOCK_SIP_SUCCESS,
      getIrstatus,
      userAction,
      { Amount: totalAmount },
      { Date: dobEpoch },
      '',
      { Stock_NAME_Qty: cartDetails },
    );
  } else if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Amount: totalAmount },
      { Date: dobEpoch },
      '',
      { Stock_NAME_Qty: cartDetails },
      { Mf_NAME_Qty: mfCartDetails },
    );
  }
};
export const sendWarningOkClickedEvent = (flowType, irStatus) => {
  const verticalName = getSipSuccessVerticalNameType(flowType);
  const landingScreen = getSipSuccessFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  if (flowType === FLOW_TYPES.MF || flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.WARNING_OK_CLICKED,
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.WARNING_OK_CLICKED,
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      SCREEN.STOCK_SIP_SUCCESS,
      getIrstatus,
      USER_ACTIONS.WARNING_OK_CLICKED,
    );
  }
};

export const sendReviewPageLandedEvent = flowType => {
  const landingScreen = getFlowType(flowType);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTIONS.SUMMARY_OPTIN_STOCKS;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTIONS.SUMMARY_OPTIN_MF;

      break;
    case FLOW_TYPES.COMBO:
      userAction = USER_ACTIONS.SUMMARY_OPTIN_COMBO;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    EVENT_TYPE.TYPE,
    userAction,
    { Event_name: EVENT_TYPE.KYC_EVENT },
  );
};

export const sendAccountOpenEvent = flowType => {
  const landingScreen = getFlowType(flowType);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTIONS.OPEN_ACCOUNT_CLICKED_STOCK;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTIONS.OPEN_ACCOUNT_CLICKED_MF;

      break;
    case FLOW_TYPES.COMBO:
      userAction = USER_ACTIONS.OPEN_ACCOUNT_CLICKED_COMBO;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    EVENT_TYPE.TYPE,
    userAction,
    { Event_name: EVENT_TYPE.KYC_EVENT },
  );
};

export const sendStatusVideoClickEvent = (flowType, irStatus, index) => {
  const landingScreen = getStatusFlowType(flowType);
  const verticalName = getStatusVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.VIDEO_ICON_CLICKED,
    { carousal_clicked: index + 1 },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.VIDEO_ICON_CLICKED,
      { carousal_clicked: index + 1 },
    );
};
