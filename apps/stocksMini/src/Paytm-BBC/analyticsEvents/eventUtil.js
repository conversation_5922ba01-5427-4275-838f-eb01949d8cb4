import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import {
  NAME,
  SCREENS,
  USER_ACTION,
} from '@src/Paytm-BBC/analyticsEvents/landingScreen';
import { FLOW_TYPES } from '@src/Paytm-BBC/utils/Constants';
import { IR_STATUS_ENUM } from '@src/config/common';

const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

const getFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.LANDING_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.LANDING_SCREEN1;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.LANDING_SCREEN2;
      break;

    default:
      break;
  }
  return landingScreen;
};
const getVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_CT_LANDING_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_CT_LANDING_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAME.COMBO_CT_LANDING_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};

export const sendLandingPageOpenEvent = (
  flowType,
  totalBucketQty,
  irStatus,
) => {
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);
  const pathName = window.location.pathname;
  const isLandingScreen =
    pathName.includes(FLOW_TYPES.STOCKS) ||
    pathName.includes(FLOW_TYPES.MF) ||
    pathName.includes(FLOW_TYPES.COMBO);
  if (!isLandingScreen || totalBucketQty) return;

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.LANDING_SCREEN;
      userAction = USER_ACTION.STOCKS_LANDING_PAGE_OPENED;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.LANDING_SCREEN1;
      userAction = USER_ACTION.MF_LANDING_PAGE;

      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.LANDING_SCREEN2;
      userAction = USER_ACTION.COMBO_LANDING_PAGE;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(verticalName, landingScreen, getIrstatus, userAction);
  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
    );
};

export const sendPmlInfoOpenEvent = (flowType, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.PML_INFO_ICON_CLICKED,
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.PML_INFO_ICON_CLICKED,
    );
};
export const sendDrawerOpenEvent = (flowType, select, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.SELECT_DROPDOWN_CLICKED,
    { value_selected: select },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.SELECT_DROPDOWN_CLICKED,
      { value_selected: select },
    );
};
export const sendBbcPreoptinClickEvent = (flowType, select, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTION.BBC_PREOPTIN_STOCKS_CLICKED;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTION.BBC_PREOPTIN_MF_CLICKED;

      break;
    case FLOW_TYPES.COMBO:
      userAction = USER_ACTION.BBC_PREOPTIN_COMBO_CLICKED;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(verticalName, landingScreen, getIrstatus, userAction, {
    value_selected: select,
  });

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
      { value_selected: select },
    );
};

export const sendBbcWidgetOpenEvent = (flowType, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTION.BBC_WIDGET_STOCKS_OPEN_DEMAT;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTION.BBC_WIDGET_MF_OPEN_DEMAT;

      break;
    case FLOW_TYPES.COMBO:
      userAction = USER_ACTION.BBC_WIDGET_COMBO_OPEN_DEMAT;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(verticalName, landingScreen, getIrstatus, userAction);

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
    );
};

export const sendOptinModalClickEvent = (flowType, irStatus) => {
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    SCREENS.OPTIN_MODAL,
    getIrstatus,
    USER_ACTION.OPTIN_MODAL_OPENED,
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      SCREENS.OPTIN_MODAL,
      getIrstatus,
      USER_ACTION.OPTIN_MODAL_OPENED,
    );
};

export const sendLandingVideoClickEvent = (
  flowType,
  sipAmount,
  irStatus,
  index,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.VIDEO_CLICKED,
    { Amount: sipAmount },
    { carousal_clicked: index + 1 },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.VIDEO_CLICKED,
      { Amount: sipAmount },
      { carousal_clicked: index + 1 },
    );
};
