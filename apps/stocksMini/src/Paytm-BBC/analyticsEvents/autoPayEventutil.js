import {
  NAMES,
  SCREEN,
  EVENT_TYPE,
  USER_ACTIONS,
} from '@src/Paytm-BBC/analyticsEvents/autoPayScreen';
import { sendAnalyticsEventBBC } from '@src/services/coreUtil';

export const sendStocksAutoPayPageLandedEvent = () => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.STOKCS_AUTOPAY_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.AUTOPAY_PAGE_LANDED,
  );
};

export const sendAmountInfoClickEvent = () => {
  const pathName = window.location.pathname;
  const isAutoPayScreen = pathName.includes(NAMES.AUTOPAY);
  const screenName = isAutoPayScreen
    ? SCREEN.STOKCS_AUTOPAY_SCREEN
    : SCREEN.STOKCS_PAYIN_SCREEN;
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    screenName,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.AMOUNT_INFO_CLICKED,
  );
};

export const sendPayAmountClickEvent = (
  isQuickRecommended,
  amountPaid,
  vpa,
) => {
  let paymentFlow = '';

  if (isQuickRecommended) {
    paymentFlow = NAMES.PUSH;
  } else {
    paymentFlow = vpa === null ? NAMES.INTENT : NAMES.COLLECT;
  }

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.STOKCS_AUTOPAY_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.PAY_AMOUNT_CLICKED,
    paymentFlow,
    amountPaid,
  );
};

export const sendStocksPayInPageLandedEvent = () => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.STOKCS_PAYIN_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.PAYIN_PAGE_LANDED,
  );
};

export const sendPayInPayAmountClickEvent = (
  isQuickRecommended,
  amountPaid,
  vpa,
) => {
  let paymentFlow = '';

  if (isQuickRecommended) {
    paymentFlow = NAMES.PUSH;
  } else {
    paymentFlow = vpa === null ? NAMES.INTENT : NAMES.COLLECT;
  }

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.STOKCS_PAYIN_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.PAY_AMOUNT_CLICKED,
    paymentFlow,
    amountPaid,
  );
};

export const sendPaymentBridgecallEvent = txnStatus => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.STOCK_PAYMENT_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.STOCKS_PAYMENT_STATUS,
    txnStatus,
  );
};
