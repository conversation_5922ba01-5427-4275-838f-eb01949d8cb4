import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import {
  NAME,
  SCREENS,
  USER_ACTION,
} from '@src/Paytm-BBC/analyticsEvents/marketUpdateScreen';
import { IR_STATUS_ENUM } from '@src/config/common';

export const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

export const sendMarketUpdateLandingEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.USER_LANDED_MARKET_UPDATE,
    { selectedTileName },
  );
};

export const sendMarketUpdateSharingEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MARKET_UPDATE_SHARING,
  );
};

export const sendMarketUpdateSharingMediumEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.SHARING_MEDIUM,
  );
};

export const sendMarketUpdateSharingMediumChoosenEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MEDIUM_CHOOSEN,
  );
};

export const sendMarketUpdateBackClickEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MARKET_UPDTAE_BACK,
  );
};

export const sendYoutubeCreatorClickEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.YT_CREATOR,
  );
};
export const sendTwitterCreatorClickEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.TWITTER_CREATOR,
  );
};

export const sendTileClickedEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.TILE_CLICKED,
    { selectedTileName },
  );
};

export const sendViewOptionChainEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.VIEW_OPTION_CHAIN,
    { selectedTileName },
  );
};

export const sendClickExpiryOptionChainEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.EXPIRY_OPTION_CHAIN,
    { selectedTileName },
  );
};

export const sendViewFullOptionChainEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.VIEW_FULL_OPTION_CHAIN,
    { selectedTileName },
  );
};

export const sendClickSpotAndfutureEvent = (irStatus, selectedTileName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.VIEW_SPOT_FUTURE,
    { selectedTileName },
  );
};

export const sendClickSetPriceAlertEvent = (
  irStatus,
  selectedTileName,
  isSpot,
  stockName,
) => {
  const getIrstatus = getIrstatusType(irStatus);
  const selectedSection = isSpot ? 'spot' : 'future';
  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.SET_PRICE_ALERT,
    { selectedTileName },
    { selectedSection },
    { stockName },
  );
};

export const sendSetPriceAlertCompletedEvent = (
  irStatus,
  selectedTileName,
  isSpot,
) => {
  const getIrstatus = getIrstatusType(irStatus);
  const selectedSection = isSpot ? 'spot' : 'future';

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.PRICE_ALERT_COMPLETED,
    { selectedTileName },
    { selectedSection },
  );
};

export const sendClickAddToWatchlistEvent = (
  irStatus,
  selectedTileName,
  isSpot,
  stockName,
) => {
  const getIrstatus = getIrstatusType(irStatus);
  const selectedSection = isSpot ? 'spot' : 'future';

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.ADD_TO_WATCHLIST,
    { selectedTileName },
    { selectedSection },
    { stockName },
  );
};

export const sendAddedToWatchlistEvent = (
  irStatus,
  selectedTileName,
  isSpot,
  watchListName,
) => {
  const getIrstatus = getIrstatusType(irStatus);
  const selectedSection = isSpot ? 'spot' : 'future';

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.WATCHLIST_ADDED,
    { selectedTileName },
    { selectedSection },
    { watchListName },
  );
};

export const sendMarketOveriewClickEvent = (irStatus, selectedSectionName) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MARKET_OVERVIEW,
    { selectedSectionName },
  );
};

export const sendMarketOveriewWatchListClickedEvent = (
  irStatus,
  selectedSectionName,
  SelectedTile,
) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MARKET_OVERIEW_WATCHLIST,
    { selectedSectionName },
    { SelectedTile },
  );
};

export const sendMarketOveriewWatchlistAddedEvent = (
  irStatus,
  selectedSectionName,
  watchListData,
) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.WATCHLIST_SET,
    { selectedSectionName },
    { watchListData },
  );
};

export const sendMoreVideosClickedEvent = (irStatus, videoTitle) => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.MORE_VIDEOS,
    { videoTitle },
  );
};

export const sendAllVideosClickedEvent = irStatus => {
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAME.VERTICAL_NAME,
    SCREENS.LANDING_SCREEN_MARKET_UPDATE,
    getIrstatus,
    USER_ACTION.ALL_VIDEOS,
  );
};
