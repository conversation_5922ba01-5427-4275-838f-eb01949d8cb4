import {
  NAMES,
  SCREEN,
  EVENT_TYPE,
  USER_ACTIONS,
} from '@src/Paytm-BBC/analyticsEvents/paymentScreen';
import { sendAnalyticsEventBBC } from '@src/services/coreUtil';

export const sendStocksAddfundPageLandedEvent = (amount, source, flowType) => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.ADD_FUND_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.ADD_FUND_PAGE_LANDED,
    { Amount: amount },
    source,
    flowType,
  );
};

export const sendAmountInfoClickEvent = flowType => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.ADD_FUND_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.AMOUNT_INFO_CLICKED,
    flowType,
  );
};

export const sendPayAmountClickEvent = (
  flow,
  amountPaid,
  payMethodId,
  source,
  flowType,
) => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.ADD_FUND_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.PAY_AMOUNT_CLICKED,
    flow,
    { payment_method_selected: payMethodId },
    amountPaid,
    source,
    flowType,
  );
};

export const sendPaymentBridgecallEvent = (txnStatus, source, flowType) => {
  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    SCREEN.ADD_FUND_SCREEN,
    EVENT_TYPE.EVENT_KYC,
    USER_ACTIONS.PAYMENT_STATUS,
    txnStatus,
    source,
    flowType,
  );
};
