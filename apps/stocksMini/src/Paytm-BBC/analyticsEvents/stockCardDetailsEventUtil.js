import {
  NAMES,
  SCREEN,
  USER_ACTIONS,
  VERTICAL_NAME,
} from '@src/Paytm-BBC/analyticsEvents/stockCardDetailsScreen';
import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import { IR_STATUS_ENUM } from '@src/config/common';
import { FLOW_TYPES } from '@src/Paytm-BBC/utils/Constants';
import { ROUTE_NAMES } from '../routes/config/urlConfig';

const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

const getComboFlowType = () => {
  const pathName = window.location.pathname;
  const isComboStockScreen = pathName.includes(ROUTE_NAMES.STOCK_SELECTION);

  let comboScreen = '';
  if (isComboStockScreen) {
    comboScreen = SCREEN.COMBO_STOCK_SCREEN;
  } else {
    comboScreen = SCREEN.COMBO_MF_SCREEN;
  }
  return comboScreen;
};

const getFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STOCK_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.COMBO_MINI_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = getComboFlowType();
      break;

    default:
      break;
  }
  return landingScreen;
};

const getFlowPageType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STOCK_MINI_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.COMBO_MINI_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.COMBO_MINI_SCREEN;
      break;

    default:
      break;
  }
  return landingScreen;
};

export const sendStockTileClickEvent = (flowType, stockName, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SELECT_STOCK_CLICKED,
    { stockName },
  );
};

export const sendStockMiniOpenEvent = (flowType, stockName, irStatus) => {
  const landingScreen = getFlowPageType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.STOCK_MINI_OPENED,
    { stockName },
  );
};

export const sendAddtStockClickEvent = (flowType, stockName, irStatus) => {
  const landingScreen = getFlowPageType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.ADD_CLICKED,
    { stockName },
  );
};

export const sendAddtQtyClickEvent = (flowType, stockName, irStatus) => {
  const landingScreen = getFlowPageType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.STOCK_UNIT_ADDED,
    { stockName },
  );
};

export const sendRemoveQtyClickEvent = (flowType, stockName, irStatus) => {
  const landingScreen = getFlowPageType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    NAMES.VERTICAL_NAME,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.STOCK_UNIT_REMOVED,
    { stockName },
  );
};
