import {
  NAME,
  SCREENS,
  EVENT_CATEGORY,
  USER_ACTION,
} from '@src/Paytm-BBC/analyticsEvents/commonEventScreen';

import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import { IR_STATUS_ENUM } from '@src/config/common';
import { FLOW_TYPES } from '@src/Paytm-BBC/utils/Constants';
import { ROUTE_NAMES } from '../routes/config/urlConfig';

export const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

const getLandingPageVerticalNames = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_CT_LANDING_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_CT_LANDING_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAME.COMBO_CT_LANDING_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};

const getLandingPageScreenNames = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.LANDING_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.LANDING_SCREEN1;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.LANDING_SCREEN2;
      break;

    default:
      break;
  }
  return landingScreen;
};

const getStatusPageScreen = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.STATUS_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.STATUS_SCREEN_MF;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.STATUS_SCREEN_COMBO;
      break;

    default:
      break;
  }
  return landingScreen;
};

const getStatusPageVerticalName = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_STATUS_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_STATUS_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAME.COMBO_STATUS_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
const getComboSelectionScreenName = () => {
  const pathName = window.location.pathname;
  const isComboStockScreen = pathName.includes(ROUTE_NAMES.STOCK_SELECTION);
  let comboScreen = '';
  if (isComboStockScreen) {
    comboScreen = SCREENS.COMBO_STOCK_SCREEN;
  } else {
    comboScreen = SCREENS.COMBO_MF_SCREEN;
  }
  return comboScreen;
};
const getSelectionPageScreenName = flowType => {
  let landingScreen = '';

  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.STOCK_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.MF_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = getComboSelectionScreenName();
      break;

    default:
      break;
  }

  return landingScreen;
};
const getSelevtionPageVerticalName = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_CT_SELCTION_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_CT_SELCTION_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName =
        getComboSelectionScreenName() === SCREENS.COMBO_STOCK_SCREEN
          ? NAME.COMBO_STOCK_CT_SELCTION_VERTICAL_NAME
          : NAME.COMBO_MF_CT_SELCTION_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};

const getSipSuccessPageScreenName = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.STOCK_SIP_SUCCESS;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.MF_SIP_SUCCESS;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.COMBO_SIP_SUCCESS;
      break;

    default:
      break;
  }
  return landingScreen;
};

const getSipSuccessVerticalName = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_SIP_SUCCESS;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_SIP_SUCCESS;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAME.COMBO_SIP_SUCCESS;
      break;

    default:
      break;
  }
  return verticalName;
};
const getSummaryScreenName = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREENS.REVIEW_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREENS.REVIEW_SCREEN_MF;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREENS.REVIEW_SCREEN_COMBO;
      break;

    default:
      break;
  }
  return landingScreen;
};
const getSummaryVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAME.STOCK_CT_SUMMARY_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAME.MF_CT_SUMMARY_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAME.COMBO_CT_SUMMARY_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
export const sendNeedHelpClickEvent = (
  flowType,
  sipAmount,
  irStatus,
  cartTotal,
) => {
  const pathName = window.location.pathname;
  let landingScreen = '';
  let verticalName = '';
  const getIrstatus = getIrstatusType(irStatus);
  if (pathName.includes(NAME.LANDING) && cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  } else if (pathName.includes(NAME.LANDING) && cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (
    pathName.includes(ROUTE_NAMES.STOCK_SELECTION) ||
    pathName.includes(ROUTE_NAMES.MF_SELECTION)
  ) {
    landingScreen = getSelectionPageScreenName(flowType);
    verticalName = getSelevtionPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.CONFIRM_SIP)) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.SIPSETUP)) {
    landingScreen = getSipSuccessPageScreenName(flowType);
    verticalName = getSipSuccessVerticalName(flowType);
  } else if (cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  }
  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.NEED_HELP_CLICK,
    { flow: EVENT_CATEGORY.FLOW },
    { Amount: sipAmount },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.NEED_HELP_CLICK,
      { flow: EVENT_CATEGORY.FLOW },
      { Amount: sipAmount },
    );
};

export const sendFaqOpenEvent = (flowType, irStatus, cartTotal) => {
  const pathName = window.location.pathname;
  let landingScreen = '';
  let verticalName = '';
  const getIrstatus = getIrstatusType(irStatus);
  if (pathName.includes(NAME.LANDING) && cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  } else if (pathName.includes(NAME.LANDING) && cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (
    pathName.includes(ROUTE_NAMES.STOCK_SELECTION) ||
    pathName.includes(ROUTE_NAMES.MF_SELECTION)
  ) {
    landingScreen = getSelectionPageScreenName(flowType);
    verticalName = getSelevtionPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.CONFIRM_SIP)) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.SIPSETUP)) {
    landingScreen = getSipSuccessPageScreenName(flowType);
    verticalName = getSipSuccessVerticalName(flowType);
  } else if (cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  }

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.FAQ_DROPDOWN,
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.FAQ_DROPDOWN,
    );
};

export const sendonCallUsClickEvent = (flowType, irStatus, cartTotal) => {
  const pathName = window.location.pathname;
  let landingScreen = '';
  let verticalName = '';
  const getIrstatus = getIrstatusType(irStatus);
  if (pathName.includes(NAME.LANDING) && cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  } else if (pathName.includes(NAME.LANDING) && cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (
    pathName.includes(ROUTE_NAMES.STOCK_SELECTION) ||
    pathName.includes(ROUTE_NAMES.MF_SELECTION)
  ) {
    landingScreen = getSelectionPageScreenName(flowType);
    verticalName = getSelevtionPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.CONFIRM_SIP)) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.SIPSETUP)) {
    landingScreen = getSipSuccessPageScreenName(flowType);
    verticalName = getSipSuccessVerticalName(flowType);
  } else if (pathName.includes(ROUTE_NAMES.REVIEW)) {
    landingScreen = getSummaryScreenName(flowType);
    verticalName = getSummaryVerticalNameType(flowType);
  } else if (cartTotal !== 0) {
    landingScreen = getStatusPageScreen(flowType);
    verticalName = getStatusPageVerticalName(flowType);
  } else if (cartTotal === 0) {
    landingScreen = getLandingPageScreenNames(flowType);
    verticalName = getLandingPageVerticalNames(flowType);
  }

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTION.CALL_US_CLICKED,
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAME.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTION.CALL_US_CLICKED,
    );
};
