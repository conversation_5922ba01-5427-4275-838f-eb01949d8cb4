import {
  NAMES,
  SCREEN,
  USER_ACTIONS,
} from '@src/Paytm-BBC/analyticsEvents/stockSelectionScreen';
import { sendAnalyticsEventBBC } from '@src/services/coreUtil';
import { IR_STATUS_ENUM } from '@src/config/common';
import { FLOW_TYPES } from '@src/Paytm-BBC/utils/Constants';
import { ROUTE_NAMES } from '../routes/config/urlConfig';

const getIrstatusType = irStatus => {
  let statusType = '';

  if (
    irStatus === IR_STATUS_ENUM.ACTIVE ||
    irStatus === IR_STATUS_ENUM.VERIFIED
  ) {
    statusType = 'KYC';
  } else {
    statusType = 'Non KYC';
  }
  return statusType;
};

const getComboFlowType = () => {
  const pathName = window.location.pathname;
  const isComboStockScreen = pathName.includes(ROUTE_NAMES.STOCK_SELECTION);

  let comboScreen = '';
  if (isComboStockScreen) {
    comboScreen = SCREEN.COMBO_STOCK_SCREEN;
  } else {
    comboScreen = SCREEN.COMBO_MF_SCREEN;
  }
  return comboScreen;
};
const getFlowType = flowType => {
  let landingScreen = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.STOCK_SCREEN;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.MF_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = getComboFlowType();
      break;

    default:
      break;
  }
  return landingScreen;
};
const getSelevtionVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAMES.STOCK_CT_SELCTION_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAMES.MF_CT_SELCTION_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName =
        getComboFlowType() === SCREEN.COMBO_STOCK_SCREEN
          ? NAMES.COMBO_STOCK_CT_SELCTION_VERTICAL_NAME
          : NAMES.COMBO_MF_CT_SELCTION_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
const getReviewVerticalNameType = flowType => {
  let verticalName = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      verticalName = NAMES.STOCK_CT_REVIEW_VERTICAL_NAME;
      break;
    case FLOW_TYPES.MF:
      verticalName = NAMES.MF_CT_REVIEW_VERTICAL_NAME;
      break;
    case FLOW_TYPES.COMBO:
      verticalName = NAMES.COMBO_CT_REVIEW_VERTICAL_NAME;
      break;

    default:
      break;
  }
  return verticalName;
};
export const sendSelectionPageOpenEvent = (flowType, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      userAction = USER_ACTIONS.STOCK_UNIT_PAGE_LANDED;
      break;
    case FLOW_TYPES.MF:
      userAction = USER_ACTIONS.MF_UNIT_PAGE_LANDED;

      break;
    case FLOW_TYPES.COMBO:
      userAction =
        landingScreen === SCREEN.COMBO_STOCK_SCREEN
          ? USER_ACTIONS.COMBO_STOCK_UNIT_PAGE_LANDED
          : USER_ACTIONS.COMBO_MF_UNIT_PAGE_LANDED;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(verticalName, landingScreen, getIrstatus, userAction);

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
    );
};
export const sendDrawerOpenClickEvent = (
  flowType,
  select,
  sipYear,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SELECT_DROPDOWN_CLICKED,
    { value_selected: select },
    { tenure_selected: sipYear },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.SELECT_DROPDOWN_CLICKED,
      { value_selected: select },
      { tenure_selected: sipYear },
    );
};
export const sendYearDrawerOpenEvent = (flowType, sipAmount, irStatus) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.YEAR_DROPDOWN_CLICKED,
    { value_selected: sipAmount },
    { Event: 'Opened_Year_Drawer' },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.YEAR_DROPDOWN_CLICKED,
      { value_selected: sipAmount },
      { Event: 'Opened_Year_Drawer' },
    );
};
export const sendYearDrawerClickEvent = (
  flowType,
  sipAmount,
  value,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.YEAR_DROPDOWN_CLICKED,
    { value_selected: sipAmount },
    { Tenure_selected: value },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.YEAR_DROPDOWN_CLICKED,
      { value_selected: sipAmount },
      { Tenure_selected: value },
    );
};
export const sendSelectStockClickEvent = (
  flowType,
  sipAmount,
  name,
  year,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SELECT_STOCK_CLICKED,
    { Value_Selected: sipAmount },
    { Tenure_Selected: year },
    { Name: name },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.SELECT_STOCK_CLICKED,
      { Value_Selected: sipAmount },
      { Tenure_Selected: year },
      { Name: name },
    );
};

export const sendComboModifyClickOpenEvent = (
  flowType,
  sipAmount,
  irStatus,
) => {
  const verticalName = getSelevtionVerticalNameType(flowType);
  const landingScreen = getFlowType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.MODIFY_CLICK,
    { Value_selected: sipAmount },
  );
};
export const sendStockAddedClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalBucketQty,
  Total_Bucket_value,
  Total_remaining_Value,
  sipYear,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.PLUS_CLICKD,
    { Value_selected: sipAmount },
    { Tenure_Selected: sipYear },
    { Stock_NAME_Qty: cartDetails },
    { Total_Stock_Unit: totalBucketQty },
    { Total_Bucket_val: Total_Bucket_value },
    { Total_remaining_Val: Total_remaining_Value },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.PLUS_CLICKD,
      { Value_selected: sipAmount },
      { Tenure_Selected: sipYear },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
};
export const sendStockSubtractClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalBucketQty,
  Total_Bucket_value,
  Total_remaining_Value,
  sipYear,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.MINUS_CLICKED,
    { Value_selected: sipAmount },
    { Tenure_Selected: sipYear },
    { Stock_NAME_Qty: cartDetails },
    { Total_Stock_Unit: totalBucketQty },
    { Total_Bucket_val: Total_Bucket_value },
    { Total_remaining_Val: Total_remaining_Value },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.MINUS_CLICKED,
      { Value_selected: sipAmount },
      { Tenure_Selected: sipYear },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
};
export const sendSearchStockClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  year,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SEARCH_CLICKED,
    { value_selected: sipAmount },
    { Tenure_selected: year },
    { Stock_List: cartDetails },
    { Total_Stock_Unit: totalQuantity },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.SEARCH_CLICKED,
      { value_selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
    );
};
export const sendStockSearchSelectedClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  sipYear,
  name,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SEARCH_STOCK_SELECTED,
    { Value_selected: sipAmount },
    { Tenure_selected: sipYear },
    { Stock_List: cartDetails },
    { Total_Stock_Unit: totalQuantity },
    { Search_Stock_Name: name },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.SEARCH_STOCK_SELECTED,
      { Value_selected: sipAmount },
      { Tenure_selected: sipYear },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Search_Stock_Name: name },
    );
};
export const sendReviewPopupClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  Total_Bucket_value,
  Total_remaining_Value,
  year,
  mfCartItem,
  irStatus,
) => {
  const verticalName = getReviewVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.REVIEW_POPUP_SCREEN;
      userAction = USER_ACTIONS.STOCKS_REVIEW_POPUP;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.MF_REVIEW_SCREEN;
      userAction = USER_ACTIONS.MF_REVIEW_POPUP;

      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.COMBO_REVIEW_SCREEN;
      userAction = USER_ACTIONS.COMBO_REVIEW_POPUP;
      break;

    default:
      break;
  }
  if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { MF_list: mfCartItem },
      { Total_MF_Unit: mfCartItem.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { MF_list: mfCartItem },
      { Total_Stock_Unit: totalQuantity },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else if (flowType === FLOW_TYPES.STOCKS) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  }
};

export const sendMfCtaClickvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  year,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.MF_CTA_CLICK,
    { value_selected: sipAmount },
    { Tenure_selected: year },
    { Stock_List: cartDetails },
    { Total_Stock_Unit: totalQuantity },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      USER_ACTIONS.MF_CTA_CLICK,
      { value_selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
    );
};

export const sendMfSelectClickEvent = (
  flowType,
  fundName,
  year,
  sipAmount,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SELECT_MF_CLICK,
    { Value_selected: sipAmount },
    { Tenure_selected: year },
    { MF_Name: fundName },
  );
};
export const sendMfSelectAmountPopupClickEvent = (
  flowType,
  fundName,
  year,
  sipAmount,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SELECT_AMOUNT_POPUP,
    { Value_selected: sipAmount },
    { Tenure_selected: year },
    { MF_Name: fundName },
  );
};
export const sendInvestAmountClickEvent = (
  flowType,
  fundName,
  year,
  sipAmount,
  amount,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.INVEST_AMOUNT_CLICK,
    { Value_selected: sipAmount },
    { Tenure_selected: year },
    { MF_Name: fundName },
    { Amount_INVESTED: amount },
  );
};
export const sendSearchMfClickEvent = (
  flowType,
  sipAmount,
  mfCartDetails,
  totalMfAmount,
  year,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.MF_SEARCH_CLICKED,
    { value_selected: sipAmount },
    { Tenure_selected: year },
    { MF_List: mfCartDetails },
    { total_MF_unit: mfCartDetails.length },
    { Total_Mf_Amount: totalMfAmount },
  );
};
export const sendSearchMfSelectedClickEvent = (
  flowType,
  fundName,
  sipAmount,
  mfCartDetails,
  totalMfAmount,
  year,
  irStatus,
) => {
  const landingScreen = getFlowType(flowType);
  const verticalName = getSelevtionVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    USER_ACTIONS.SEARCH_MF_SELECTED,
    { Value_selected: sipAmount },
    { Tenure_selected: year },
    { MF_List: mfCartDetails },
    { total_MF_unit: mfCartDetails.length },
    { Total_Mf_Amount: totalMfAmount },
    { Name: fundName },
  );
};

export const sendReviewStockAddedClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalBucketQty,
  Total_Bucket_value,
  Total_remaining_Value,
  sipYear,
  irStatus,
) => {
  const verticalName = getReviewVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.REVIEW_POPUP_SCREEN;
      userAction = USER_ACTIONS.PLUS_CLICKD;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.MF_REVIEW_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.COMBO_REVIEW_SCREEN;
      userAction = USER_ACTIONS.PLUS_CLICKD;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    userAction,
    { Value_selected: sipAmount },
    { Tenure_Selected: sipYear },
    { Stock_NAME_Qty: cartDetails },
    { Total_Stock_Unit: totalBucketQty },
    { Total_Bucket_val: Total_Bucket_value },
    { Total_remaining_Val: Total_remaining_Value },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_selected: sipAmount },
      { Tenure_Selected: sipYear },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
};
export const sendReviewStockSubtractedClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalBucketQty,
  Total_Bucket_value,
  Total_remaining_Value,
  sipYear,
  irStatus,
) => {
  const verticalName = getReviewVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.REVIEW_POPUP_SCREEN;
      userAction = USER_ACTIONS.MINUS_CLICKED;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.MF_REVIEW_SCREEN;
      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.COMBO_REVIEW_SCREEN;
      userAction = USER_ACTIONS.MINUS_CLICKED;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    userAction,
    { Value_selected: sipAmount },
    { Tenure_Selected: sipYear },
    { Stock_NAME_Qty: cartDetails },
    { Total_Stock_Unit: totalBucketQty },
    { Total_Bucket_val: Total_Bucket_value },
    { Total_remaining_Val: Total_remaining_Value },
  );

  if (flowType === FLOW_TYPES.STOCKS)
    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_selected: sipAmount },
      { Tenure_Selected: sipYear },
      { Stock_NAME_Qty: cartDetails },
      { Total_Stock_Unit: totalBucketQty },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
};
export const sendReviewProceedClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  Total_Bucket_value,
  Total_remaining_Value,
  year,
  mfCartDetails,
  irStatus,
) => {
  const verticalName = getReviewVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      landingScreen = SCREEN.REVIEW_POPUP_SCREEN;
      userAction = USER_ACTIONS.PROCEED_CLICKED;
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.REVIEW_MF_POPUP_SCREEN;
      userAction = USER_ACTIONS.PROCEED_MF_CLICK;

      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.REVIEW_COMBO_POPUP_SCREEN;
      userAction = USER_ACTIONS.PROCEED_COMBO_CLICK;
      break;

    default:
      break;
  }

  if (flowType === FLOW_TYPES.COMBO) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { MF_list: mfCartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else if (flowType === FLOW_TYPES.MF) {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { MF_list: mfCartDetails },
      { Total_Mf_Unit: mfCartDetails.length },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  } else {
    sendAnalyticsEventBBC(
      verticalName,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );

    sendAnalyticsEventBBC(
      NAMES.VERTICAL_NAME,
      landingScreen,
      getIrstatus,
      userAction,
      { Value_Selected: sipAmount },
      { Tenure_selected: year },
      { Stock_List: cartDetails },
      { Total_Stock_Unit: totalQuantity },
      { Total_Bucket_val: Total_Bucket_value },
      { Total_remaining_Val: Total_remaining_Value },
    );
  }
};
export const sendMfAmountChangeClickEvent = (
  flowType,
  sipAmount,
  cartDetails,
  totalQuantity,
  Total_Bucket_value,
  year,
  mfCartItem,
  irStatus,
) => {
  const verticalName = getReviewVerticalNameType(flowType);
  const getIrstatus = getIrstatusType(irStatus);

  let landingScreen = '';
  let userAction = '';
  switch (flowType) {
    case FLOW_TYPES.STOCKS:
      break;
    case FLOW_TYPES.MF:
      landingScreen = SCREEN.REVIEW_MF_POPUP_SCREEN;
      userAction = USER_ACTIONS.MF_UNIT_ADDED;

      break;
    case FLOW_TYPES.COMBO:
      landingScreen = SCREEN.REVIEW_COMBO_POPUP_SCREEN;
      userAction = USER_ACTIONS.COMBO_UNIT_ADDED;
      break;

    default:
      break;
  }
  sendAnalyticsEventBBC(
    verticalName,
    landingScreen,
    getIrstatus,
    userAction,
    { Value_Selected: sipAmount },
    { Tenure_selected: year },
    { MF_list: mfCartItem },
    { Total_mf_Unit: mfCartItem.length },
    { Total_Bucket_val: Total_Bucket_value },
    { Total_remaining_Val: Math.max(sipAmount - Total_Bucket_value, 0) },
  );
};
