const USER_ACTIONS = {
  LEARN_MORE_CLICKED: 'learn_more_clicked',
  LIST_OF_CHARGES_CLICKED: 'list_of_charges_clicked',
  TNC_CLICKED: 'tnc_clicked',
  OPEN_FREE_ACCOUNT_CLICKED: 'open_account_CTA_clicked',
  OPEN_DEMAT_ACCOUNT_CLICKED: 'open_demat_account_clicked',
  REVIEW_SIP_PAGE_OPENED: 'NU_summary_page_landed',
  // securityshield
  ACTIVATE_SHIELD_CLICKED: 'activate_paytm_shield_clicked',
  SECURITY_SHIELD_PAGE_LANDED: 'security_shield_popup_opened',
  SHIELD_LATER_CLICKED: 'Shield_later_clicked',
  // status screen
  VIDEO_ICON_CLICKED: 'Nokyc_Carousal_video_clicked',
  STOCK_P2P_PAGE_LANDED: 'Stock_nokyc_p2p_page_landed',
  MF_P2P_PAGE_LANDED: 'mf_nokyc_p2p_page_landed',
  COMBO_P2P_PAGE_LANDED: 'combo_nokyc_p2p_page_landed',
  LEARN_MORE_CLICK: 'Learn_more_carousal_clicked',
  MODIFY_AMOUNT_SELECTED: 'Modify_amount_selected',
  PROCEED_CLICKED_POST_KYC: 'Proceed_clicked_post_kyc',
  VIEW_DETAILS_CLICKED: 'view_details_clicked',
  // Addfunds
  PAY_AMOUNT_CLICKED: 'pay_amount_clicked',
  ADD_FUND_PAGE_LANDED: 'add_funds_screen_loaded',
  AMOUNT_INFO_CLICKED: 'amount_info_clicked',
  // Sip Sucess
  STOCK_SIP_PAGE_LANDED: 'stock_sip_page_landed',
  MF_SIP_PAGE_LANDED: 'mf_sip_page_landed',
  COMBO_SIP_PAGE_LANDED: 'combo_sip_page_landed',
  GO_TO_PAYTM_MONET_CLICKED: 'Go_to_paytm_money_clicked',
  RETRY_CLICKED: 'retry_payment_clicked',
  WARNING_OK_CLICKED: 'warning_ok_clicked',
  // kyc screen
  DOCS_SUBMITTED_SUCCESS: 'User_lands_on_all_docs_submitted_success',
  // optin page actions
  SUMMARY_OPTIN_STOCKS: 'NU_summary_page_landed_stock_BBC1',
  SUMMARY_OPTIN_MF: 'NU_summary_page_landed_mf_BBC1',
  SUMMARY_OPTIN_COMBO: 'NU_summary_page_landed_combo_BBC1',
  OPEN_ACCOUNT_CLICKED_STOCK: 'open_account_CTA_clicked_stock_BBC1',
  OPEN_ACCOUNT_CLICKED_MF: 'open_account_CTA_clicked_mf_BBC1',
  OPEN_ACCOUNT_CLICKED_COMBO: 'open_account_CTA_clicked_combo_BBC1',
};

const SCREEN = {
  REVIEW_SCREEN: 'Stock_summary_NU_page',
  REVIEW_SCREEN_MF: 'MF_summary_NU_page',
  REVIEW_SCREEN_COMBO: 'Combo_summary_NU_page',
  SHIELD_SCREEN: 'Security_shield_popup',
  STATUS_SCREEN: 'Stock_account_status_page',
  STATUS_SCREEN_MF: 'MF_account_status_page',
  STATUS_SCREEN_COMBO: 'Combo_account_status_page',
  STOCK_SP_OPEN_ACCOUNT_SCREEN: 'BBC_preoptin_stocks_SP_open_account',
  ADD_FUND_SCREEN: 'Add_funds_page',
  STOCK_SIP_SUCCESS: 'Stock_SIP_success',
  MF_SIP_SUCCESS: 'MF_SIP_success',
  COMBO_SIP_SUCCESS: 'Combo_SIP_success',
  ESIGN_SCREEN: 'esign',
  OPTIN_MODAL: 'optin_modal',
};

const EVENT_TYPE = {
  EVENT: 'Non KYC',
  EVENT_NAME: 'KYC + Non KYC',
  EVENT_KYC: 'KYC',
  TYPE: 'onboarding',
  KYC_EVENT: 'Paytm Mini Onboarding',
};
const NAMES = {
  VERTICAL_NAME: 'Paytm Stocks',
  STOCK_CT_SUMMARY_VERTICAL_NAME: 'Stock_summary_newuser',
  MF_CT_SUMMARY_VERTICAL_NAME: 'MF_summary_newuser',
  COMBO_CT_SUMMARY_VERTICAL_NAME: 'Combo_summary_newuser',
  STOCK_STATUS_VERTICAL_NAME: 'Stock_account_status',
  MF_STATUS_VERTICAL_NAME: 'MF_account_status',
  COMBO_STATUS_VERTICAL_NAME: 'Combo_account_status',
  STOCK_SIP_SUCCESS: 'SIP_success',
  MF_SIP_SUCCESS: 'SIP_MF_success',
  COMBO_SIP_SUCCESS: 'SIP_Combo_success',
};
export { NAMES, USER_ACTIONS, SCREEN, EVENT_TYPE };
