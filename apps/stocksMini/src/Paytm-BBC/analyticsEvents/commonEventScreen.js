const USER_ACTION = {
  FAQ_DROPDOWN: 'faq_dropdown_clicked',
  NEED_HELP_CLICK: 'need_help_clicked',
  CALL_US_CLICKED: 'call_us_clicked',
};

const SCREENS = {
  LANDING_SCREEN: 'Stocks_landing_page',
  LANDING_SCREEN1: 'MF_landing_page',
  LANDING_SCREEN2: 'Combo_landing_page',
  STATUS_SCREEN: 'Stock_account_status_page',
  STATUS_SCREEN_MF: 'MF_account_status_page',
  STATUS_SCREEN_COMBO: 'Combo_account_status_page',
  STOCK_SIP_SUCCESS: 'Stock_SIP_success',
  MF_SIP_SUCCESS: 'MF_SIP_success',
  COMBO_SIP_SUCCESS: 'Combo_SIP_success',
  STOCK_SCREEN: 'Stock_units_page',
  MF_SCREEN: 'MF_units_page',
  COMBO_STOCK_SCREEN: 'Combo_stock_units_page',
  COMBO_MF_SCREEN: 'Combo_units_mf_page',
  R<PERSON><PERSON><PERSON><PERSON>_SCREEN: 'Stock_summary_NU_page',
  REVIEW_SCREEN_MF: 'MF_summary_NU_page',
  REVIEW_SCREEN_COMBO: 'Combo_summary_NU_page',
};

const EVENT_CATEGORY = {
  EVENT_NAME: 'KYC + Non KYC',
  TYPE: 'onboarding',
  FLOW: 'BBC',
  EVENT_TYPE: 'Non KYC',
};
const NAME = {
  VERTICAL_NAME: 'Paytm Stocks',
  STOCK_CT_LANDING_VERTICAL_NAME: 'Stocks_SIP_initiated',
  MF_CT_LANDING_VERTICAL_NAME: 'MF_SIP_initiated',
  COMBO_CT_LANDING_VERTICAL_NAME: 'Combo_SIP_initiated',
  STOCK_STATUS_VERTICAL_NAME: 'Stock_account_status',
  MF_STATUS_VERTICAL_NAME: 'MF_account_status',
  COMBO_STATUS_VERTICAL_NAME: 'Combo_account_status',
  STOCK_SIP_SUCCESS: 'SIP_success',
  MF_SIP_SUCCESS: 'SIP_MF_success',
  COMBO_SIP_SUCCESS: 'SIP_Combo_success',
  STOCK_CT_SELCTION_VERTICAL_NAME: 'Stock_basket_creation',
  MF_CT_SELCTION_VERTICAL_NAME: 'MF_basket_creation',
  COMBO_STOCK_CT_SELCTION_VERTICAL_NAME: 'Combo_stock_basket_creation',
  COMBO_MF_CT_SELCTION_VERTICAL_NAME: 'Combo_mf_basket_creation',
  LANDING: 'landing',
  STOCK_CT_SUMMARY_VERTICAL_NAME: 'Stock_summary_newuser',
  MF_CT_SUMMARY_VERTICAL_NAME: 'MF_summary_newuser',
  COMBO_CT_SUMMARY_VERTICAL_NAME: 'Combo_summary_newuser',
};
export { NAME, USER_ACTION, SCREENS, EVENT_CATEGORY };
