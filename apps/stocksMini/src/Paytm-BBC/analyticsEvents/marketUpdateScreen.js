const USER_ACTION = {
  USER_LANDED_MARKET_UPDATE: 'User_landing_page_market_updates_landed',
  MARKET_UPDATE_SHARING: 'Market_updates_sharing',
  SHARING_MEDIUM: 'sharing_medium',
  MEDIUM_CHOOSEN: 'medium_chosen',
  MARKET_UPDTAE_BACK: 'Market_updates_back',
  YT_CREATOR: 'Creator_youtube',
  TWITTER_CREATOR: 'Creator_twitter',
  TILE_CLICKED: 'click_tile',
  VIEW_OPTION_CHAIN: 'View_option_chain',
  EXPIRY_OPTION_CHAIN: 'Expiry_option_chain',
  VIEW_FULL_OPTION_CHAIN: 'View_full_option_chain',
  VIEW_SPOT_FUTURE: 'View_Spot_Futures_price',
  SET_PRICE_ALERT: 'Set_price_alert',
  PRICE_ALERT_COMPLETED: 'Price_alert_completed',
  ADD_TO_WATCHLIST: 'Add_to_watchlist',
  WATCHLIST_ADDED: 'Watchlist_added',
  MARKET_OVERVIEW: 'Market_overview',
  MARKET_OVERIEW_WATCHLIST: 'Market_overview_watchlist',
  WATCHLIST_SET: 'Watchlist_set_market_overview',
  MORE_VIDEOS: 'more_videos',
  ALL_VIDEOS: 'all_videos',
};

const SCREENS = {
  LANDING_SCREEN_MARKET_UPDATE: 'Landing_page_market_updates',
};

const EVENT_CATEGORY = {
  EVENT_NAME: 'KYC + Non KYC',
  TYPE: 'onboarding',
  FLOW: 'BBC',
  EVENT_TYPE: 'Non KYC',
};
const NAME = {
  VERTICAL_NAME: 'Market_updates_landing_page',
};
export { NAME, USER_ACTION, SCREENS, EVENT_CATEGORY };
