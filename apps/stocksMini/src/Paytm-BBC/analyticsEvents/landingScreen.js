const USER_ACTION = {
  SELECT_DROPDOWN_CLICKED: 'drop_down_selected',
  PML_INFO_ICON_CLICKED: 'pml_info_clicked',
  DISCLAIMER_MODAL_OPEN: 'disclaimer_modal_open',
  BBC_PREOPTIN_STOCKS_CLICKED: 'BBC_preoptin_stocks_create_clicked',
  BBC_PREOPTIN_MF_CLICKED: 'BBC_preoptin_mf_create_clicked',
  BBC_PREOPTIN_COMBO_CLICKED: 'BBC_preoptin_combo_create_clicked',
  BBC_WIDGET_STOCKS_OPEN_DEMAT: 'stocks_open_demat_clicked',
  BBC_WIDGET_MF_OPEN_DEMAT: 'mf_open_demat_clicked',
  BBC_WIDGET_COMBO_OPEN_DEMAT: 'combo_open_demat_clicked',
  STOCKS_LANDING_PAGE_OPENED: 'BBC_stocks_LP1_landed',
  MF_LANDING_PAGE: 'BBC_mf_LP1_landed',
  <PERSON>MBO_LANDING_PAGE: 'BBC_combo_LP1_landed',
  <PERSON>Q_DROPDOWN: 'faq_dropdown_clicked',
  NEED_HELP_CLICK: 'need_help_clicked',
  OPTIN_MODAL_OPENED: 'optin_modal_loaded',
  VIDEO_CLICKED: 'video_carousal_clicked',
};

const SCREENS = {
  LANDING_SCREEN: 'Stocks_landing_page',
  LANDING_SCREEN1: 'MF_landing_page',
  LANDING_SCREEN2: 'Combo_landing_page',
  OPTIN_MODAL: 'optin_modal',
  STOCK_SCREEN: 'Stock_units_page',
};

const EVENT_CATEGORY = {
  EVENT_NAME: 'KYC + Non KYC',
  TYPE: 'onboarding',
  FLOW: 'BBC',
  EVENT_TYPE: 'Non KYC',
};
const NAME = {
  VERTICAL_NAME: 'Paytm Stocks',
  STOCK_CT_LANDING_VERTICAL_NAME: 'Stocks_SIP_initiated',
  MF_CT_LANDING_VERTICAL_NAME: 'MF_SIP_initiated',
  COMBO_CT_LANDING_VERTICAL_NAME: 'Combo_SIP_initiated',
  STOCK_CT_SELCTION_VERTICAL_NAME: 'Stock_basket_creation',
};
export { NAME, USER_ACTION, SCREENS, EVENT_CATEGORY };
