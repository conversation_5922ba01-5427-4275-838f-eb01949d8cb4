// For Paytm-BBC
@mixin bbcTypography(
  $size,
  $color: map-get($colors, primaryTextColor),
  $fontFamily: Inter
) {
  // B corresponds to font-weight bold or 700, B1 to 600, B2 to 500, default value is regular or 400.
  // R corresponds to regular text
  // If you need to add more, do it similarly.
  @if $size == heading2B {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == heading2BR {
    font-size: 20px;
  }
  @if $size == heading1B4 {
    font-size: 24px;
  }
  @if $size == heading1B {
    font-size: 24px;
    font-weight: 700;
  }
  @if $size == heading5B {
    font-size: 18px;
    font-weight: 700;
  }
  @if $size == heading2B {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size ==headingB1 {
    font-size: 20px;
    font-weight: 700;
  }
  @if $size ==heading2B1 {
    font-size: 20px;
    font-weight: 600;
  }
  @if $size == heading3B {
    font-size: 16px;
    font-weight: 700;
  }
  @if $size == heading3 {
    font-size: 16px;
  }
  @if $size == heading1B1 {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == heading2B1 {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.43;
  }
  @if $size == heading2B2 {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
  }
  @if $size == body1R {
    font-size: 14px;
    line-height: 1.43;
  }
  @if $size == bodyB {
    font-size: 14px;
    font-weight: 700;
  }
  @if $size == text4R {
    font-size: 12px;
    line-height: 1.33;
  }

  @if $size == body2 {
    font-size: 12px;
    line-height: 1.5;
  }
  @if $size == subTitle1B {
    font-size: 12px;
    font-weight: 700;
  }
  @if $size == subTitle1 {
    font-size: 12px;
  }
  @if $size == subTitle1B1 {
    font-size: 12px;
    font-weight: 600;
  }
  @if $size == body2B {
    font-size: 12px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == body2B2 {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
  }
  @if $size == body2R {
    font-size: 12px;
    line-height: 1.42;
  }
  @if $size == body3R2 {
    font-size: 10px;
    line-height: 1.3;
  }
  @if $size == body3R1 {
    font-size: 10px;
    font-weight: 600;
  }
  @if $size == body3RB {
    font-size: 10px;
    font-weight: 700;
  }
  @if $size == body3R {
    font-size: 10px;
    line-height: 1.4;
  }
  @if $size == body3R3 {
    font-size: 10px;
  }

  @if $color {
    color: $color;
  }

  @if $fontFamily {
    font-family: $fontFamily;
  }
}
