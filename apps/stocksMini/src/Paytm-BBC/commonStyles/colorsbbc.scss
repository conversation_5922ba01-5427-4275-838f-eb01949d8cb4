/* Define all colours */
// scss-lint:disable ColorVariable
$colorsbbc: (
  primaryBgColor: var(--primary-background-color, #ffffff),
  secondaryBgColor: var(--secondary-background-color, #f5f9fe),
  primaryTextColor: var(--primary-text-color, #101010),
  secondaryTextColor: var(--secondary-text-color, rgba(16, 16, 16, 0.7)),
  tertiaryTextColor: var(--tertiary-text-color, rgba(16, 16, 16, 0.5)),
  AliceBlue: #e7f1f8,
  DBlue: #00b8f5,
  DBlue1: #012a72,
  DBlue2: #1d2f54,
  DBlue3: #00b9f5,
  DBlue4: #012b72,
  PureWhite: #ffffff,
  Red: #fd5154,
  LRed: #ffebef,
  VLBlue: var(--text-color-14, rgba(245, 249, 254, 0.8)),
  Gray1: rgba(16, 16, 16, 0.1),
  Grey4: rgba(16, 16, 16, 0.22),
  Grey5: rgba(16, 16, 16, 0.07),
  Grey6: rgba(16, 16, 16, 0.2),
  Grey7: rgba(16, 16, 16, 0.12),
  LYellow: #fff8e1,
  DYellow: #ff9d00,
  ShadowColor1: rgba(1, 42, 114, 0.1),
  Shadow<PERSON>olor2: rgba(128, 152, 213, 0.06),
  Shadow<PERSON>olor3: rgba(0, 0, 0, 0.06),
  ShadowColor4: rgba(16, 16, 16, 0.15),
  LightBlue: rgba(245, 249, 254, 0.7),
  Black: #404040,
  DGrey: #494949,
  primaryGrey: var(--primary-grey, #adafb6),
  LGrey: var(--text-color-3, #eeeef0),
  PureBlack: #000000,
  LBlue: #00c1f2,
  Shimmer:
    var(
      --shimmer,
      linear-gradient(to right, #eff1f3 4%, #e2e2e2 25%, #eff1f3 36%)
    ),
  BgBlue: #003d9f,
  TextGrey: rgba(255, 255, 255, 0.7),
  BorderBlue2: rgba(1, 42, 114, 0.2),
  BgBlueShadow: rgba(0, 57, 165, 0.25),
  PureBlackAlpha: (
    0,
    0,
    0,
  ),
  StrongNavy: #27306a,
  SplashGrey: #979797,
  BgGrey: rgba(1, 42, 114, 0.8),
  Grey10: rgba(16, 16, 16, 0.54),
  Grey11: #8ba6c1,
  Grey12: #f3f4f8,
  Grey14: #506d85,
  Grey15: rgba(170, 166, 166, 0.1),
  Grey17: #e8edf3,
  DisabledGray: #6f6f6f,
  LightGrey: rgba(16, 16, 16, 0.13),
  Green4: #51bc83,
  Green5: #21c179,
  Red3: #fb5252,
  Red4: #eb5757,
  Blue6: #3e74dd,
  Tara: #e3f6ec,
  ClearDay: #e0f5fd,
  DBlack4: #54575c,
  Blue8: #e9f2fe,
  DGrey2: var(--text-color-1, #727682),
  OffWhite: #f5f9fe,
  ETFLightGreen: #c2e6cd,
);

:export {
  @each $key, $value in $colorsbbc {
    #{$key}: $value;
  }
}
