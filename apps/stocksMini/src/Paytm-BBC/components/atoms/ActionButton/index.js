import React from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import styles from './style.scss';
import { ButtonSizes, ButtonStyle, ButtonVariant } from './enum';
import LoadingAnimation from '../LoadingAnimation/LoadingAnimation';

const variantStyle = {
  primary: styles.primary,
  rounded: styles.rounded,
  outline: styles.outline,
  sm: styles.sm,
  md: styles.md,
  lg: styles.lg,
  block: styles.block,
};

const ActionButton = ({
  id,
  onClickHandler,
  disabled,
  className,
  children,
  variant,
  theme,
  size,
  isLoading,
}) => {
  const classProps = classNames({
    [styles.button]: true,
    [className]: className,
    [variantStyle[variant]]: variant,
    [variantStyle[theme]]: theme,
    [variantStyle[size]]: size,
    [styles.disabled]: disabled || isLoading,
  });
  return (
    <button
      id={id}
      disabled={disabled || isLoading}
      onClick={onClickHandler}
      className={classProps}
    >
      {isLoading ? <LoadingAnimation /> : children}
    </button>
  );
};

ActionButton.defaultProps = {
  id: '',
  disabled: false,
  className: '',
  onClickHandler: () => {},
  theme: ButtonStyle.Rounded,
  size: ButtonSizes.Small,
  variant: ButtonVariant.Outline,
  isLoading: false,
};

ActionButton.propTypes = {
  id: PropTypes.string,
  disabled: PropTypes.bool,
  onClickHandler: PropTypes.func,
  className: PropTypes.string,
  theme: PropTypes.string,
  size: PropTypes.string,
  variant: PropTypes.string,
  isLoading: PropTypes.bool,
};

export default ActionButton;
