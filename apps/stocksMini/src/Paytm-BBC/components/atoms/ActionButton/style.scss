@import '../../../../commonStyles/commoncss.scss';
@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';;
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: none;
  outline: none;
}
.primary {
  background: map-get($colorsbbc, DBlue);
  @include bbcTypography(subTitle1B1, map-get($colorsbbc, PureWhite));
}
.rounded {
  border-radius: 96px;
}
.outline {
  background: map-get($colorsbbc, PureWhite);
  @include bbcTypography(subTitle1B1, map-get($colorsbbc, DBlue));
  border: 2px solid map-get($colorsbbc, DBlue) !important ;
  padding: 0.75em 1em !important;
}

.sm {
  padding: 0.5em 0.6em;
}
.md {
  padding: 1.1em 1.3em;
}
.lg {
  padding: 1.7em 2.4em;
}
.block {
  display: block;
  width: 100%;
  padding: 14px 28px;
  cursor: pointer;
  text-align: center;
}
.disabled {
  cursor: not-allowed;
  pointer-events: none;
  color: map-get($colorsbbc, Grey6);
  background-color: map-get($colorsbbc, Grey7);
}
