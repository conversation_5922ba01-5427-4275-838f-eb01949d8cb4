import React from 'react';
import { BACK_TO_HOME } from '@src/Paytm-BBC/utils/Constants';
import HomeIcon from '@src/Paytm-BBC/assets/icons/home_icon.svg';
import { paytmExitSession } from '@src/utils/bridgeUtils';
import { log } from '@src/utils/commonUtils';
import styles from './BackToHome.scss';

const BackToHome = () => {
  const exitSession = () => {
    paytmExitSession(data => {
      log('session exit', data);
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.iconcontainer} onClick={exitSession}>
        <img src={HomeIcon} alt="HOME" />
        <div className={styles.text}>{BACK_TO_HOME}</div>
      </div>
    </div>
  );
};

export default BackToHome;
