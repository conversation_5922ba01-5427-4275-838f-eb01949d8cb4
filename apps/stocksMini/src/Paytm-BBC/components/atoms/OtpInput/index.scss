@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.otpInputContainer {
  display: flex;
  justify-content: space-between;
  position: relative;

  > div:last-child {
    margin: 0;
  }
}

.otpInput {
  @include bbcTypography(heading1B1, map-get($colorsbbc, primaryTextColor));
  line-height: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 12px 0 0;
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid rgba(16, 16, 16, 0.13);

  // six digit OTP
  &:nth-child(2):nth-last-child(6),
  &:nth-child(2):nth-last-child(6) ~ div {
    height: 48px;
    min-width: 32px;
    max-width: 48px;
  }

  :nth-child(6) {
    margin-right: 0px;
  }
}

.input {
  @include bbcTypography(heading1B1, transparent);
  width: 100%;
  border-radius: 8px;
  height: 100%;
  border: 0;
  padding: 0;
  position: absolute;
  text-align: center;
  background-color: transparent;
  outline: none;
  opacity: 0;
}

.focusedOtpInput {
  border: 1px solid #00b8f5;

  .blinkCursor {
    height: 24px;
    width: 2px;
    background-color: #00b8f5;
    animation: blinker 1s linear infinite;
  }
  @keyframes blinker {
    50% {
      opacity: 0;
    }
  }
}
