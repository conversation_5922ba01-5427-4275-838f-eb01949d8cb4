import { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';

import { log } from '@src/utils/commonUtils';
import { REGEX } from '@src/config/common';

const useOtpInput = ({
  otpLength,
  handleOnSubmit,
  resetError,
  autoSubmit,
  inProgress,
}) => {
  const [otpInput, setOtpInput] = useState(new Array(otpLength).fill(''));
  const [inputVal, setinputVal] = useState('');
  const [activeIndex, setActiveIndex] = useState(null);

  const isValidNumberArray = useCallback(
    otpArr => otpArr.every(otpVal => /^[0-9]$/.test(otpVal)),
    [],
  );

  const resetOtpInput = useCallback(() => {
    setOtpInput(new Array(otpLength).fill(''));
    setinputVal('');
    setActiveIndex(0);
    if (resetError) {
      resetError(false);
    }
  }, []);

  const handleOnClick = useCallback(
    (e, inputError) => {
      if (inputError) {
        return resetOtpInput();
      }
      // set cursor positon to end of input
      const inputValue = e.target.value;
      e.target.value = '';
      e.target.value = inputValue;
    },
    [resetOtpInput],
  );

  const handleInputFocus = useCallback(input => {
    log('handleInputFocus-------');
    if (input.length >= 0 && input.length < otpLength) {
      setActiveIndex(input.length);
    } else if (input.length === otpLength) {
      setActiveIndex(otpLength - 1);
    }
  }, []);

  const handleOnBlur = useCallback(() => {
    log('handleOnBlur-------');
    setActiveIndex(null);
  }, []);

  const handleOtpInputValue = useCallback(
    input => {
      const updatedOtp = [...otpInput];
      const isValidNumber = REGEX.VALIDATE_NUMBER.test(input);
      if (isValidNumber) {
        setinputVal(input);
        handleInputFocus(input);
        const inputArr = input.split('');
        for (let i = 0; i < otpLength; i += 1) {
          if (inputArr[i]) {
            updatedOtp[i] = inputArr[i];
          } else {
            updatedOtp[i] = '';
          }
        }
        setOtpInput(updatedOtp);
        return true;
      }
      return false;
    },
    [handleInputFocus, otpInput],
  );

  const handleOnInputChange = useCallback(
    e => {
      const input = e.target.value.trim().slice(0, otpLength);
      e.target.value = input;
      if (input) {
        if (!handleOtpInputValue(input)) {
          e.preventDefault();
        }
      } else {
        resetOtpInput();
      }
    },
    [handleOtpInputValue, resetOtpInput],
  );

  const handleOnKeyDown = useCallback(
    (e, inputError, requestInProgress) => {
      if (inputError) {
        return resetOtpInput();
      }
      switch (e.key) {
        case 'Enter': {
          if (!autoSubmit && !requestInProgress) {
            if (isValidNumberArray(otpInput)) {
              handleOnSubmit(otpInput);
            }
          }
          break;
        }
        case 'e':
        case 'E':
        case '+':
        case '-':
        case '.':
        case 'ArrowRight':
        case 'ArrowLeft':
        case 'ArrowDown':
        case 'ArrowUp': {
          e.preventDefault();
          break;
        }
        default:
          break;
      }
    },
    [resetOtpInput, isValidNumberArray, otpInput],
  );

  useEffect(() => {
    if (autoSubmit && !inProgress) {
      if (isValidNumberArray(otpInput)) {
        return handleOnSubmit(otpInput);
      }
    }
  }, [isValidNumberArray, otpInput]);

  return {
    otpInput,
    activeIndex,
    inputVal,
    handleOnInputChange,
    handleOnKeyDown,
    handleInputFocus,
    handleOnBlur,
    handleOnClick,
    resetOtpInput,
    isValidNumberArray,
    handleOtpInputValue,
  };
};

useOtpInput.propTypes = {
  otpLength: PropTypes.number,
  handleOnSubmit: PropTypes.func,
  resetError: PropTypes.func,
  autoSubmit: PropTypes.bool,
  inProgress: PropTypes.bool,
};

useOtpInput.defaultProps = {
  otpLength: 6,
  handleOnSubmit: () => {},
  resetError: null,
  autoSubmit: true,
  inProgress: false,
};

export default useOtpInput;
