import React, { memo, useEffect, useRef } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import { log } from '@src/utils/commonUtils';

import styles from './index.scss';

const OtpInput = ({
  type,
  otpInput,
  activeIndex,
  inputVal = '',
  handleOnInputChange,
  handleOnKeyDown,
  handleInputFocus,
  handleOnBlur,
  handleOnClick,
  isInValidOtp = false,
  autoFocusInput = true,
  inProgress = false,
}) => {
  const otpInputRef = useRef(null);

  useEffect(() => {
    log(
      'document.hasFocus(), autoFocusInput---',
      document.hasFocus(),
      autoFocusInput,
    );
    if (autoFocusInput) {
      otpInputRef.current.focus();
    }
  }, [autoFocusInput]);

  return (
    <div className={styles.otpInputContainer}>
      <>
        <input
          type={type}
          value={inputVal}
          className={styles.input}
          min="0"
          max="9"
          pattern="[0-9]"
          onChange={handleOnInputChange}
          onFocus={() => handleInputFocus(inputVal)}
          onKeyDown={e => handleOnKeyDown(e, isInValidOtp, inProgress)}
          onClick={e => handleOnClick(e, isInValidOtp)}
          onBlur={handleOnBlur}
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={autoFocusInput}
          ref={otpInputRef}
          autoComplete="one-time-code"
        />
        {otpInput.map((otpVal, ind) => (
          <div
            className={cx(styles.otpInput, {
              [styles.focusedOtpInput]: ind === activeIndex,
            })}
            id={ind}
            key={ind}
          >
            {ind === activeIndex && !otpVal ? (
              <span className={styles.blinkCursor} />
            ) : (
              otpVal
            )}
          </div>
        ))}
      </>
    </div>
  );
};

OtpInput.propTypes = {
  type: PropTypes.string,
  otpInput: PropTypes.array,
  handleOnInputChange: PropTypes.func,
  handleOnKeyDown: PropTypes.func,
  handleInputFocus: PropTypes.func,
  handleOnBlur: PropTypes.func,
  handleOnClick: PropTypes.func,
};

OtpInput.defaultProps = {
  type: 'tel',
  otpInput: [],
  handleOnInputChange: () => {},
  handleOnKeyDown: () => {},
  handleInputFocus: () => {},
  handleOnBlur: () => {},
  handleOnClick: () => {},
};

export default memo(OtpInput);
