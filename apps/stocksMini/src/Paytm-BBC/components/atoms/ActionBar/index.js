import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import styles from './index.scss';

function ActionBar({
  iconComponent = null,
  textComponent,
  ctaComponent = null,
  wrapperCss = '',
}) {
  return (
    <div className={cx(styles.actionBar, wrapperCss)}>
      <div className={styles.group}>
        {/* Icon section */}
        {iconComponent && (
          <div className={styles.iconSection}>{iconComponent}</div>
        )}
        {/* Text section */}
        {textComponent}
      </div>
      {/* CTA section */}
      {ctaComponent && ctaComponent}
    </div>
  );
}

ActionBar.propTypes = {
  iconComponent: PropTypes.element,
  textComponent: PropTypes.oneOfType([PropTypes.string, PropTypes.element])
    .isRequired,
  ctaComponent: PropTypes.element,
  wrapperCss: PropTypes.string,
};

ActionBar.defaultProps = {
  iconComponent: null,
  ctaComponent: null,
  wrapperCss: '',
};

export default ActionBar;
