@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.headingContainer {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  padding-top: 10px;
  padding-left: 16px;
  margin-top: 10px;
  background-color: map-get($colorsbbc, PureWhite);
  background: map-get($colorsbbc, OffWhite);

  .name {
    @include bbcTypography(heading2B1, map-get($colorsbbc, PureBlack));
  }

  .relationDobRow {
    display: flex;
    padding-bottom: 10px;
    align-items: center;

    .separator {
      height: 3px;
      width: 3px;
      border: 3px solid map-get($colorsbbc, Grey10);
      border-radius: 50%;
      margin: 0 10px;
    }
    .relationDob {
      @include bbcTypography(body1R, map-get($colorsbbc, Grey10));
    }
  }
}
