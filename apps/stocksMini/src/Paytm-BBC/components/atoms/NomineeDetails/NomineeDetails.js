import React, { useEffect, useState } from 'react';

import styles from './NomineeDetails.scss';

const NomineeDetails = ({ nomineeDetails = [] }) => {
  const [nomineeData, setnomineeData] = useState({
    name: '',
    relation: '',
    dob: '',
  });
  const { name, relation, dob } = nomineeData;

  useEffect(() => {
    let max = 0;
    let maxIndex = 0;
    if (nomineeDetails.length > 1) {
      for (let index = 0; index < nomineeDetails.length; index += 1) {
        if (nomineeDetails[index].percentage > max) {
          max = nomineeDetails[index].percentage;
          maxIndex = index;
        }
      }
    }
    setnomineeData(nomineeDetails[maxIndex]);
  }, [nomineeDetails]);

  return (
    <div className={styles.headingContainer}>
      {name && <div className={styles.name}>{name}</div>}
      <div className={styles.relationDobRow}>
        {relation && <div className={styles.relationDob}>{relation}</div>}
        <div className={styles.separator} />
        {dob && <div className={styles.relationDob}>{dob}</div>}
      </div>
    </div>
  );
};

export default NomineeDetails;
