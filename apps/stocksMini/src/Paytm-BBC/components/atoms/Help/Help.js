import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { useRootContext } from '@src/Paytm-BBC/context/root/RootContext';
import { sendNeedHelpClickEvent } from '@src/Paytm-BBC/analyticsEvents/commonEventutil';
import { deepLinkNeedHelp } from '@src/utils/navigationUtil';
import { useCartContext } from '@src/Paytm-BBC/context/cart/CartProvider';
import styles from './Help.scss';
import { NEED_HELP_CONST } from '../../../utils/Constants';

const Help = ({ customClass }) => {
  const { value: { flowType, sipAmount = 0 } = {} } = useRootContext() || {};
  const getIrstatus = sessionStorage.getItem('irStatus') || '';
  const { cartTotal } = useCartContext() || {};
  const cartTotalPrice = cartTotal(flowType) || 0;

  const navigateToAskForHelp = () => {
    sendNeedHelpClickEvent(flowType, sipAmount, getIrstatus, cartTotalPrice);
    deepLinkNeedHelp();
  };

  return (
    <>
      <div
        className={cx(styles.help, customClass)}
        onClick={navigateToAskForHelp}
      >
        <span>{NEED_HELP_CONST.NEED_HELP}</span>
      </div>
    </>
  );
};

Help.propTypes = {
  customClass: PropTypes.string,
};

Help.defaultProps = {
  customClass: '',
};

export default Help;
