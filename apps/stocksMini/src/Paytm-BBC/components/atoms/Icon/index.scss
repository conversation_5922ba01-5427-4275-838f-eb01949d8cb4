@import '../../../../commonStyles/commoncss.scss';
@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';;
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.initials {
  padding: 3px 2px;
  border: solid 1px map-get($colorsbbc, Gray1);
  border-radius: 8px;
  box-sizing: border-box;
  flex: none;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  color: map-get($colorsbbc, secondaryTextColor);
}

.borderRadius {
  //border-radius: 15px;
}

.hideLogo {
  display: none;
}

.companyIcon {
  padding: 3px 2px;
  border: solid 1px map-get($colorsbbc, Gray1);
  border-radius: 8px;
  box-sizing: border-box;
  flex: none;

  img {
    width: 100%;
  }
}

.mediumLogo {
  height: 30px;
  width: 30px;
}

.largeLogo {
  height: 48px;
  width: 48px;
}

.averageLogo {
  height: 40px;
  width: 40px;
}

.mediumFallbackLogo {
  > * {
    font-size: 20px;
    height: 30px;
    width: 30px;
  }
}

.averageFallbackLogo {
  > * {
    font-size: 30px;
    height: 40px;
    width: 40px;
  }
}

.largeFallbackLogo {
  > * {
    font-size: 34px;
    height: 48px;
    width: 48px;
  }
}
