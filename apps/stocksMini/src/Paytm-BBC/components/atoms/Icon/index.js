/* eslint-disable no-use-before-define */
import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { isDarkMode } from '@src/utils/commonUtils';
import { getLogo } from '@src/Paytm-BBC/utils/commonUtils';
import ICONS, { ICONS_NAME, STATICS, THEME, LOGO_SIZE } from './enum';

import styles from './index.scss';

function Icon({
  name,
  size,
  width,
  className,
  onClick,
  iconStyles,
  showDarkModeIcon,
  inLineStyles,
  zIndex,
  companyName,
  fallbackStyle,
  logoSize,
  url,
  onImgLoad,
  testId = '',
}) {
  // prop showDarkModeIcon is to forcibly show darkModeIcon (UseCase: Custom Header)
  const activeTheme = useRef(isDarkMode() ? THEME.DARK : THEME.LIGHT);
  const [isCompanyLogoLoaded, setIsCompanyLogoLoaded] = useState(false);
  const [isLogoFailed, setIsLogoFailed] = useState(false);

  useEffect(() => {
    if (companyName) {
      const img = new Image();
      img.onload = () => {
        setIsCompanyLogoLoaded(true);
        setIsLogoFailed(false);
      };
      img.onerror = () => {
        setIsLogoFailed(true);
        setIsCompanyLogoLoaded(false);
      };
      img.crossOrigin = 'anonymous';
      img.src = getLogo(name);
    }
  }, [companyName, name]);

  function getSrc() {
    if (companyName) {
      return (
        <>
          {!isLogoFailed && (
            <img
              className={cx('', {
                [iconStyles]: iconStyles,
                [styles.hideLogo]: !isCompanyLogoLoaded,
              })}
              crossOrigin="anonymous"
              src={getLogo(name)}
              style={
                inLineStyles || {
                  zIndex,
                }
              }
              alt=""
            />
          )}

          {!isCompanyLogoLoaded && (
            <span
              className={cx(styles.initials, {
                [fallbackStyle]: fallbackStyle,
              })}
              data-testid={testId || name}
            >
              {companyName.slice(0, 1).toUpperCase()}
            </span>
          )}
        </>
      );
    }

    return (
      <img
        className={cx('', {
          [iconStyles]: iconStyles,
          [styles.borderRadius]: companyName,
        })}
        crossOrigin="anonymous"
        src={
          url ||
          (showDarkModeIcon
            ? ICONS[THEME.DARK][name]
            : ICONS[activeTheme.current][name])
        }
        style={
          inLineStyles || {
            width: width || size * STATICS.SIZE_MULTIPLIER,
            objectFit: 'contain',
            zIndex,
          }
        }
        alt=""
        onLoad={onImgLoad}
        data-testid={testId || name}
      />
    );
  }

  return (
    <div
      className={cx(styles.wrapper, {
        [className]: className,
        [styles.companyIcon]: isCompanyLogoLoaded && companyName,
        ...getLogoStyle(),
      })}
      onClick={onClick}
    >
      {getSrc()}
    </div>
  );

  function getLogoStyle() {
    // for company Icons
    if (isCompanyLogoLoaded) {
      switch (logoSize) {
        case LOGO_SIZE.MEDIUM:
          return {
            [styles.mediumLogo]: true,
          };
        case LOGO_SIZE.AVERAGE:
          return {
            [styles.averageLogo]: true,
          };
        case LOGO_SIZE.LARGE:
          return {
            [styles.largeLogo]: true,
          };
        default:
          return '';
      }
    } else if (companyName) {
      switch (logoSize) {
        case LOGO_SIZE.MEDIUM:
          return {
            [styles.mediumFallbackLogo]: true,
          };
        case LOGO_SIZE.AVERAGE:
          return {
            [styles.averageFallbackLogo]: true,
          };
        case LOGO_SIZE.LARGE:
          return {
            [styles.largeFallbackLogo]: true,
          };
        default:
          return '';
      }
    }
    return '';
  }
}

Icon.propTypes = {
  name: PropTypes.string,
  size: PropTypes.number,
  zIndex: PropTypes.number,
  className: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onClick: PropTypes.func,
  iconStyles: PropTypes.string,
  showDarkModeIcon: PropTypes.bool,
  fallbackStyle: PropTypes.string,
  logoSize: PropTypes.string,
};

Icon.defaultProps = {
  name: ICONS_NAME.PLAY,
  size: 1,
  zIndex: 0,
  className: '',
  width: '',
  iconStyles: '',
  onClick: () => {},
  showDarkModeIcon: false,
  fallbackStyle: '',
  logoSize: LOGO_SIZE.MEDIUM,
};

export { Icon as default, ICONS_NAME, LOGO_SIZE };
