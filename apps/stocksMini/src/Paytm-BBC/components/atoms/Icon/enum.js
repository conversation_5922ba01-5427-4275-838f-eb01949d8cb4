import backIconBBC from '@src/Paytm-BBC/assets/icons/backIconBBC.svg';
import backIconDarkMode from '@src/assets/icons/backIcon_darkMode.png';
import CheckboxChecked from '@src/Paytm-BBC/assets/icons/checkbox_checked.png';
import CheckboxUnchecked from '@src/Paytm-BBC/assets/icons/checkbox_unchecked.png';
import Close from '@src/assets/icons/close.png';
import CloseDark from '@src/assets/icons/closeDark.png';
import ArrowUpGreen from '@src/Paytm-BBC/assets/icons/arrow_up_green.png';
import BankLogo from '@src/assets/icons/bank_logo.png';
import BlackArrowUp from '@src/Paytm-BBC/assets/icons/blackArrowUp.svg';
import BlackArrowDown from '@src/Paytm-BBC/assets/icons/arrowDownBlack.svg';
import WhiteCircularTick from '@src/assets/icons/white_circular_tick.png';
import Cancel from '@src/Paytm-BBC/assets/icons/cancel.png';
import UpArrowImg from '@src/assets/icons/upArrow.svg';
import DownArrowImg from '@src/assets/icons/downArrow.svg';
import UpAndDownArrowImg from '@src/assets/icons/upAndDownArrow.png';
import miniAppCheckedImg from '@src/assets/icons/mini_app_checked.png';
import unCheckedImgDark from '@src/Paytm-BBC/assets/icons/checkbox1_unchecked.png';
import editIcon from '@src/Paytm-BBC/assets/icons/icons_edit.png';
import CloseWhite from '@src/Paytm-BBC/assets/icons/close_white.png';
import MiniBackButtonWhite from '@src/assets-offline/icons/backArrowWhite.svg';
import CheckboxOn from '@src/Paytm-BBC/assets/icons/checkbox_on.png';
import CheckboxOff from '@src/Paytm-BBC/assets/icons/checkbox_off.png';
import videoLearn from '@src/Paytm-BBC/assets/icons/video_learn.svg';
import SuccessIcon from '@src/Paytm-BBC/assets/icons/success.svg';
import UncheckedGrayCircle from '@src/Paytm-BBC/assets/icons/awaiting.svg';
import UncheckedGreenCircle from '@src/Paytm-BBC/assets/icons/awaitingGreen.svg';
import Processing from '@src/Paytm-BBC/assets/icons/processing.svg';
import ProcessingError from '@src/Paytm-BBC/assets/icons/ErrorProcessing.svg';
import upiIcon from '@src/Paytm-BBC/assets/icons/upiIcon.png';
import autoPayPendingIcon from '@src/Paytm-BBC/assets/icons/autopay_pending.png';
import mandateNotExitsIcon from '@src/Paytm-BBC/assets/icons/mandateNotExists.png';
import dismiss from '@src/Paytm-BBC/assets/icons//dismiss.png';
import downArrowRed from '@src/Paytm-BBC/assets/icons/downArrowRed.png';
import upArrowGreen from '@src/Paytm-BBC/assets/icons/upArrowGreen.png';

const THEME = {
  DARK: 'DARK',
  LIGHT: 'LIGHT',
};

const ICONS_NAME = {
  BACK: 'BACK',
  CHECKBOX_CHECKED: 'CHECKBOX_CHECKED',
  CHECKBOX_UNCHECKED: 'CHECKBOX_UNCHECKED',
  CLOSE: 'CLOSE',
  MINI_BACK_BUTTON_WHITE: 'MINI_BACK_BUTTON_WHITE',
  ARROW_UP_GREEN: 'ARROW_UP_GREEN',
  BANK_LOGO: 'BANK_LOGO',
  BLACK_ARROW_UP: 'BLACK_ARROW_UP',
  BLACK_ARROW_DOWN: 'BLACK_ARROW_DOWN',
  WHITE_CIRCULAR_GREEN_TICK: 'WHITE_CIRCULAR_GREEN_TICK',
  CANCEL: 'CANCEL',
  MINI_APP_CHECKED: 'MINI_APP_CHECKED',
  UNCHECKED_DARK: 'UNCHECKED_DARK',
  UP_ARROW: 'UP_ARROW',
  DOWN_ARROW: 'DOWN_ARROW',
  UP_AND_DOWN_ARROW: 'UP_AND_DOWN_ARROW',
  EDIT_ICON: 'EDIT_ICON',
  CLOSE_WHITE: 'CLOSE_WHITE',
  CHECKBOX_ON: 'CHECKBOX_ON',
  CHECKBOX_OFF: 'CHECKBOX_OFF',
  WARNING: 'WARNING',
  VIDEO_LEARN: 'VIDEO_LEARN',
  INFO_ICON_RED: 'INFO_ICON_RED',
  SUCCESS: 'SUCCESS',
  UNCHECKED_GRAY_CIRCLE: 'UNCHECKED_GRAY_CIRCLE',
  UNCHECKED_GREEN_CIRCLE: 'UNCHECKED_GREEN_CIRCLE',
  PROCESSING: 'PROCESSING',
  PROCESSING_ERR: 'PROCESSING_ERR',
  UPI_ICON: 'UPI_ICON',
  AUTOPAY_PENDING: 'AUTOPAY_PENDING',
  MANDATE_NOT_EXISTS: 'MANDATE_NOT_EXISTS',
  DISMISS_ICON: 'DISMISS_ICON',
  DOWN_ARROW_RED: 'DOWN_ARROW_RED',
  UP_ARROW_GREEN: 'UP_ARROW_GREEN',
};

const ICONS = {
  [THEME.LIGHT]: {
    [ICONS_NAME.BACK]: backIconBBC,
    [ICONS_NAME.CHECKBOX_CHECKED]: CheckboxChecked,
    [ICONS_NAME.CHECKBOX_UNCHECKED]: CheckboxUnchecked,
    [ICONS_NAME.CLOSE]: Close,
    [ICONS_NAME.MINI_BACK_BUTTON_WHITE]: MiniBackButtonWhite,
    [ICONS_NAME.ARROW_UP_GREEN]: ArrowUpGreen,
    [ICONS_NAME.BLACK_ARROW_UP]: BlackArrowUp,
    [ICONS_NAME.BLACK_ARROW_DOWN]: BlackArrowDown,
    // TODO: remove when we have actual icon
    [ICONS_NAME.BANK_LOGO]: BankLogo,
    [ICONS_NAME.WHITE_CIRCULAR_GREEN_TICK]: WhiteCircularTick,
    [ICONS_NAME.CANCEL]: Cancel,
    [ICONS_NAME.MINI_APP_CHECKED]: miniAppCheckedImg,
    [ICONS_NAME.UNCHECKED_DARK]: unCheckedImgDark,
    [ICONS_NAME.UP_ARROW]: UpArrowImg,
    [ICONS_NAME.DOWN_ARROW]: DownArrowImg,
    [ICONS_NAME.UP_AND_DOWN_ARROW]: UpAndDownArrowImg,
    [ICONS_NAME.EDIT_ICON]: editIcon,
    [ICONS_NAME.CLOSE_WHITE]: CloseWhite,
    [ICONS_NAME.CHECKBOX_ON]: CheckboxOn,
    [ICONS_NAME.CHECKBOX_OFF]: CheckboxOff,
    [ICONS_NAME.VIDEO_LEARN]: videoLearn,
    [ICONS_NAME.SUCCESS]: SuccessIcon,
    [ICONS_NAME.UNCHECKED_GRAY_CIRCLE]: UncheckedGrayCircle,
    [ICONS_NAME.UNCHECKED_GREEN_CIRCLE]: UncheckedGreenCircle,
    [ICONS_NAME.PROCESSING]: Processing,
    [ICONS_NAME.PROCESSING_ERR]: ProcessingError,
    [ICONS_NAME.UPI_ICON]: upiIcon,
    [ICONS_NAME.AUTOPAY_PENDING]: autoPayPendingIcon,
    [ICONS_NAME.MANDATE_NOT_EXISTS]: mandateNotExitsIcon,
    [ICONS_NAME.DISMISS_ICON]: dismiss,
    [ICONS_NAME.DOWN_ARROW_RED]: downArrowRed,
    [ICONS_NAME.UP_ARROW_GREEN]: upArrowGreen,
  },
  [THEME.DARK]: {
    [ICONS_NAME.BACK]: backIconDarkMode,
    [ICONS_NAME.CHECKBOX_CHECKED]: CheckboxChecked,
    [ICONS_NAME.CHECKBOX_UNCHECKED]: CheckboxUnchecked,
    [ICONS_NAME.CLOSE]: CloseDark,
    [ICONS_NAME.MINI_BACK_BUTTON_WHITE]: MiniBackButtonWhite,
    [ICONS_NAME.ARROW_UP_GREEN]: ArrowUpGreen,
    [ICONS_NAME.BLACK_ARROW_UP]: BlackArrowUp,
    [ICONS_NAME.CANCEL]: Cancel,
    [ICONS_NAME.WHITE_CIRCULAR_GREEN_TICK]: WhiteCircularTick,
    [ICONS_NAME.MINI_APP_CHECKED]: miniAppCheckedImg,
    [ICONS_NAME.UNCHECKED_DARK]: unCheckedImgDark,
    [ICONS_NAME.UP_ARROW]: UpArrowImg,
    [ICONS_NAME.DOWN_ARROW]: DownArrowImg,
    [ICONS_NAME.UP_AND_DOWN_ARROW]: UpAndDownArrowImg,
    [ICONS_NAME.EDIT_ICON]: editIcon,
    [ICONS_NAME.CLOSE_WHITE]: CloseWhite,
    [ICONS_NAME.CHECKBOX_ON]: CheckboxOn,
    [ICONS_NAME.CHECKBOX_OFF]: CheckboxOff,
    [ICONS_NAME.VIDEO_LEARN]: videoLearn,
    [ICONS_NAME.SUCCESS]: SuccessIcon,
    [ICONS_NAME.UNCHECKED_GRAY_CIRCLE]: UncheckedGrayCircle,
    [ICONS_NAME.UNCHECKED_GREEN_CIRCLE]: UncheckedGreenCircle,
    [ICONS_NAME.PROCESSING]: Processing,
    [ICONS_NAME.PROCESSING_ERR]: ProcessingError,
    [ICONS_NAME.UPI_ICON]: upiIcon,
    [ICONS_NAME.AUTOPAY_PENDING]: autoPayPendingIcon,
    [ICONS_NAME.MANDATE_NOT_EXISTS]: mandateNotExitsIcon,
    [ICONS_NAME.DISMISS_ICON]: dismiss,
    [ICONS_NAME.DOWN_ARROW_RED]: downArrowRed,
    [ICONS_NAME.UP_ARROW_GREEN]: upArrowGreen,
  },
};

const STATICS = {
  SIZE_MULTIPLIER: 5,
};

const LOGO_SIZE = {
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  AVERAGE: 'AVERAGE',
};

export { ICONS_NAME, STATICS, THEME, LOGO_SIZE, ICONS as default };
