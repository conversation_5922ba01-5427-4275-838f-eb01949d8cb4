import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { sanitize } from 'dompurify';
import arrowDown from '@src/Paytm-BBC/assets/icons/arrowDownBlack.svg';
import arrowUp from '@src/Paytm-BBC/assets/icons/blackArrowUp.svg';
import styles from './Accordion.scss';

const Accordion = ({ title, content, className, isOpen, toggleAccordion }) => (
  <>
    <div
      className={cx(styles.titleContainer, { [className]: className })}
      onClick={toggleAccordion}
    >
      {title}
      <img alt="" src={isOpen ? arrowUp : arrowDown} className={styles.icon} />
    </div>
    {isOpen ? (
      <span
        className={styles.content}
        dangerouslySetInnerHTML={{
          __html: sanitize(content, { USE_PROFILES: { html: true } }),
        }}
      />
    ) : null}
  </>
);

Accordion.propTypes = {
  title: PropTypes.any,
  content: PropTypes.any,
  className: PropTypes.string,
  isOpen: PropTypes.bool,
};

Accordion.defaultProps = {
  title: '',
  content: '',
  className: '',
  isOpen: false,
};

export default Accordion;
