import React from 'react';
import PropTypes from 'prop-types';
import styles from './style.scss';

const Link = props => {
  const { text, onClickHandler, path, customClass } = props;
  return (
    <span>
      <a
        href={path}
        onClick={onClickHandler}
        className={`${styles.link} ${customClass}`}
      >
        {text}
      </a>
    </span>
  );
};

Link.propTypes = {
  text: PropTypes.string,
  onClickHandler: PropTypes.func,
  path: PropTypes.string,
  customClass: PropTypes.string,
};

Link.defaultProps = {
  text: 'link',
  onClickHandler: () => {},
  path: '#',
  customClass: '',
};
export default Link;
