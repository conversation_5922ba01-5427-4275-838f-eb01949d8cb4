@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';
@import '../../../../commonStyles/commoncss.scss';



.text {
  @include bbcTypography(body1R, map-get($colorsbbc, PureBlack));
  line-height: 20px;

  >span {
    @include bbcTypography(heading2B1, map-get($colorsbbc, PureBlack));
  }
}

.container {
  margin-top: 5px;
  margin-bottom: 18px;
  display: flex;
  flex-direction: column;

  .subHeading {
    margin-top: 15px;
    @include bbcTypography(body1R, map-get($colorsbbc, PureBlack));
  }

  .details {
    @include noScrollBar;
    display: flex;
    flex-direction: column;
    overflow-x: auto;

    .phones {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 10px;
      white-space: nowrap;

      .textArray {
        padding: 4px 8px;
        margin-right: 6px;
        background: map-get($colorsbbc, AliceBlue);
        border-radius: 3px;
        @include bbcTypography(text4R, map-get($colorsbbc, PureBlack));
      }
    }

    .emails {
      display: flex;
      flex-direction: row;
      align-items: center;
      white-space: nowrap;

      .textArray {
        padding: 4px 8px;
        margin-right: 6px;
        background: map-get($colorsbbc, AliceBlue);
        border-radius: 3px;
        @include bbcTypography(text4R, map-get($colorsbbc, PureBlack));
      }
    }
  }
}