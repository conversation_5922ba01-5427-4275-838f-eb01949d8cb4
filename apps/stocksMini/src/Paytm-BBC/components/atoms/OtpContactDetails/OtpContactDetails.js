import { isString } from 'lodash';
import React from 'react';

import {
  SUB_HEADING,
  OTP_2FA,
} from '@src/Paytm-BBC/config/twofaVerificationConfig';

import styles from './OtpContactDetails.scss';

function OtpContactDetails({ contactDetails }) {
  const { mobile, email } = contactDetails;
  const renderSingleContactdetails = () =>
    (mobile?.length === 1 && email?.length === 1) ||
    (isString(mobile) && isString(email));

  return (
    <>
      {renderSingleContactdetails() ? (
        <p className={styles.text}>
          {OTP_2FA.SEND_TEXT}
          <span>{mobile[0]}</span> {OTP_2FA.AND} <span>{email[0]}</span>
        </p>
      ) : (
        mobile &&
        email && (
          <div className={styles.container}>
            <p className={styles.subHeading}>{SUB_HEADING.TEXT}</p>

            <div className={styles.details}>
              <div className={styles.phones}>
                {mobile.map((number, index) => (
                  <span className={styles.textArray} key={`phone-${index}`}>
                    {number}
                  </span>
                ))}
              </div>
              <div className={styles.emails}>
                {email.map((address, index) => (
                  <span className={styles.textArray} key={`email-${index}`}>
                    {address}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )
      )}
    </>
  );
}

export default OtpContactDetails;
