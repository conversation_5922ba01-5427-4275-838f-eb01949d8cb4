// @import '~commonStyles/commoncss';

.loaderBox {
    display: flex;
    background: transparent;
    z-index: 21;
    top: 51px;


    left: 0;

    .loaderInner {
        margin: auto;
        display: flex;
        position: relative;

    }
}

@-webkit-keyframes scale {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }

    45% {
        -webkit-transform: scale(0.35);
        transform: scale(0.35);
        opacity: 0.7;
    }

    80% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }

    45% {
        -webkit-transform: scale(0.35);
        transform: scale(0.35);
        opacity: 0.7;
    }

    80% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

.ballPulse>div:nth-child(1) {
    background-color: #012b72;
    -webkit-animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s -0.24s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div:nth-child(2) {
    background-color: #012b72;
    -webkit-animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s -0.12s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div:nth-child(3) {
    background-color: #012b72;
    -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div:nth-child(4) {
    background-color: #00b9f5;
    -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div:nth-child(5) {
    background-color: #00b9f5;
    -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div:nth-child(6) {
    background-color: #00b9f5;
    -webkit-animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    animation: scale 0.75s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
    margin: 2px;
}

.ballPulse>div {
    width: 12px;
    height: 12px;
    border-radius: 100%;
}