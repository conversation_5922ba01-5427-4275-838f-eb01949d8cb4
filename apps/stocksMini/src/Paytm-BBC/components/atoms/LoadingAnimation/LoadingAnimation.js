import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import s from './LoadingAnimation.scss';

const LoadingAnimation = ({ customLoaderbox }) => (
  <div className={cx(s.loaderBox, customLoaderbox)}>
    <div
      className={cx({
        [s.loaderInner]: true,
        [s.ballPulse]: true,
      })}
    >
      <div />
      <div />
      <div />
      <div />
      <div />
    </div>
  </div>
);

LoadingAnimation.propTypes = {
  customLoaderbox: PropTypes.string,
};

LoadingAnimation.defaultProps = {
  customLoaderbox: '',
};

export default LoadingAnimation;
