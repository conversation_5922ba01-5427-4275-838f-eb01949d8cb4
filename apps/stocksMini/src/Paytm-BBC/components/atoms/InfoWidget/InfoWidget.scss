@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';;
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.infoWidgetContainer{
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 12px 10px;
    line-height: 16px;
    background-color: map-get($colorsbbc, LYellow);
    border-radius: 8px;

    .iconContainer{
      margin-right: 8px;
    }
    .contentContainer{
      display: flex;
      flex-direction: column;
      .headtext {
              @include bbcTypography(subTitle1B, map-get($colorsbbc, PureBlack));
            }
      .subtext {
      display: flex;
      justify-content: space-between;
      margin-top: 2px;
      @include bbcTypography(subTitle1, map-get($colorsbbc, PureBlack));
      }
    }
    .actionContainer{
      display: flex;
      flex-direction: column;
      justify-content:flex-end;
      
      .actionText{
        font-size: 12px;
        line-height: 16px;
        color: map-get($colorsbbc, DYellow);
      }
    }
    .closeIcon{
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }
  