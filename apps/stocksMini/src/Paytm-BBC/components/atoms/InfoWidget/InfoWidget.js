import React from 'react';
import Icon, { ICONS_NAME } from '@src/components/Icon';
import cx from 'classnames';
import PropTypes from 'prop-types';
import styles from './InfoWidget.scss';

function InfoWidget({
  closeHandler,
  actionHandler,
  icon,
  header,
  subtitle,
  actionText,
  customClass,
}) {
  return (
    <div className={cx(styles.infoWidgetContainer, customClass)}>
      <div className={styles.iconContainer}>
        <Icon name={icon} width={13} className={styles.infoWidgetIcon} />
      </div>
      <div className={styles.contentContainer}>
        <div className={styles.headtext}>{header}</div>
        <div className={styles.subtext}>{subtitle}</div>
      </div>
      <div className={styles.actionContainer}>
        <div className={styles.actionText} onClick={actionHandler}>
          {actionText}
        </div>
      </div>
      <Icon
        name={ICONS_NAME.CLOSE}
        width={12}
        className={styles.closeIcon}
        onClick={closeHandler}
      />
    </div>
  );
}

InfoWidget.propTypes = {
  closeHandler: PropTypes.func,
  actionHandler: PropTypes.func,
  icon: PropTypes.string,
  header: PropTypes.string,
  subtitle: PropTypes.string,
  actionText: PropTypes.string,
  customClass: PropTypes.string,
};

InfoWidget.defaultProps = {
  closeHandler: () => {},
  actionHandler: () => {},
  icon: ICONS_NAME.WARNING,
  header: '',
  subtitle: '',
  actionText: '',
  customClass: '',
};

export default InfoWidget;
