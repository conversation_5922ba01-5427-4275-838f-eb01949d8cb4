import React from 'react';
import PropTypes from 'prop-types';

import Icon, { ICONS_NAME } from '../Icon';

const Checkbox = props => {
  const { isChecked, isDisabled, onChangeHandler, customWidth } = props;
  const handleCheckBoxClick = () => {
    if (!isDisabled) {
      onChangeHandler();
    }
  };
  const getViewWithImg = () => {
    if (isChecked) {
      return (
        <div onClick={handleCheckBoxClick}>
          <Icon name={ICONS_NAME.CHECKBOX_CHECKED} width={customWidth} />
          <Icon name={ICONS_NAME.CHECKBOX_ON} width={customWidth} />
        </div>
      );
    }
    return (
      <div onClick={handleCheckBoxClick}>
        <Icon name={ICONS_NAME.CHECKBOX_UNCHECKED} width={customWidth} />
        <Icon name={ICONS_NAME.CHECKBOX_OFF} width={customWidth} />
      </div>
    );
  };
  return getViewWithImg();
};
Checkbox.propTypes = {
  isChecked: PropTypes.bool,
  isDisabled: PropTypes.bool,
  onChangeHandler: PropTypes.func,
  customWidth: PropTypes.string,
};
Checkbox.defaultProps = {
  isChecked: false,
  isDisabled: false,
  onChangeHandler: () => {},
  customWidth: '18px',
};

export default Checkbox;
