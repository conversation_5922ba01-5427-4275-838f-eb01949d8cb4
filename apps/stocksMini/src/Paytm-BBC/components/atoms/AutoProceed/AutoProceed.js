import React, { useEffect, useState } from 'react';
import { AUTO_PROCEED_TEXT } from '@src/Paytm-BBC/config/paymentConfig';
import styles from './AutoProceed.scss';

const AutoProceed = ({
  text = AUTO_PROCEED_TEXT,
  timer = 10,
  callback = () => {},
}) => {
  const [timeLeft, setTimeLeft] = useState(timer);

  useEffect(() => {
    if (timeLeft === 0) {
      callback();
    }
    if (timeLeft > 0) {
      const timerID = setTimeout(() => {
        setTimeLeft(tl => tl - 1);
      }, 1000);
      return () => clearTimeout(timerID);
    }
  }, [timeLeft]);

  return (
    <div className={styles.wrapper}>
      <div className={styles.text}>
        {text} {timeLeft} seconds
      </div>
    </div>
  );
};

export default AutoProceed;
