@import 'src/Paytm-BBC/commonStyles/variablesbbc.scss';
@import 'src/Paytm-BBC/commonStyles/colorsbbc.scss';

.headingContainer {
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  background-color: map-get($colorsbbc, PureWhite);

  .heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @include bbcTypography(body3RB, map-get($colorsbbc, PureBlack));

    .title {
      margin-right: 10px;
      > p {
        @include bbcTypography(heading1B4, map-get($colorsbbc, PureBlack));
      }
    }

    .image {
      width: 24px;
      height: 24px;
      object-fit: contain;
      margin-right: 5px;
    }
  }

  .description {
    @include bbcTypography(body1R, map-get($colorsbbc, PureBlack));
    align-items: center;
    padding-top: 4px;
  }
}
