import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import styles from './HorizontalScrollGrid.scss';

const HorizontalScrollGrid = ({ customClass, children }) => (
  <div className={cx(styles.mediaScroller, customClass)}>{children}</div>
);

HorizontalScrollGrid.propTypes = {
  customClass: PropTypes.string,
  children: PropTypes.node.isRequired,
};

HorizontalScrollGrid.defaultProps = {
  customClass: '',
};

export default HorizontalScrollGrid;
