import queryString from 'query-string';
import { Buffer } from 'buffer';

import { setHomeActiveTab } from '@src/actions/genericActions';
import { isV2KycUser } from '@utils/apiUtil';
import { TRANSACTION_TYPES } from '@utils/Equities/enum';
import { CHARTS } from '@src/config/profileConfig';
import { sendErrorToBackend } from '@src/actions/runtime';
import { getUrlSearchParam } from '@src/utils/commonUtils';

import {
  getH5NativeDeepLinkData,
  navigateTo,
  resetH5NativeDeepLinkData,
} from '../services/coreUtil';
import history from '../history';
import { MINI_APP_ROUTES } from '../config/urlConfig';
import { IPO_ROUTES } from '../ipo/config/urlConfig';
import { QUERY_STRING } from '../config/homeDashboardConfig';
import { log, errorLog, isPaytmMoney } from './commonUtils';
import {
  openDeepLink,
  openInBrowser,
  openNewPage,
  openDeepLinkPaytm,
} from './bridgeUtils';
import DeviceInfoProvider from './Providers/DeviceInfoProvider';

const PAYTM_APP_LINK = 'paytmmp://paytmmoney';
const P4B_APP_LINK = 'paytmba://paytmmoney/pm-mini';
const HOST = 'https://www.paytmmoney.com/';
const DEEPLINK_HOST = 'paytmmoney:///';
const PAYTM_APP_LINK_WITH_HOST = `${PAYTM_APP_LINK}?url=${HOST}`;
const PAYTM_APP_LINK_WITH_DEEPLINK_HOST = `${PAYTM_APP_LINK}?url=${DEEPLINK_HOST}`;
const IPO_PAYTM_DEEPLINK_PREFIX =
  'paytmmp://mini-app?aId=8cc7a6ac4e344a17a7b3ff069929b557&data=';
const HOST_WITH_SUB_DOMAIN = 'https://stocks-mini.paytmmoney.com/';
// const NEED_HELP_DEEPLINK_PREFIX =
//   'paytmmp://cst_flow?featuretype=open_cst&client=paytmmoney&source=miniapps-';
export const getQueryString = value => queryString.parse(value);

export const deepLinkNeedHelp = () => {
  const helpDeeplink = 'https://www.paytmmoney.com/care';
  openInBrowser(helpDeeplink);
};

/**
 * Use navigateHome to naviagte to home page
 * @param {Object} data if any data you want to pass to  home page
 * @param {Object} method if it is push/replace
 * @param {Object} queryValue you can use this set activeTab for homepage. (Check QUERY_STRING in homeDashboardConfig for posible value)
 */
export const navigateHome = ({
  data = {},
  method = 'replace',
  queryValue,
  activeTab,
  watchlist_id,
  watchlist_index,
  isRevampUserParam,
  card,
  type,
  topTab,
  sortOrder,
  sort,
  ipoTabId,
  category,
} = {}) => {
  let url;
  // eslint-disable-next-line no-underscore-dangle
  const { isRevampUser } = window?._context || {};
  if (isRevampUserParam || isRevampUser) {
    if (isRevampUserParam) {
      if (queryValue) {
        if (ipoTabId) {
          url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?topTab=${queryValue}&ipoTabId=${ipoTabId}`;
        } else {
          url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?topTab=${queryValue}`;
        }
      } else if (watchlist_id && activeTab) {
        url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?activeTab=${activeTab}&mastertab=${watchlist_id}&masterindex=${watchlist_index}`;
      } else if (card) {
        const params = new URLSearchParams();
        params.set('topTab', topTab);
        params.set('card', card);

        if (type) params.set('type', type);
        if (sortOrder) params.set('sortOrder', sortOrder);
        if (sort) params.set('sort', sort);
        url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?${params.toString()}`;
      } else {
        url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?activeTab=${activeTab}`;
      }
    } else if (queryValue) {
      url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?activeTab=${queryValue}`;
    } else if (card) {
      const params = new URLSearchParams();
      params.set('topTab', topTab);
      params.set('card', card);

      if (type) params.set('type', type);
      if (sortOrder) params.set('sortOrder', sortOrder);
      if (sort) params.set('sort', sort);
      if (category) params.set('category', category);
      url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?${params.toString()}`;
    } else {
      url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?activeTab=${QUERY_STRING.HOME}`;
    }
  } else if (queryValue) {
    url = `${MINI_APP_ROUTES.EQUITY}?activeTab=${queryValue}`;
  } else if (card) {
    const params = new URLSearchParams();
    params.set('topTab', topTab);
    params.set('card', card);

    if (type) params.set('type', type);
    if (sortOrder) params.set('sortOrder', sortOrder);
    if (sort) params.set('sort', sort);
    url = `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?${params.toString()}`;
  } else {
    url = `${MINI_APP_ROUTES.EQUITY}?activeTab=${QUERY_STRING.HOME}`;
  }
  navigateTo(history, url, data, method);
};

const buildFilteredUrl = (sourceParams, allowedParams, basePath) => {
  const queryParams = new URLSearchParams();

  allowedParams.forEach(param => {
    const value = sourceParams.get(param);
    if (value) queryParams.set(param, value);
  });

  const tempQueryString = queryParams.toString();
  return `${basePath}${tempQueryString ? `?${tempQueryString}` : ''}`;
};

export const navigateCombineDashboardWithTwoFa = (method = 'replace') => {
  sessionStorage.removeItem('kycAppLoaded');
  navigateTo(history, MINI_APP_ROUTES.COMBINED_DASHBOARD, {}, method);
  window.location.reload();
};

export const navigateToPasscode = (method = 'replace') => {
  navigateTo(
    history,
    MINI_APP_ROUTES.PASSCODE,
    {
      againLogin: 'true',
    },
    method,
  );
};

export const navigateToTwoFaOTP = (method = 'replace') => {
  navigateTo(
    history,
    MINI_APP_ROUTES.TWO_FA_OTP,
    {
      againLogin: 'true',
    },
    method,
  );
};

export const navigateCombineDashboard = (method = 'replace', params) => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.COMBINED_DASHBOARD}?${params}`,
    {},
    method,
  );
};

export const navigateFno = path => {
  const isPrelookUrl = path?.includes('prelook');
  if (path) {
    if (sessionStorage.getItem('moduleFlow2FA') || isPrelookUrl) {
      if (isPrelookUrl) {
        sessionStorage.setItem('prelook', true);
        // eslint-disable-next-line no-param-reassign
        path = path.match(/prelook\/(.+)/)?.[1];
      }
      sessionStorage.setItem('fnoFlow', true);
    }

    const params = sessionStorage.getItem('deeplinkParams') || '';
    const isDeeplink = '&isDeeplink=true';
    const query = `?${params}${isDeeplink}`;
    if (params) {
      sessionStorage.removeItem('deeplinkParams');
    }
    navigateTo(history, `${MINI_APP_ROUTES.FNO}/${path}${query}`, {});
  } else {
    // navigate to dashboard by default, if path is not provided
    navigateTo(history, MINI_APP_ROUTES.FNO_DASHBOARD, {});
  }
};

export const navigateMarginPledge = () => {
  if (sessionStorage.getItem('moduleFlow2FA')) {
    // fnoFlow and moduleFlow2FA will trigger exit when user presses back button
    sessionStorage.setItem('fnoFlow', true);
  }
  navigateTo(history, MINI_APP_ROUTES.MARGIN_PLEDGE);
};

export const navigateCharts = ([path, queryParams]) => {
  let isPrelookQuery = '';
  if (path.includes('prelook')) {
    isPrelookQuery = `&isPrelook=true&${CHARTS.SELECTED_CHART}=${CHARTS.TRADING_VIEW}`; // Chart for deeplink is tradingView
  }
  const query = `${queryParams}${isPrelookQuery}`;
  navigateTo(history, `${MINI_APP_ROUTES.CHARTS}?${query}`, {});
};

export const navigatePinStocks = (deeplinkParams, method = 'replace') => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.PINSTOCKS}?${deeplinkParams}`,
    {},
    method,
  );
};

export const navigateFoRiskDisclosure = (method = 'push') => {
  navigateTo(history, MINI_APP_ROUTES.FO_RISK_DISCLOSURE, {}, method);
};

export const navigateOtpConsent = (deeplinkParams, method = 'replace') => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.OTP_CONSENT}?${deeplinkParams}`,
    {},
    method,
  );
};

export const navigateLedgerHistory = (
  method = 'push',
  isRevamp = false,
  deeplinkParams,
) => {
  navigateTo(
    history,
    isRevamp
      ? `${MINI_APP_ROUTES.LEDGER_HISTORY_REVAMP}${
          deeplinkParams ? `?${deeplinkParams}` : ''
        }`
      : MINI_APP_ROUTES.LEDGER_HISTORY,
    {},
    method,
  );
};

export const navigateToKycErrorPage = (errorData, method = 'push') => {
  navigateTo(
    history,
    MINI_APP_ROUTES.ERROR_PAGE,
    {
      error: errorData || new Error('KYC is down'),
      errorKey: 'kyc_fatal_error',
    },
    method,
  );
};

const createKycUrl = url => `${MINI_APP_ROUTES.KYC}${url}`;

export const navigateKyc = async (
  kycvalue,
  method = 'replace',
  deeplinkParams,
  isReplace,
) => {
  // eslint-disable-next-line no-undef
  if (__BUILD__ === 'prod') {
    sendErrorToBackend({
      level: 'info',
      key: 'chunk_load_error_logs',
      data: JSON.stringify({
        type: 'kyc routes fetch start',
        kycvalue,
      }),
    });
  }
  // eslint-disable-next-line import/no-unresolved, import/extensions
  const { KYC_ROUTES } = await import('KYCMINI/RoutesConfig').catch(error => {
    navigateToKycErrorPage(error);
  });
  // eslint-disable-next-line no-undef
  if (__BUILD__ === 'prod') {
    sendErrorToBackend({
      level: 'info',
      key: 'chunk_load_error_logs',
      data: JSON.stringify({
        type: 'kyc routes fetched',
        kycvalue,
      }),
    });
  }
  switch (kycvalue) {
    case 'ir':
      navigateTo(history, MINI_APP_ROUTES.CHECK_KYC, { isIpo: true });
      break;
    case 'account-details':
      navigateTo(history, createKycUrl(KYC_ROUTES.ACCOUNT_MODIFICATION));
      break;
    case 'link-pan-aadhar':
      navigateTo(history, createKycUrl(KYC_ROUTES.LINK_PAN_AADHAR));
      break;
    case 'dormancy-account-details':
      navigateTo(history, createKycUrl(KYC_ROUTES.DORMANCY_ACCOUNT_DETAILS));
      break;
    case 'post-ir-documents':
      navigateTo(history, createKycUrl(KYC_ROUTES.POST_IR_DOCUMENTS));
      break;
    case 'account-modification':
      navigateTo(
        history,
        createKycUrl(KYC_ROUTES.ACCOUNT_MODIFICATION),
        {},
        isReplace ? 'replace' : 'push',
      );
      break;
    case 'additional-documents':
      navigateTo(history, createKycUrl(KYC_ROUTES.ADDITIONAL_DETAILS));
      break;
    case 'additional-details':
      navigateTo(
        history,
        createKycUrl(KYC_ROUTES.ADDITIONAL_DOCUMENTS),
        {},
        isReplace ? 'replace' : 'push',
      );
      break;
    case 'kyc-landing-page':
      navigateTo(
        history,
        createKycUrl(`${KYC_ROUTES.KYC_LANDING_PAGE}?${deeplinkParams}`),
      );
      break;
    case 'nominee-landing-page':
      navigateTo(
        history,
        createKycUrl(`/nominee-landing-page?${deeplinkParams}`),
      );
      break;
    case 'fno-onboarding':
      navigateTo(history, createKycUrl('/fno-onboarding'));
      break;
    case 'view-bank':
      navigateTo(history, createKycUrl(`/v3/view-bank?${deeplinkParams}`));
      break;
    case 'pricing':
      navigateTo(
        history,
        isV2KycUser()
          ? `${MINI_APP_ROUTES.KYC}/bbc${KYC_ROUTES.PRICING}`
          : createKycUrl(`${KYC_ROUTES.PRICING}`),
      );
      break;
    default: {
      const kycRouteExists = Object.values(KYC_ROUTES).findIndex(
        route => route === `/${kycvalue}`,
      );
      if (kycRouteExists !== -1) {
        if (deeplinkParams) {
          navigateTo(history, createKycUrl(`/${kycvalue}?${deeplinkParams}`));
        } else {
          navigateTo(history, createKycUrl(`/${kycvalue}`));
        }
      } else {
        navigateTo(history, MINI_APP_ROUTES.PROFILE, {}, method);
      }
      break;
    }
  }
};

export const navigateIpoHome = method => {
  navigateTo(history, IPO_ROUTES.HOME, {}, method);
};

export const navigateIpoOder = method => {
  navigateTo(history, IPO_ROUTES.ORDERS, {}, method);
};

export const navigateProfile = async ({ value, method = 'replace' }) => {
  // eslint-disable-next-line import/no-unresolved, import/extensions
  const { KYC_ROUTES } = await import('KYCMINI/RoutesConfig').catch(error => {
    navigateToKycErrorPage(error);
  });
  let kycUrl = MINI_APP_ROUTES.KYC;

  switch (value) {
    case 'bank':
      kycUrl = `${kycUrl}${KYC_ROUTES.VIEW_BANK}`;
      break;
    default:
      kycUrl = MINI_APP_ROUTES.PROFILE;
      break;
  }

  navigateTo(history, kycUrl, {}, method);
};

export const navigateOrders = (deeplinkParams, method = 'push') => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.ORDERS}?${deeplinkParams}`,
    {},
    method,
  );
};

export const navigateBrokerageCalculator = (
  deeplinkParams,
  method = 'push',
) => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.BROKERAGE_CALCULATOR}?${deeplinkParams}`,
    {},
    method,
  );
};

export const placeOrderBuyNavigation = (queryStr, method = 'push') => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.PLACE_ORDER_ROUTE_RESOLVER}?${queryStr}&deeplink=true${
      queryStr.includes('transactionType')
        ? ''
        : `&transactionType=${TRANSACTION_TYPES.BUY}`
    }`,
    {},
    method,
  );
};

export const placeOrderSellNavigation = (queryStr, method = 'push') => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.PLACE_ORDER_ROUTE_RESOLVER}?${queryStr}&deeplink=true${
      queryStr.includes('transactionType')
        ? ''
        : `&transactionType=${TRANSACTION_TYPES.SELL}`
    }`,
    {},
    method,
  );
};

export const navigateToSIPSummary = isReplace => {
  navigateTo(
    history,
    `${MINI_APP_ROUTES.PRICE_ALERT_SUMMARY}?activeTab=1`,
    {},
    isReplace ? 'replace' : 'push',
  );
};


export const navigateToMySIP = isReplace => {
  navigateTo(
    history,
    MINI_APP_ROUTES.MY_SIP,
    {},
    isReplace ? 'replace' : 'push',
  );
};


export const companyRevampPageNavigation = (
  id,
  portfolio = false,
  option = 'push',
  stockData,
  ...rest
) => {
  console.log("Inside company revamp url", `${MINI_APP_ROUTES.COMPANY_REVAMP}?id=${id}&${rest[0]?.deeplinkParams}`);
  navigateTo(
    history,
    `${MINI_APP_ROUTES.COMPANY_REVAMP}?id=${id}&${rest[0]?.deeplinkParams}`,
    { pmlId: String(id), portfolio, stockData, rest },
    option,
  );
};

export const companyPageNavigation = (
  id,
  portfolio = false,
  option = 'push',
  stockData,
  ...rest
) => {
  console.log("Inside company");
  // eslint-disable-next-line no-underscore-dangle
  const isRevampUser = window?._context?.isRevampUser;
  if (isRevampUser) {
    navigateTo(
      history,
      `${MINI_APP_ROUTES.COMPANY_REVAMP}?id=${id}&${rest[0]?.deeplinkParams}`,
      { pmlId: String(id), portfolio, stockData, rest },
      option,
    );
    return;
  }
  navigateTo(
    history,
    `${MINI_APP_ROUTES.COMPANY}?id=${id}`,
    { pmlId: String(id), portfolio, stockData, rest },
    option,
  );
};

export const navigateUserProfile = (method = 'replace') => {
  navigateTo(history, MINI_APP_ROUTES.PROFILE, {}, method);
};

const setUtmParams = (param, value) => {
  sessionStorage.setItem(param, value);
};

export const getQueryParam = (query, param) => {
  const urlSearchParams = new URLSearchParams(query);
  return param ? urlSearchParams?.get(param) : urlSearchParams;
};

export const handleMiniAppDeepLink = (deeplink, method = 'replace') => {
  const queryParams = deeplink.split('?')[1];
  const pageName = getQueryParam(queryParams, 'pageName');
  const miniAppSource = getQueryParam(queryParams, 'maSrc');
  if (miniAppSource) {
    log(`## handleMiniAppDeepLink ~ miniapp source: ${miniAppSource}`);
    DeviceInfoProvider.setInfo('miniAppSource', miniAppSource);
  }
  const requiredQueryParams = queryParams
    .split('&')
    .slice(2)
    .join('&');
  const relativeUrl = `${pageName}?${requiredQueryParams}`;
  navigateTo(history, `/${relativeUrl}`, {}, method);
};

/**
 *
 * @param {*} deepLinkUrl deeplink string to which you want to nav
 * @param {*} push default false, determines navigation method
 * @param {*} sourceList list of deeplink origin
 *
 * @description: redirect to paticular deeplink
 *
 * Valid deeplinks
 * https://www.paytmmoney.com/stocks/company/**********
 * paytmmp://paytmmoney?url=https://www.paytmmoney.com/stocks/company/**********
 * paytmmp://paytmmoney/stocks/company/**********
 * paytmmp://paytmmoney/stocks/profile/bank
 * paytmmp://paytmmoney?url=paytmmoney:///stocks/company/**********
 * https://stocks-mini.paytmmoney.com/stocks/company/**********
 * paytmmp://paytmmoney/stocks/payment/moduleFlow?paymentTxnId=PI-P-0806221o0qO0vCmyd2Of&amount=65& source=bond&paymentId=EQINSTG202206087466604288
 * paytmmp://paytmmoney/stocks/transfer-money?cashBalance=5000&tradeBalance=5000&collaterals=0
 * paytmba://paytmmoney/pm-mini/
 * paytmmp://paytmmoney/pm-mini/stocks
 * paytmba://paytmmoney/pm-mini/stocks/company/**********
 * paytmmp://paytmmoney/stocks/manage-communication
 */

export const deepLinkNav = deepLinkUrl => {
  console.log('Inside deepLinkNav', deepLinkUrl);
  console.log('isPaytmMoney', isPaytmMoney());
  if (isPaytmMoney()) {
    if (deepLinkUrl.includes('4e64bdc1c28e4655a14cca00efeb47da')) {
      handleMiniAppDeepLink(deepLinkUrl, 'replace');
      return;
    }

    const { pathname, search } = window.location;
    navigateTo(history, `${pathname}${search}`, {}, 'replace');
    return;
  }
  if (
    deepLinkUrl &&
    (deepLinkUrl.startsWith(PAYTM_APP_LINK) ||
      deepLinkUrl.includes('paytmmoney') ||
      deepLinkUrl.startsWith(P4B_APP_LINK))
  ) {
    let trimmedDeepLinkUrl = '';
    if (deepLinkUrl.startsWith(PAYTM_APP_LINK_WITH_HOST)) {
      trimmedDeepLinkUrl = deepLinkUrl.replace(PAYTM_APP_LINK_WITH_HOST, '');
    } else if (deepLinkUrl.startsWith(PAYTM_APP_LINK_WITH_DEEPLINK_HOST)) {
      trimmedDeepLinkUrl = deepLinkUrl.replace(
        PAYTM_APP_LINK_WITH_DEEPLINK_HOST,
        '',
      );
    } else if (deepLinkUrl.startsWith(HOST_WITH_SUB_DOMAIN)) {
      trimmedDeepLinkUrl = deepLinkUrl.replace(HOST_WITH_SUB_DOMAIN, '');
    } else if (true || deepLinkUrl.startsWith(`${PAYTM_APP_LINK}/`)) {
      trimmedDeepLinkUrl = deepLinkUrl.replace(`${PAYTM_APP_LINK}/`, '');
      console.log('$$$ trimmedDeepLinkUrl', trimmedDeepLinkUrl);
    } else if (deepLinkUrl.startsWith(`${P4B_APP_LINK}`)) {
      trimmedDeepLinkUrl = deepLinkUrl.replace(`${P4B_APP_LINK}/`, '');
    } else {
      navigateCombineDashboard();
    }

    const trimmedDeepLinkUrlArrayCheckParam = trimmedDeepLinkUrl.split('?');
    console.log('$$$ trimmedDeepLinkUrlArrayCheckParam', trimmedDeepLinkUrlArrayCheckParam);
    let deeplinkParams;
    if (trimmedDeepLinkUrlArrayCheckParam.length === 2) {
      deeplinkParams = trimmedDeepLinkUrlArrayCheckParam[1];
      sessionStorage.setItem(
        'deeplinkParams',
        trimmedDeepLinkUrlArrayCheckParam[1],
      );

      const querySearchParams = new URLSearchParams(
        trimmedDeepLinkUrlArrayCheckParam[1],
      );

      if (querySearchParams?.has('intent')) {
        sessionStorage.setItem('intent', querySearchParams.get('intent'));
      }

      if (querySearchParams?.has('utm_term')) {
        setUtmParams('utm_term', querySearchParams.get('utm_term'));
      }
      if (querySearchParams?.has('utm_campaign')) {
        setUtmParams('utm_campaign', querySearchParams.get('utm_campaign'));
      }
      if (querySearchParams?.has('utm_content')) {
        setUtmParams('utm_content', querySearchParams.get('utm_content'));
      }
      if (querySearchParams?.has('utm_source')) {
        setUtmParams('utm_source', querySearchParams.get('utm_source'));
      }
      if (querySearchParams?.has('utm_medium')) {
        setUtmParams('utm_medium', querySearchParams.get('utm_medium'));
      }
    }

    // clear storage if isLogin exists in deeplink
    if (deeplinkParams?.includes('isLogin')) {
      localStorage.removeItem('isLogout');
    }

    let isReplace = false;

    const trimmedDeepLinkUrlArray = trimmedDeepLinkUrlArrayCheckParam[0].split(
      '/',
    );

    if (
      trimmedDeepLinkUrlArray[trimmedDeepLinkUrlArray.length - 1] ===
      'moduleFlow'
    ) {
      sessionStorage.setItem('moduleFlow', true);
      isReplace = true;
    }

    if (
      trimmedDeepLinkUrlArray[trimmedDeepLinkUrlArray.length - 1] ===
      'moduleFlow2FA'
    ) {
      const searchParams = new URLSearchParams(
        trimmedDeepLinkUrlArrayCheckParam[1],
      );

      if (searchParams.has('irStatus')) {
        sessionStorage.setItem(
          'moduleFlow2FAirStatus',
          searchParams.get('irStatus'),
        );
      }
      isReplace = true;
      sessionStorage.setItem('moduleFlow2FA', true);
    }

    if (trimmedDeepLinkUrlArray.includes('prelook')) {
      sessionStorage.setItem('prelook', true);
      isReplace = true;
    }

    switch (trimmedDeepLinkUrlArray[0]) {
      case 'stocks':
        switch (trimmedDeepLinkUrlArray[1]) {
          case 'passcode':
            break;
          case 'kyc': {
            // eslint-disable-next-line no-undef
            if (__BUILD__ === 'prod') {
              sendErrorToBackend({
                level: 'info',
                key: 'chunk_load_error_logs',
                data: JSON.stringify({
                  deeplink: deepLinkUrl,
                }),
              });
            }
            navigateKyc(
              trimmedDeepLinkUrlArray[2],
              true,
              trimmedDeepLinkUrlArrayCheckParam[1],
              isReplace,
            );
            break;
          }
          case 'company': {
            const [deeplinkDirectCard, activeTab, tab] = getUrlSearchParam(
              ['card', 'activeTab', 'tab'],
              deepLinkUrl.split('?')[1],
            );
            const isDirectDeepLink = trimmedDeepLinkUrlArray[3] === 'dl';
            if (isDirectDeepLink) {
              sessionStorage.setItem('isDirectDeepLink', isDirectDeepLink);
            }
            companyPageNavigation(
              trimmedDeepLinkUrlArray[2],
              false,
              isDirectDeepLink || isReplace ? 'replace' : 'push',
              { previousPage: 'deeplink' },
              { deeplinkParams },
            );
            break;
          }
          case 'company-revamp': {
            const [deeplinkDirectCard, activeTab, tab] = getUrlSearchParam(
              ['card', 'activeTab', 'tab'],
              deepLinkUrl.split('?')[1],
            );

            const isDirectDeepLink = trimmedDeepLinkUrlArray[3] === 'dl';
            if (isDirectDeepLink) {
              sessionStorage.setItem('isDirectDeepLink', isDirectDeepLink);
            }
            companyRevampPageNavigation(
              trimmedDeepLinkUrlArray[2],
              false,
              isDirectDeepLink || isReplace ? 'replace' : 'push',
              { previousPage: 'deeplink' },
              { deeplinkParams },
            );
            break;
          }
          case 'stocks-sips':
            navigateToSIPSummary(isReplace);
            break;
          case 'my-sip': 
            navigateToMySIP(isReplace);
            break;
          case 'place-order-buy':
            placeOrderBuyNavigation(
              deepLinkUrl.split('?')[1],
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'place-order-sell':
            placeOrderSellNavigation(
              deepLinkUrl.split('?')[1],
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'chatbot':
            navigateTo(
              history,
              MINI_APP_ROUTES.CHAT_BOT,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'manage-communication':
            navigateTo(
              history,
              MINI_APP_ROUTES.MANAGE_COMMUNICATION,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'equity-transaction':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.EQUITY_TRANSACTION_DETAILS}?${deepLinkUrl.split('?')[1]}`,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'chatbot':
            navigateTo(
              history,
              MINI_APP_ROUTES.CHAT_BOT,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'auto-pay':
            navigateTo(
              history,
              MINI_APP_ROUTES.AUTO_PAY,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'bank-transfer':
            navigateTo(
              history,
              MINI_APP_ROUTES.BANK_TRANSFER,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'account-statements':
            navigateTo(
              history,
              MINI_APP_ROUTES.ACCOUNT_STATEMENTS,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'notification-preferences':
            navigateTo(
              history,
              MINI_APP_ROUTES.NOTIFICATION_PREFERENCES,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'manage-passcode':
            navigateTo(
              history,
              MINI_APP_ROUTES.MANAGE_PASSCODE,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'stocks':
            switch (trimmedDeepLinkUrlArray[2]) {
              case 'ipo':
                navigateIpoHome('push');
                break;
              case 'pricing':
                navigateKyc(trimmedDeepLinkUrlArray[2]);
                break;
              default:
                break;
            }
            break;
          case 'ipo':
            switch (trimmedDeepLinkUrlArray[2]) {
              case 'details':
                /**
                 * @TODO update it later ipo details
                 */
                break;
              case 'orders':
                navigateIpoOder();
                break;
              default:
                navigateIpoHome('push');
                break;
            }
            break;
          case 'watchlist':
            navigateHome({
              method: isReplace ? 'replace' : 'push',
              queryValue: QUERY_STRING.FAV,
            });
            break;
          case 'pinstocks':
            navigatePinStocks(
              trimmedDeepLinkUrlArrayCheckParam[1],
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'ledger':
            navigateLedgerHistory(isReplace ? 'replace' : 'push');
            break;
          case 'ledger-history-revamp':
            navigateLedgerHistory(
              isReplace ? 'replace' : 'push',
              true,
              deeplinkParams,
            );
            break;
          case 'otp-consent':
            navigateOtpConsent(trimmedDeepLinkUrlArrayCheckParam[1]);
            break;
          case 'orders':
            navigateOrders(deeplinkParams, isReplace ? 'replace' : 'push');
            break;
          case 'brokerage-calculator':
            navigateBrokerageCalculator(
              deeplinkParams,
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'portfolio':
            setHomeActiveTab(1);
            navigateHome({
              method: isReplace ? 'replace' : 'push',
              queryValue: QUERY_STRING.STOCKS,
            });
            break;
          case 'profile':
            navigateProfile({
              value: trimmedDeepLinkUrlArray[2],
              method: isReplace ? 'replace' : 'push',
            });
            break;
          case 'funds':
            navigateTo(
              history,
              `equity?activeTab=funds`,
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'positions-page':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.EQUITY_DASHBOARD}?activeTab=holdings&portfolioTab=1`,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'positions':
            navigateTo(
              history,
              MINI_APP_ROUTES.POSITIONS,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'home':
            navigateCombineDashboard('replace', deeplinkParams);
            break;
          case 'equity':
            navigateHome({}, isReplace ? 'replace' : 'push');
            break;
          case 'equity-dashboard': {
            const equityDashboardParams = new URLSearchParams(
              trimmedDeepLinkUrlArrayCheckParam[1],
            );

            navigateHome({
              method: isReplace ? 'replace' : 'push',
              queryValue: equityDashboardParams.get('topTab'),
              activeTab: equityDashboardParams.get('activeTab'),
              isRevampUserParam: equityDashboardParams.get('dl'),
              watchlist_id: equityDashboardParams.get('mastertab'),
              watchlist_index: equityDashboardParams.get('masterindex'),
              type: equityDashboardParams.get('type'),
              card: trimmedDeepLinkUrlArray?.[3],
              topTab: trimmedDeepLinkUrlArray?.[2],
              sortOrder: equityDashboardParams.get('sortOrder'),
              sort: equityDashboardParams.get('sort'),
              ipoTabId: equityDashboardParams.get('ipoTabId'),
              category: equityDashboardParams.get('category'),
            });
            break;
          }
          case 'etf-revamp': {
            const equityRevampParams = new URLSearchParams(
              trimmedDeepLinkUrlArrayCheckParam[1],
            );
            const url = buildFilteredUrl(
              equityRevampParams,
              ['type', 'sort', 'sortOrder'],
              'etf-revamp',
            );
            navigateTo(history, url, {}, isReplace ? 'replace' : 'push');
            break;
          }
          // eslint-disable-next-line no-case-declarations
          case 'fno':
            const fnoLinkArray = [...trimmedDeepLinkUrlArray];
            if (sessionStorage.getItem('moduleFlow2FA')) {
              // remove moduleFlow2FA from route
              fnoLinkArray.pop();
            }
            navigateFno(fnoLinkArray.slice(2).join('/'));
            break;
          case 'margin-pledge':
            navigateMarginPledge();
            break;
          case 'etf':
            navigateTo(
              history,
              `equity?activeTab=fav&section=2&${deeplinkParams}`,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'etf-home':
            navigateTo(
              history,
              MINI_APP_ROUTES.ETF_HOME,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'ipo-kyc':
            navigateTo(
              history,
              MINI_APP_ROUTES.CHECK_KYC,
              { isIpo: true },
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'market-movers':
            navigateTo(
              history,
              `equity?activeTab=fav&section=1&${deeplinkParams}`,
              {},
              isReplace ? 'replace' : 'push',
            );
            setHomeActiveTab(2);
            break;
          case 'market-movers-revamp':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.MARKET_MOVERS}${
                deeplinkParams ? `?${deeplinkParams}` : ''
              }`,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'most-bought':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.MOST_BOUGHT}${
                deeplinkParams ? `?${deeplinkParams}` : ''
              }`,
              {},
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'search':
            navigateHome(
              {
                method: 'push',
                queryValue: `${QUERY_STRING.HOME}&search-bar=true`,
              },
              isReplace ? 'replace' : 'push',
            );
            break;
          case 'payment':
            if (trimmedDeepLinkUrlArrayCheckParam[1]) {
              const data = {};
              trimmedDeepLinkUrlArrayCheckParam[1].split('&').forEach(param => {
                const [key, value] = param.split('=');
                data[key] = value;
              });
              navigateTo(
                history,
                MINI_APP_ROUTES.PAYMENT,
                data,
                isReplace ? 'replace' : 'push',
              );
            }
            break;
          case 'add-money':
            if (trimmedDeepLinkUrlArrayCheckParam[1]) {
              const data = {};
              trimmedDeepLinkUrlArrayCheckParam[1].split('&').forEach(param => {
                const [key, value] = param.split('=');
                data[key] = value;
              });
              navigateTo(
                history,
                MINI_APP_ROUTES.ADD_MONEY,
                { ...data, flow_deeplink: true },
                isReplace ? 'replace' : 'push',
              );
            }
            break;
          case 'transfer-money':
            if (trimmedDeepLinkUrlArrayCheckParam[1]) {
              const data = {};
              trimmedDeepLinkUrlArrayCheckParam[1].split('&').forEach(param => {
                const [key, value] = param.split('=');
                data[key] = Number(value);
              });
              navigateTo(
                history,
                MINI_APP_ROUTES.TRANSFER_MONEY,
                data,
                isReplace ? 'replace' : 'push',
              );
            }
            break;
          case 'pwc':
            navigateTo(history, MINI_APP_ROUTES.PWC, {}, 'push');
            break;
          case 'bbc':
            switch (trimmedDeepLinkUrlArray[2]) {
              case 'mf':
                sessionStorage.setItem('skipTnc', true);
                localStorage.setItem(
                  'params',
                  JSON.stringify({
                    flowType: 'mf',
                    productType: 'weekly-sip',
                  }),
                );
                navigateTo(history, `/bbc/weekly-sip/mf`, {}, 'push');
                break;
              case 'combo':
                sessionStorage.setItem('skipTnc', true);
                localStorage.setItem(
                  'params',
                  JSON.stringify({
                    flowType: 'combo',
                    productType: 'weekly-sip',
                  }),
                );
                navigateTo(history, `/bbc/weekly-sip/combo`, {}, 'push');
                break;
              case 'stocks-selection': {
                sessionStorage.setItem('skipTnc', true);
                localStorage.setItem(
                  'params',
                  JSON.stringify({
                    flowType: 'stocks',
                    productType: 'weekly-sip',
                  }),
                );
                navigateTo(
                  history,
                  `/bbc/weekly-sip/stocks/stock-sip-selection?${trimmedDeepLinkUrlArrayCheckParam[1]}`,
                  {},
                  'push',
                );
                break;
              }
              case 'market-updates':
                navigateTo(history, `/bbc/market-updates`, {}, 'push');
                break;
              default:
                sessionStorage.setItem('skipTnc', true);
                localStorage.setItem(
                  'params',
                  JSON.stringify({
                    flowType: 'stocks',
                    productType: 'weekly-sip',
                  }),
                );
                navigateTo(history, '/bbc/weekly-sip/stocks', {}, 'push');
                break;
            }
            break;
          case 'sf':
            navigateTo(history, `/sf?sfId=${trimmedDeepLinkUrlArray[2]}`);
            break;

          case 'charts':
            navigateCharts(trimmedDeepLinkUrlArrayCheckParam);
            break;
          case 'miniapps':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.MINI_APP_DEEPLINK}?${trimmedDeepLinkUrlArrayCheckParam[1]}?${trimmedDeepLinkUrlArrayCheckParam[2]}`,
            );
            break;
          case 'transaction-history':
            navigateTo(
              history,
              `${MINI_APP_ROUTES.ALL_TRANSACTION_HISTORY}${
                trimmedDeepLinkUrlArrayCheckParam?.[1]?.length > 0
                  ? `?${trimmedDeepLinkUrlArrayCheckParam[1]}`
                  : ''
              }`,
            );
            break;
          default:
            navigateCombineDashboard();

            break;
        }
        break;
      case 'blog':
        if (deepLinkUrl.startsWith(HOST)) {
          openNewPage(deepLinkUrl);
        }
        break;
      case 'pm-mini':
        navigateCombineDashboard();
        break;
      default:
        break;
    }
  }
};

export const checkDeepLink = () => {
  const deepLinkUrl = getH5NativeDeepLinkData();
  resetH5NativeDeepLinkData();

  log({ deepLinkUrl });
  return deepLinkUrl;
};

export const navigateToPayment = ({
  paymentTxnId,
  amount,
  source,
  companyId = null,
}) => {
  navigateTo(history, MINI_APP_ROUTES.PAYMENT, {
    paymentTxnId,
    amount,
    source,
    companyId,
  });
};

const ipoDeepLinkData = (path = '', params = '') => {
  const data = {
    params: `?stsBarHt=true${params}`,
    path,
    sparams: {
      showTitleBar: false,
      canPullDown: false,
    },
  };

  return Buffer.from(JSON.stringify(data), 'utf8').toString('base64');
};

export const ipoDeepLinkGen = {
  home: () => `${IPO_PAYTM_DEEPLINK_PREFIX}${ipoDeepLinkData()}`,
  order: () => `${IPO_PAYTM_DEEPLINK_PREFIX}${ipoDeepLinkData('ipo-order')}`,
  details: id =>
    `${IPO_PAYTM_DEEPLINK_PREFIX}${ipoDeepLinkData(
      'ipo-details',
      `&ipoId=${id}`,
    )}`,
  application: id =>
    `${IPO_PAYTM_DEEPLINK_PREFIX}${ipoDeepLinkData(
      'ipo-order-details',
      `&pageName=ipo-order-details&application_id=${id}`,
    )}`,
};

export const updateUrlSearchParams = (queries = []) => {
  if ('URLSearchParams' in window) {
    const searchParams = new URLSearchParams(window.location.search);
    queries.forEach(query => {
      if (query.value !== undefined) searchParams.set(query.key, query.value);
      else searchParams.delete(query.key);
    });

    searchParams.delete('card');
    searchParams.delete('tab');
    window.history.replaceState(null, null, `?${searchParams.toString()}`);
  }
};

export const removeQueryParams = queryParams => {
  const urlObj = new URL(window.location.href);
  for (const param of queryParams) {
    urlObj.searchParams.delete(param);
  }

  history.replace({}, '', urlObj.toString());
};

export const deepTypeHelper = (type, url) => {
  log('deepTypeHelper', type, url);
  switch (type) {
    case 'paytmmoney':
      deepLinkNav(url, true);
      break;
    case 'embed':
      openDeepLink(url);
      break;
    case 'external':
      openInBrowser(url);
      break;
    default:
      errorLog('Invalid banner type', type);
      break;
  }
};

export const combineDashboardDeepTypeHelper = (type, url, data = {}) => {
  log('deepTypeHelper', type, url);
  switch (type) {
    case 'paytmmoney':
      navigateTo(history, url, data);
      break;
    case 'embed':
      openDeepLink(url);
      break;
    case 'help': {
      deepLinkNeedHelp();
      break;
    }
    case 'external':
      openInBrowser(url);
      break;
    default:
      errorLog('Invalid banner type', type);
      break;
  }
};

export const overviewDeeplinkHelper = (type, url) => {
  log('deepTypeHelper', type, url);
  switch (type) {
    case 'embed':
      openDeepLink(url);
      break;
    case 'external':
      openInBrowser(url);
      break;
    default:
      errorLog('Invalid banner type', type);
      break;
  }
};

export const revampDeeplinkHelper = (deepLinkUrl = '', method = 'push') => {
  if (!deepLinkUrl) {
    errorLog('No deeplink url found');
    return;
  }
  if (
    deepLinkUrl.startsWith(HOST) ||
    deepLinkUrl.startsWith('https://paytmmoney')
  ) {
    openInBrowser(deepLinkUrl);
  } else if (deepLinkUrl.startsWith('paytmmp://mini-app')) {
    openDeepLinkPaytm(deepLinkUrl);
  } else {
    const trimmedDeepLinkUrl = deepLinkUrl.replace(
      'paytmmp://paytmmoney/stocks',
      '',
    );
    navigateTo(history, trimmedDeepLinkUrl, {}, method);
  }
};

export const callPaymentFlow = ({
  amount = '',
  path = 'add-funds',
  queryParams = {},
} = {}) => {
  let params = `?stsBarHt=true&origin=PAYTM`;
  if (amount) {
    params = `${params}&amount=${amount}`;
  }
  const queryParamsStr = queryString.stringify(queryParams);
  if (queryParamsStr) {
    params = `${params}&${queryParamsStr}`;
  }
  const deepLinkData = {
    params,
    path: `/mini-app/payments/${path}`,
    sparams: {
      showTitleBar: false,
      canPullDown: false,
      isCancellable: false,
    },
  };
  log('callPaymentFlow > deepLinkData ::', deepLinkData);
  const base64 = btoa(JSON.stringify(deepLinkData));
  revampDeeplinkHelper(
    `paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=${base64}`,
  );
};

export const callPaymentStatusFlow = ({
  transactionId = '',
  prevPage = 'nativeQnr',
  path = 'transaction-status',
  queryParams = {},
} = {}) => {
  let params = `?stsBarHt=true&origin=PAYTM`;
  params = `${params}&transactionId=${transactionId}&prevPage=${prevPage}`;
  const queryParamsStr = queryString.stringify(queryParams);
  if (queryParamsStr) {
    params = `${params}&${queryParamsStr}`;
  }
  const deepLinkData = {
    params,
    path: `/mini-app/payments/${path}`,
    sparams: {
      showTitleBar: false,
      canPullDown: false,
      isCancellable: false,
    },
  };

  const base64 = btoa(JSON.stringify(deepLinkData));
  revampDeeplinkHelper(
    `paytmmp://mini-app?aId=8342ec35254f47dd99f057698ebb2aaf&data=${base64}`,
  );
};

window.deppLinkNavGLobal = deepLinkNav;
