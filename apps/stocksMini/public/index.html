<!DOCTYPE html>
<html lang="en" class="miniapp">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,user-scalable=no"
    />
    <meta
      httpEquiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Paytm Money Mini App</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
      }

      *:not(input) {
        user-select: none;
      }

      *:not(input)::selection {
        background: none;
      }

      html,
      body {
        margin: 0;
        padding: 0;
      }

      .pmLogo {
        width: 157.5px;
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 20px;
      }

      .center {
        /* position: absolute;
    top: 20%; */
        width: 100%;
      }

      .footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
      }

      .footerImg {
        width: 100%;
        height: 346px;
      }

      .redirectText {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #101010;
        text-align: center;
        font-family: Inter;
      }

      .redirectPaytmMoney {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        padding-top: 16px;
        color: rgba(16, 16, 16, 0.7);
        text-align: center;
        font-family: Inter;
      }

      .footerContainer {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        text-align: center;
        color: #1010108a;
      }

      .footerText {
        position: relative;
        z-index: 10;
        top: 110vh;
      }

      .paytmMoney {
        font-weight: 600;
      }

      .image-container {
        position: absolute;
        width: 100%;
        height: 100vh;
        /* Adjust width as needed */
      }

      .logos {
        position: relative;
        top: 110px;
        z-index: 10;
      }

      .bckgrnd {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }

      .lottieImage {
        position: relative;
        top: 4px;
      }

      /* @keyframes move {
    from { 
      transform: translateX(0); 
      -webkit-transform: translateX(0);
    }
    to { 
      transform: translateX(-100%); 
      -webkit-transform: translateX(-100%);
    }
  } */
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.4/lottie.min.js"></script>
  </head>

  <body>
    <script>
      // var urlSearchParams = new URLSearchParams(window.location.search);
      // var isDarkModeParam = urlSearchParams.get('darkMode');
      // var root = document.getElementsByTagName('html')[0];
      // if (isDarkModeParam === 'true') root.setAttribute('class', 'dark');
    </script>
    <script>
      const queryString = window.location.search;
      const urlParams = new URLSearchParams(queryString);

      // Check for the query parameter
      const origin = urlParams.get('origin');
      const dontShowAppShell = urlParams.get('dontShowAppShell');

      if (!(origin === 'PAYTMMONEY' || dontShowAppShell)) {
        const elemDiv = document.createElement('div');
        let dots = '';
        const updateDots = () => {
          dots += '.'; // Add a dot every second
          document.getElementById('redirectTextDots').innerHTML = `${dots}`;

          if (dots.length < 3) {
            // Limit to 3 dots
            setTimeout(updateDots, 1000); // Update every second
          }
        };

        elemDiv.innerHTML =
          '<div class="center">' +
          '<div class="image-container">' +
          '<img src="/AppShell/backGround.png" id="static-image" class="bckgrnd" alt="Loading..."></div>' +
          '</div>' +
          '<div class="logos">' +
          '<img src="/AppShell/paytm_money_logo.png" class="pmLogo" alt="" />' +
          '<div id="stocksText" class="redirectText">Stocks  •  Mutual Funds  •  F&O </div>' +
          '<div id="ipoText" class="redirectText">IPO  •  Bonds </div>' +
          '<div id="redirectText" class="redirectPaytmMoney">Redirecting to Paytm Money <span id="redirectTextDots"></span></div>' +
          '</div>' +
          '<div class="footer">' +
          '<div class="footerContainer"> <div class="footerText">' +
          '<div class="paytmMoney">Paytm Money Limited</div>' +
          '<div>SEBI Reg No. Broking: INZ000240532</div></div><div id="lottie-container" class="lottieImage"></div>' +
          '</div></div>';
        elemDiv.id = 'addShell';
        document.body.appendChild(elemDiv);

        window.requestAnimationFrame(() => {
          if (document.getElementById('redirectTextDots')) {
            updateDots();
          }
        });
      }
      const animation = lottie.loadAnimation({
        container: document.getElementById('lottie-container'),
        renderer: 'svg',
        loop: true,
        autoplay: true,
        path: '/AppShell/Splash.json',
      });

      animation.addEventListener('DOMLoaded', function() {
        var staticImage = document.getElementById('static-image');
        if (staticImage) {
          staticImage.parentNode.removeChild(staticImage);
        }
      });
    </script>
    <div style="display: none;" id="root"></div>
  </body>
</html>
