{"v": "5.12.1", "fr": 30, "ip": 0, "op": 181, "w": 376, "h": 812, "nm": "Main comp", "ddd": 0, "assets": [{"id": "image_0", "w": 1850, "h": 1624, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABzoAAAZYCAYAAADubMTAAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAgAElEQVR4nOzBAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA2bubF0nO+w7gv+rq6dmdWUkrIUVKCFlJAdsQCx1iDIliSZm/QEQB55RLQkD4kJeLY0zIQiDkEnIw5JRLjgJLGBJIcNBKSuLkkhCCDpExkrOKQLLWFivv7rz0dHfl0FPT1bVd3T3T1dNTPZ8PDE+9PU/VVC9z6O/+ngcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM7W1767/8YXv7WfffFb+1m+/dTb3/idVT8XAAAAAADQXK1VPwCw/t7+fuyUty/defaJ1T0RAAAAAADQdIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQAAAAAAAEDjCDoBAAAAAACAxhF0AgAAAAAAAI0j6AQAAAAAAAAaR9AJAAAAAAAANI6gEwAAAAAAAGgcQScAAAAAAADQOIJOAAAAAAAAoHEEnQBL9sv/8RcPrfoZAAAAAABg3bRX/QDAarzxUfeVf/xh/HUdY+3eHdy40f3Oja9ee/l7f3fwl//1n1/648/qGLeprr9z+OKrN197bqfz0s6nnw52Pr+bZjv/9kfJouNu97Ls+vOb/oMKAAAAAACEoBMuttbC2VtEROztxc5z6Us7H7zfj2fjD+KXXv/9eOBKchx+fvutfi33OW+efvObX/rtR69fyUPNiIiDbux88H4/fiVeir29iMuXj3LJWt51VsMYAAAAAACwHupJOYDGeeOj7ivf/SCppaLzJ7fGg8zB0W4rPWpbEYNBRLc7DOoOu1k88kjrxmM/m/5Z3uf6Mxtv1fEsdfnCm9ef/K1Hv/lkRMSrN197LiIiDzP39mIn/91yg/7o9y37+QeTbG9z8aTzaj+Lb/xqx99tAAAAAAAIFZ1woSVLmAS11Rr+RAzDzTzkjIjoHGV0nU4Sd3eznYOb/Z08FP3dv98/HmOzEzeKY97ofufGHz7zcuev3nmtW26/eu3l7017nldvvvZcVd88uMyNBZj3Ij68149BP+IrGy9FRMRBdxRoFn+viIj2xvh+fj5/F7W86/UsjAUAAAAAgFNRGQQX1BsfdV/5pw/rqej89Fa9CdykwHAw4RatdBQ8Vp2PmH5u7NiSgt8ntpJs//LpKjqLgekDByo6AQAAAAAgp6ITLrA0PfvMrBjclSs+c+32/dfkP9OuLVdR5tdOCzAnjVl1ftJ2Vf/jgHYQsbfZStoLvOv0KJTtbco4AQAAAAAgJ+iEC6yuqWtPWgmZX19up10z7V7FY9XbSQwG2X3bVWOeZPyq/vnxq/0s7i0QUmaD4WfV3p2SyAIAAAAAwAWzhIkaAc6fYrBZ3G6CPJDubfqTDQAAAAAAORWdcIGlK8rN8orKcmXltGuKbURM7eQocRQAACAASURBVD+t76R2Wb9fvn07TeJSHe+63qVQAQAAAACg0QSdcIHVN3XtyadlzftM61u+pnjtrP5VfaeNWafiuFf7WRwscJ986loAAAAAAGBE0AkXWLKcjI+S3mZroXedpMN2VRW4AAAAAABwHgk64QJLaqpmbKW1DLPWFnnX2SA76t+stUUBAAAAAGCZBJ1wgakQPBtb/UHsnyIN7g+Gn1E/kkhbEalAGQAAAAAAjgk6gYWdxRS4WTa8T7lddt86nnU3bcVpMuU8iBZIAwAAAADA/Xx9DjRCHkym7WRsf9l9TyMf3xqoAAAAAACwPIJO4NxrpaPEcNA/2TqVi/Q9rfyeeasiEwAAAAAA6mfqWmBhxTDxPN7jLJ6v6p4bMVxrU9YJAAAAAAD18t07wBL100RFJwAAAAAALIGv3wGW4Gg50Ggnw4pOAAAAAACgXqauBRaWpqt+gvMni4joR2St4RqdZ7M6KAAAAAAAXBwqOgGWRAAMAAAAAADLI+gEWIJ8Xc60ZepaAAAAAABYBlPXAgtL/ZeJiYphp6lrAQAAAACgXuIJAAAAAAAAoHEEnQB1aiXjLQAAAAAAsBSmrgUWJ9QbVwo7n340vfne7uDaCp8IAAAAAADWjqATWJics9ogi/jBJ/1rrSteEgAAAAAA1MnUtQA1S1qjtpVEpP7SAgAAAABA7VR0AgtLBHn3KYadAAAAAABA/XwFD1CzVmu8TdPVPQsAAAAAAKwrFZ3Awlr+y8R9Wq2IwWDY9vurfhoAAAAAAFg/4gmAJckD4KcfTW+u9kkAAAAAAGD9qOgEFpYkyaof4VzKsiwiIn7wSf9a64p3BAAAAAAAdRJ0AgtL1IaPyQbDd5JEEtkgotOO6K36oQAAAAAAYM2IJwBqVgx+09QanQAAAAAAsAwqOqEm1985fDEdJNdXdf+b/9d7YbMTN37jmda3v/bun//Du79+/X/P6t4ts7JOlaZnW9H5xkfdV97rJk/89/f7v/bE4+30DG895vGr2du/d23jT1d1fwAAAAAA1pugE2p0kMUL2eBs75m0hkFjt5tFtxs7f/vv/Z0vx9fjy69/PdppEk892X774x/1+s9+Pv3XX+xkH//Lj5P/uf7Mxlu1PoOgs9LRMp21e+Oj7isREa+/M/jNiIi9vdiJiOj1s/ibt3ux0Rl+KI9d6mfpdprcG2Tx0GEWt9Mkrvaz+GwjWer+7TSJuO0fBgAAAAAAy+NbaKjJ9XcOX+z1kzdXdf8P3j+cev6wO0zc8gAsaUVsbw3nWP2ZS3Ecxz1waRRb7j80XzFg92BJaV6DJa3RWp1feCi9+d7u4NqsPu2DLNr7w6S824s46A1j0p/2Itnv5WH28F0XP892OvzIev37P4enH21l6Xa6mr/17Xj7T55tv7iSewMAAAAAsPZUdMIF0bmURLHaNBtE3L07PHD37vA/PRTDuWwQER+OFpfsdJLodJK41I7Y742Ho3mKVgxJcz+91Bpbs3JdpQcRW/3xct48rHz30/4vRMRxaLnbzWKrk8R+b/QZFB2//yn/GSUPrCMmB5wRozC07N4gi+3CfMN170dE3O1lcaXt/9IAAAAAALA8gk64IOaZUje/ZtK1eTXh3aP9PBydrV955sqVVgwGw9Bv9yiUy7dbreT4XETEg+1YWtnox7tZUrx31XPcvpNVhopTTHxPu7vV49Q1/fFGJ4n0ofSfoxcvRAwDyceyLCIdJs/tg0H0NlvHIWUd+3c2k7gciZATAAAAAIClE3QCK5NXMxZDv9F2Nrb/4zOYanue52iSw24W/c/6z6fbwymIt1tJ3BpEbB+dH4aSI4vu9zZbcTlGASgAAAAAACyTb6IB1lRxettccYrZy5FE+2AQe0dh7uUYVWaeZP9ubzwEFnICAAAAAHAWfBsNsKYmrdF5b5DFVn8Q9wbDc8MqzPEA887mKAzdi+w4uCz2ybcj4r5paovnAAAAAABgWQSdAGvqeI3Ogu1WErtpa6yyM5cHnMXg83JhxuBin+1WchyM5tdOug4AAAAAAJZF0Amwxvqf9Z+f99rLpWVQZ01BWzxvuloAAAAAAM6ab6YBAAAAAACAxhF0AqypSWt0AgAAAADAuhB0AqypSWt0AgAAAADAuhB0wppIWsv/ye8TEdFOk7G2rnHOe1vHOyseX8ZnlN8n4mRrdAIAAAAAQJMIOoG5JYW/GL1+Fklr1J50nGwQx/3z8U7aVvVfdNxJbT873TSw5d+1uJ8NTjXkTMXP5KkHDu8u5y4AAAAAALBagk7gxPLqwTywW9Rpqyur+pefM2ktXsm5DHW8u6pxs8Fwjc4f3tm4spy7AAAAAADAagk6gVNbJKibVOl40raqf7FiMv857T3qqMIs919WwFlmjU4AAAAAANaZoBNYiXLwt6w2ot6qzNOGlOX1Os9KvkZnenTfs2oBAAAAAGDZfCUNrMRZBn6nXV9znfQHwxDyrFoAAAAAAFg2X0cDK7PMas52mtTeb90Uw8mTHj9JPwAAAAAAWIb2qh8AqMdXnmrHZwdZdHvD/Vv7oyrG23eyc1nVOC08TJMkelH9zLPOT1pbc1KoWbXeZ5PCzu2tVlw6+mv+2KUkOkfbP7edRLd7cPeDQfpAxP0BZFVoOev4PGMAAAAAAMCyCTphDWSDiIe3knh4c7QW5ecqru32s7jXG+3fujceFh70suyT/Rhb1LLbzeKwt7ygdFKgOCtwnCeQnFXB2evP/zstK+TMBhFXrgyDyv1ejAWWRY9tJ7GRFvavDB+o18ui3U4q21vd+6eSrWNa2mnnjq+p+2UBAAAAAECBoBPWQB7CdftZdNJk+sURsd2O6KRJdPtZPPzIfQne7AGOfHKnuoTv9uEwNN1sJ0lVe2s/Ow73zqrNFfdnbeftg+3INtvJ1PdzdSNiozP5ku12xPbm3K93ql4heM63q9qyciB50rU2rcsJAAAAAMB5IOiENbDZH4acEaO2Sh5wnvT6vC0ev7qVVAarV/tZdNJW0j1qjw4ft91+Fp8rhazFoHbe0PaMLfRAG+2Iw979+/O0EdPPTdJuz/+4k8LPWecmXQsAAAAAAGdF0Alr4CCdfU1uVrBZdX253zxB6axrJh3Pj5WD1VnKgWyxzcetOla89zLD1WLIWdyft511LjcKVM/fuqwAAAAAAFAXdTiwBjaPFkO8vZvN1eZrdBbX6rw3ITArKp+fdf2iFg1ki+2sY1X7k45PG3Nae5amhaAAAAAAALAuBJ2wBg7SYfC40Ummtle3kvvCznz/sJvFvd5wv9zm54vX52NU/RSvnbctPteyg9STKIaVk7bnaev8Ocm9AQAAAABgXQk6YQ1sZaMg8rCbVW7f3s1io5OMHdvoDMPPq1vD48U2IsbO5/2L4ed2e/ze+f6kgHVWe683PlbEeOBaDl8n7VdtL9KWg9t5KmKn7ReD3dMQdgIAAAAAgKAT1sLu0bKSeThYDDOrtvOQs1ypWWyLQWk5nKs6XgxTi9fkAWlVmwes5WcoB6/bRysL58Fheb8YnhaPLyJ/rvJ+sQp2uz0evFbtF5+x/LvW0Za3AQAAAABgXQk6YQ20kvH9YjBXtT1pv8pGZ3SDYp+q45O2J7WTgtZ8zDyonBToTeqXK19frFqdNM48bdX1EdXBa668X6xALR6rS7lCFgAAAAAA1pWgE9bAYMkFfFWB6LxB6UnGrQow5+0XMR7AFs9VHa9qy+OVQ9pJzzlPxWU+ZlXf8tS4xalzi/0mnS9WtC76+QAAAAAAwHkm6IQ1sJVF3D4c/URM357WlrennStfd17UFcxWBZ+TjlWNPStcLW5PClTL56dNQxxRHawCAAAAAMC6ac++BDjvdpOI7YjotCO6vdnB5EnDzqsbo/2q7Unm6ZdvTzt2dWP0TPl22bzXnbWThKuzrq1jGmIAAAAAAFgXgk5YA5v9Ydtd0pqMp63onNYvDzHz4z/ay7LHLyfJtOrTafc8SQA7q98ibfl3LG9P2gcAAAAAAE5O0Alr4CCN6PeyiSV9m+0kOTg6N2n7pG2576met5dlt2O872Y7SX60d/oxTxLAFsPN01S8zlMROy14nSeInbcCtqqNGFb4bk2/DQAAAAAANJY1Ov+fvXvpkWVLz/v+rIjIzLrs6+lzuqkWzSYJUxMNCEg2PLDhJmEY0MciIBiQR57ZX8HQlxBpTzURDEge8YYm3a1z1L3P3ruy8hIRy4OIlbly5YpLXiMq6/8DCu+Ke1RmdZ3O/dS7ArgBrqMzZukFoLHxodUPPJe5tcd8+cf649i5Y+NDasxvnrfnu5S+zzw99Pi+YeuHifRlUX2PaTrMl/Mv//2/ed/9HQMAAAAAAAAAcBg6OoEbsEyv9z/mcweEbeFr1/ZTwk4/VO3jmG7Yrn37XvsYYXdsUVTh47UqAAAAAAAAAACXRtAJ3IBZIf3dZ2uy+n/RSWJ0l0lfF1Zv7qqs610m+zmX6VMlyR/fokMD22MC1777nIv/Pkoy83mpZT55c/9hGz5eqwIAAAAAAAAAcGkEncANWKZSWUirQsoLqyyVvhZWWWr0w6KUJP0gmSw1+pzKlIX0STLZRPpcykjSZ8lkmTRfadMFuMiN8QPT+crq9x6M/cev1vz8zbb+em6Nv94th9UFceEyAexWHVLKvU7+trKsvjbL9ZTFebF9OX7Q9v3LUqM/+tn666+VvqWjEwAAAAAAAABwawg6gRuTpSZaHReOSVIePCtytfesT7sJTD+tqzDtr+elyVKjv/9dFcJt6rKq//i1uZalNM9kksTIhXguWHX160ImSfbXz1cyq6X0Bx/3g9augDUWuFbXqgJcP8h9mParfuDoJEkVRCaJlOfb5VC5CaTNTkjpv4c/FNaE712Tpv2en0v9zRc6OgEAAAAAAAAAt4mgE0CrMETrCt/C8DRUhanHN2T+/e+syQurv15UQeBf/1AaSdvlOohtq/738sOi3AS5eWG1Wlbb5vNq/WoZX99HuRccbzUF0eH2U9zfJ0rfp/+XpF8O1dH5L//9v3l/8jcCAAAAAAAAAEAEQSdwRsnp2dTJCmuVGrOp/rpw/FL5IWBTENtVu87VZzwGbe9nYa2KH4v/UR9SOjoBAAAAAAAAADcnGfoGAJyXC7388Ksp8MTL4943V8P3M3xf/+jt+usVbw8AAAAAAAAAgKsh6ARuUBh6haGYG/uhWNMY5xG+pm3LsdAyfA/999IPsP31q4XV33yZvDnH/QMAAAAAAAAAMDbjmoMReMH+4v9Z/1lZmn831PV/9bd56/bYlLbhtrb9244fm6Gn6j3kte5aDte1vR+xY3/5B/mXX6d3b8/6Dfb0e8Xiy/Kb2f/2p28y+3W1/k24/f/+wfyn//OH/+VvJen//fO/+Ntr3x8AAAAAAAAA4GUbd1oBvCBjCzr7BJN9Q8+24/qEcJcITduu2/W9NV0/L+zOMzj95UO/56Zrxu6/636btnedKy+s/vjbxN5/GObBoklq9PP39f2X0tprYp0Ed/TffIjf4v+31N9Fz22Lv5GkhzT9q3lR/FKSyqL8t/4+ZTb5T//zd+Yvj7x9AAAAAAAAAMDIZUPfAIDL8KcybQrjYiFanwCvKXwLt/etxwSjbffe9H13XT8L8kB/uS3kbDtn1/o+70Xba9V2bpNIs2wc7bdruw03XeC5DmZIXhbSLN2t/1Vqf5FOjYqV1W5NfiFJeaY/eztJleVW+V36Z5KqcWaU5Vb/8fP2In5o6oJSaTcg/Z/+yfT/OPf3DgAAAAAAAAC4DIJO4Ea1BWJdU5+2BXj+fn06Ko8JL9tqU3gZu2bbvfedXrbtem3hZ9M5Dw08Y9cvrNUkM1rn8W1NnZ9D84PNWMgZq3lm9LSWZkGVMdVTpt3+xkh1QPpUj5ep2WyfpdIf3usXy7JazvIqKJWkp1n6Z4+F1ZMx+o+f7f8ubUNRF4i6MJQgFAAAAAAAAADGY5z/Gg68QGObutZ36jS2fadfbVrXx7HT3Pb9nrru65Ape/vcy6HT6Z76/YThp/Mn3yVK3iSt39elJKnRz94apYm0KPa3T8w28PwX7477z9GjrQLKvuv7mqVVzfLteVyX6efn0uaT5O+Vr/9Xgk8AAAAAAAAAGA4dncCN+JPvqjDr+0WVHC1yafFcjdu6APt0Z3ZNkRquO6azs29XZ+x6/nLTvTSFiX06Q7umoW27Xt/XOHaOPlMNh9t+8q76OXiXyb69M+bBSJ+bf2wG5Xd1FrZ5P0lKTbVP+LTRpjDzlJBT8jpLvfO4de/uEzNL9YsvyeRnJ10EAAAAAAAAAHASgk7gRvyzb6qA65/Vy5NMWufb+rvlNkn6cWm18hpAXThaMfq6sNK6PfjsmtL10ACzqwOyK/Rr6nSMrW87f597bNq/7/Xazittw0rXdP/dXaKp99v6/czo0V9+jHZsGkl6Xlh9XsU2vywuCI0Fon746cLQPjU89hDzXJoXxS+POxoAAAAAAAAAcA4EncCNWue79eNsm+j4Y2kbjrZZFVZPubReWU2m2+PD0NS3zK39nHdPkV11n1bjvoHpoc+47BPOxo5LUundg9vP/1b2v63v7pq/1e8et9smqbQuqvrxzijLjPK8o6WxQexYty7LjFaFjd7r2ERmtt2R1vuksWPt7rYwFG2q4fgQqZEe0vSvjjsaAAAAAAAAAHAOBJ3AjVjVic00rcItV2Prpl4bW7jc5uPMSHVI6o4LQ9PAKBO2sNu1q7adIzzfMY4NOZuOdetOOe8lLEtplsRrW+BYFlZFalQWVmtVz/4MTY20yLfTM7t6lxktetaVrc7jQlO3rNhYdHQCAAAAAAAAwNAIOoEb48LNlZcchetWQaoULned+9DjuoQB7CHh6zHCbteu6vjhpr+taf9jHBrCNoWspwSvl7As26tUBZpJHWi6epcZLVSFjKvUbALNMKhcSHvh5qSjNoWekpRm1fUXku4i41VqVJr0j6708gEAAAAAAAAAIkb0z+AAXhsXcLaFs8eesyk8bep2DY+NOUeQ2eXQELYtZL3G/R6qqaOzrN+DsLrgcVPr84Trk7QKOf1169zurA+r229V/6itrDYBq1vnQlHXReqPAQAAAAAAAADDIugEbsBdKT0dEWo9ZtKnudWHB7M5/jHT3jhWpeZxX+fqCI2ds09na5/tlxALY5vqOQwVzKX1QzPvEunHel1bR2dfLoz0x+Fy1/qwStrbV9qGoqX3M7EZGwJPAAAAAAAAABhSMvQNADjdIvhf8npl9ZhVtY0LOT/Nt/v7galb/2lencdtc8tunVvvxp/mNjp2y/45who7n3/tY6vvmFD4nJrC2Fg99eulWBqz99VksS5VJNX2srA7+7ptLox025o6RsvIaxRbBwAAAAAAAAAYH4JO4Abc1V1x65XVemU1mVbh5WRqNmHnZlu5/OIHoH54OZlWodCkXH6RtHMedw6/g9M/tx+suv3DsVt+yrfndkGrq/59ufP6Iai7Nz+UDe/Fr267H7S6ztRwfSyM9fdZraprHxOy9hnHnBrKDh3qrntmhqYo975WpY1+2TRRkW+3u31dqOlvm9n4OdzX0pidY92yG/vri6Ta5ioAAAAAAAAAYFgEncANWCS7wV4s9NuElcnsrb/NcaHoemU1z6dv/dDUjaVtCOhv89e7bf4xTWMpHgrGtm+OTWZv28JXV12QGp4vXO8C2g8Pu+GsO4fb/phJ83z61oWqbeGqH8K6sNR/nfzw1l8f63jt2h7riPXrpFx+uS/tIF2KRbEdW9v+dQhTxOe7ja13gaRNk2g1RSmbJpuw1C27sb++yMtNyAoAAAAAAAAAGB7P6AReiVinY9v6cHvfbU3nj43DjtNwuel8/n5Sc/jatM4t+x2rflDr7/Nptb2GW+e6T8PjYx2wYcdq0zFN+4Xr2+omUM2q+3ySkZLZ2+fEvPq/anEhaFjDcWy57zYAAAAAAAAAwHURdAI3Ym6lB7Ot/rpwn7FoC0Xb9u27X9iNGu4T1qbgta2rNdw3vIdYh2YYvIb7hUGstD/FcLi/Ww6nLf7wYPS8KrWYJioLqyQd0Q+AZ3nl/HCWSM8m0b0tD64AAAAAAAAAgHF47U0+wE35tN7WcOwvz22/Gp7Tr3NbfblxrLY55phDtQWiffbv6nY99lpdoW3surEuVWk3nI2FrZ/mVotp9at+yJCztO1fkjSX2ftap4f9ZyrJqv3L+rhZfXhYl6WUFOVR1X0BAAAAAAAAAIZFRydwI1b5bvXHHyZVQBnuE9YHI63q5Wm2G26Gy5trdNTWe/buzZ1/mm2v7+7NX+dva/NgqvN9mHR3u/YZj01XGOq4QHQIadp/369JolnDrR4UdlpJ9f5FmmheP5t0XkoPsgSUAAAAAAAAAHBDCDqBGzDJ27f/5tnaWWY6E6+dADM4Z7jcxoWXbevd2C03ha9u7B/rQsw2LmjdnD9Y33esbPdewtDVD2r9Zb+6btW28PWYMDZ2zEvVFHIeal1YTVKzqROvi3Wt7ous62D0QRdoMQYAAAAAAAAAnBVBJ3AD1plUPNmdZGaWGbPMq3X+2F8Oa9M2SfLHbZa5tZ+0e6zjB659w1fHDza7Qs4+YqFr21jahpnStvrh60rNXbBSd/jaFsbGzrVzfw1dr9+Wyy/S/dvWF2MElvY8YacLNicHTNPrh6N3qVGhKhRtuicXhk5Pv10AAAAAAAAAwAkIOoEz6h/bnZeNNJ/5waY/9pfD2ndbl9ixUr/wtc++/rpDwlJfU+jaFsa2dbXGwtdDumCdMFwNpx3218U6cMM6N9O3QydyVejY/qNzro7OY/jhaOGtd/fkglB//0lRKrHl31zxNgEAAAAAAAAAAYJO4AYYI/3nhcyd97/od9n45t7sE7527esCzqYw9Zj7aQpVuzpdm6p//kOD2DBcjXW+dnW0unv+nMt8mIxjTtvESKWtHp9ZlPv1XB2dlxDrDl2nie52YlEAAAAAAAAAwLURdAI3YvFstUqlspCSVPpUbB9ImKR1TaqvzfrE6C74LfAuk/2cy7ig1B+PwSnBZp9zdo371lPuN3aOz3n1fi5y6S6T5iursqzez7Ksv+rcLUllNj8Hd2Y0v+ibQs40GW/I2WRSlPwXFAAAAAAAAAAGxj/TAjdiE2YG1XEhWL7ebisLq0Vwnk+qAjVXJelzqr0YKkmkLDMqy/0czw9QXTAXVmk3VG2q0jZsbdvW93U6lQsc3X3Exr+eW/MwNdHvPfZ6uaBS2gbVZUuzoHvPknT7frrj/HOMTVPIOfaOTsd/Scs0kejoBAAAAAAAAIBBEXQCN86FXl1hWGxb2/75ugrtwn2rarV43u67aKhhqNpU+267pqbru/FqWQWa4fcs7QaZ+6/dftgZvhcKzuVXf9wWll5Tqmp65US309GZ0NEJAAAAAAAAAINLuncB8JLFArBw3LUtDDzdPrHwzV/f9OVfo2ndMfuE93+O84XH9Fnu+moKn/vc5yHh5Ri7OmMhp1R1dI7wdhuVKf/5BAAAAAAAAICh8S+1AKJiAVxTp2HbOFabzuuv67OPE4aH4bpw37Zrtq0Pj+v7/TZ9/7HvpUuf/cNrjpULO6Wqo3Pkt7sjcTcOAAAAAAAAABgMQSeAqGOmSW0KEWPLbr+mdWWxHw62HXfo9qZrhiFmUwdr3+ll277/S2kKhIdwl0p3ifQmrcazRHrwpnx1jZGPyf7XCG6/ER2dAAAAAAAAADA8njAG4CyapmRte85nLBxsO6ZtWyxk7Nredd4+1+raHhtfOoD073+aSa73ME2lorhufTsxuk+kx0xa1DcyzYxWud3Uu0T6w/vDvsdl0FC5tNIqCJAX3j653V0+Fc/oBAAAAAAAAIDh8c+0AM6m7TmZx6zzx7EQtc/YPzY2dtfomv62K8jtO74G/5qrXJp5103T69bQXSItcitJWtX1mAByFjRUzqSDWkD9oPRLXtXCSuvqlvS1o+u26uh8SZPtAgAAAAAAAMDtIegE8CJ0BaGnjmPLfY495jqXFuteHcq6sEonZrPsh5p3yXm7LA/hB6WzafN+n+ssc1Vsg9BFWXWIAgAAAAAAAACGRdAJADem6XmhYxOGnMuyCiBdDdfHlpvGseWmdW3eudcw8lqWi+Wb/mcCAAAAAAAAAJzbAf/cCwB4CV5CyBkKQ85luf3yl5uO6dov3D9c3zT2a9M6AAAAAAAAAMAwCDqBG/Huwejdg1FGn/arl6TS778z9vffGfthMvTd9BcGkuH6cJ9w33Ac2++Y4/3jnEO6QgEAAAAAAAAAl0EkAtwAa6U/+4P9Fr7fLazW9TMG1yurT+vttu8Xuw8Z/DznoYNjlGXSw3T7jMt3mezbO7NZ8fPH7bbHmVGWbZaNJD0vrP7uOrfa6tBncfrBoh80xkLHWBAaOyYWVobrY9dtCj1fUIYMAAAAAAAAADeJoBO4AcZI63x//ZvMbP5XPnk0+mkuTbJq33+ebY+ZZPvHrwqrp2BdGJY6y9zaz7nM/pbK14VVecNTfb57aPzWJUn/9E18+7eR4757kyjPrbLMbKpTLxtv3Ljv2Nwlh4WdSyvN6m/nc1GNl1ZSubut6Tg/sHTH7+3bcT+x7UxZCwAAAAAAAADjQdAJ3ADboxnTBZlhDcfONDWahk2iM6Ofxk9/toQtFrAO4aePw4SGeW6jNdzeZ1+pej2VjjcAdZbBrbtgMhZqutAzrLHj/ONjx7TVpuu79XR0AgAAAAAAAMCwCDqBG2BMHWj1NE2NVoXtrG3HXUo0YL0i190aC38POf6QekmXfK8OsSilItINmSbb9X5g6QeffogZGzuxdf45ukLN8Dwz07xvU0cpAAAAAAAAAOB6CDqBV8iFmF2167gxOjXYOzbgDI8/tB5iEpl2uKmOraPTBZuuurHUHGK65Vj46Y/D48P1XfXQMR2dAAAAAAAAADAsgk7gRqystC6kSVrVx6xa19fUVPvH3P9RgwAAIABJREFUqjt/OI7tN7Rzh7BtXa+xa7v1sfG5umFj0w6fI0C9Bte96arf0emEXZlNXZpNDt3faeoIbeo0BQAAAAAAAAAMi6ATuBHrYrce+pzLdaRO0u15/LEkqQ5Uw/0cF7jG6iX4IWzb9kMd2/XaND40iPWnEQ6nFI5tiwWtY3CXSE+ltLbSXbrt5FwU0mQkt9k27a3UPjUuAAAAAAAAAOD6kqFvAMBlrIPEb72yesz217eeo4iPm7Y9Zvvr/JDTLXd9+fu1jf3rPOXbrtbY97Gy233CGo7bvty1/Bq+pIeGzG2agtOmbavC7oWq0/V40rmJqUJO/5bSRFrV79vKe/+axkMh5AQAAAAAAACAcaGjE7ghfog5mZrN8qRuZ/QDOH/bMeNY/TS3m2u5dVIdTnrLnd9HQ8DaNQ5r7Jxt+8aO2ztPXcOwc9MJu7L68GAODjsfs+253Litumu3jTdTC4+lZVLbjs6u13pVSNN0W/31h/DP0VSbrtc0VnnYtNAAAAAAAAAAgMsg6ARuRKyDMxyH1R8fGnj6IWfs3P622LLv2LC1aRyrfe0EtAce6+7hmI5O/5gwRG2qfcbTEf2WX9uqo3NRh5VhDbs6m2pbIOkvhx2hTWFneM6m6+xcawQdpgAAAAAAAADw2jF1LXAj5rb6cuNDxcLPtnG4HAaCbfuG+/e9drRbtCGEDWvfr1gg27RvuM1f7hqfuzZNS3zOaXSPsSh3l5tm0V2U1Vdh418+P/gMx03haGzdc74blroaW+dqKqkg5AQAAAAAAACAURhRrw+Aczgl7Aw9mOo8feqPS6sHs722v81f5xzyrNDYMX2C1L7X6ApJm84ZhqyndsQecs8hN21wLHx+I6uvMkrTYUK6wlYh5rJs3scFoosjzp+Z9mV/5t40GMemwm2aHjfsEv1VOft62J0CAAAAAAAAAM6JoBO4Iau8mqrUr9L+OjedaTj+MJE+rb1jI1XaD1Nj4WrbunPpE7yGAWt0eWX31sc0hZB9pg3u2q9p37616XyTqdFXVd+YCzld4HnpKklFWQWKd4n0Y31fs2QbevrjUG63oaU/lqSllWbetrZj2sJT/5z+eGJ2Q9HC7oachZV+P1m+aTk1AAAAAAAAAODCCDqBG+FCzbD6gWdTAOp8Wu+fMzze6QpSD63+9drGvq7Atam7tWn5lCC2LXQdg53w8Uq1SVvI6QeVsRDSbZ+Zw48Jw1K3LTZeRM7rxi4E/VVBRycAAAAAAAAADImgE7hxfjgZLofbTt3n0Np2rrZAtsuhAWt4L/76pv1CYVjqOmPdct/znNv7uvrh4zU7OtPIk6BdwLks98POMIgMxbb3PaZrv7bz5nYbhC4kTcpShqdcAwAAAAAAAMCgCDqBG7HMbWs/4iwzxt/HLfvVnadpnX+tpm2HaAsv+4StXcceGrg2Xe/Q60v74eyxHbDu+seGpOuVVfqwvz5Nr1OLoGOz77S1Y7dOEk01wANPAQAAAAAAAAAbBJ3ADTCJ9DmXeZepMewMg1C3HNaudbHlrpC1jR/AhuFrbF1T4BouHxLGnhLWNunTtXpIEHtM2LrMrX24P+/3dapYyDl7IZ2RfoenKUrphdw3AAAAAAAAANwqgk7gjIacyvLz3OqzZCQpSaovJ0mMkrrDbhqJvdoC0kvrG7DGtnUt9w1jw27XcH1X8HpoR+wx/Pv7nGvnXAs/EF1tv40kkXl7Zwb9RZ8mUuoth12cL7Wr06aJjKWjEwAAAAAAAACGRNAJ3KCyrL62tuHXIrK/C0idLPKbIUm2uyRechULTn0uRO3qOPV9kTFvdXyX6KGaQtBDgld/n+8XVu+yTbWfcxn3gMdFLt1lu7Us9y+f73dwHhyUlgMHiFlq9GFi9JBJHyZGann7/+uH9sBz2ZApPgfHrK20uML3TUcnAAAAAAAAAAyPoBPAnkjIpqaQKhacSlVHaVlqp8v0cxkP68IOVMnqSTJluT2P2+6PLyFJzF7wuB8cd3PfdxgiS9JqtVsv5ZKv0yXEprDdrDvyv1bLcjdAXVpJ9bIfkn45sDmTjk4AAAAAAAAAGB5BJ4Cz8ANJPxQMl2PrmoLEJKlCV1f9dU3X7zOO3dPWYLP4nt3QHZ0xi1K6S/ar46aydfUc09r6AepM2syl+87b52fe2F3TdZF+rmvu/WjQ0QkAAAAAAAAAwyPoBHAWLlSL1UNC0KZzun3Djs4+5/e7Qf1668bQ0Rn2PLpQM6zOJmRseI/CIPTc1V3Dr+8m+/e4LBJ9WtPRCQAAAAAAAABDIugEcHFNYeQhgWPYARrb1lVfk64Q+VomRSm/9bGro7NLGISeu/YxS6R3mfSmWL7pfxQAAAAAAAAA4NxG0O8D4DV4zaHjEC79LNO+1unuTZwSco7JOabUBQAAAAAAAACc5oX+EzMAoM0YOjrvylLrYvtgy0UZ7+iULt+peUznJgAAAAAAAABg3Ag6AeAGuY7OBzPcPSy8ltJ775mcbWGn/6xM3zHP3Gw73l2vT40hMAUAAAAAAACA4fGMTgC4Ma6bsyyluR3uF/1duX0+Z1oHrl3P6PTDyFjg2BZKxsLMMDjtCjub9vOFISoAAAAAAAAAYBj8cy0A3Bg3Ze3Qz+hcRG6graPTaeqW9APG2LgtGJ0lzeuatvnnie0DAAAAAAAAABgWHZ3Ajfj9d8ZK0g9rmdXSdu2OG/fzN9XPw4ORWQ19Mx4Xaoa1j8+FNDP742UpLe12OSYWYIY1tq3teAAAAAAAAADAsAg6gRvxL36e7sU8T0urVZ15fl5Lq/U2AP2Hr7th6NcF4egYTWdG3060eXNmmTEfJtvtH7yHcL5/TGTSzaKRpPmT1T8mwzyo03iXTb31TVPX+mFlLLj0l8P9Yse5sV/VEFK2bdvZj5ATAAAAAAAAAEaDoBO4YY8zo2kuTTLpTSZN3hqt6+U/+ab9WD8kdebPVvNIHrrKpe9bgtL5ym6mU71Fb+6M3mVqfAHe3plo0vjzx/3V7x+j8832Tipt0XfP4cS6OnfCSE9Tc3IYYPrVHRcLSt352o5vO59fJwIAAAAAAAAADImgE7gR67x7W1jbTFOjabDuzdt43jY542+Sp6XVU4/7m6Td+5zi493BXZCdB2SZUZ7bTY0Jt4fH+LXt+JcmFkKG2/a6Mz3h+q5x35AzDEzduG2aXAAAAAAAAADAdRB0AjhZn+C0r2lqND1jiDnJtOli9au/rWl8zgBX0ia8bAo5w/1ix/Q5h79tVYyj9XB/YuVdfnDpxn71w0p/ORw3na9t/9j5YzUcj+BlBQAAAAAAAIBXjaATuBGr4vrP2JzW6dWqsJqmprEOqa2bNTb2A89zBbj+OcN1fph6aLjaZ/+hX/+Yu9xqUXedbsbh1MbBDL5tU9o2TW97yP6xDtJw3NRNCgAAAAAAAAAYBkEncCPC52k2mZ4x9/LDVTduqkPoE/KFYezT8vzhbCwwbQpTDw1Xu/YfsqPT2u1zOAsrFaWUJtJTYuSadhdN0+wO8UzXpLru0oWsblxqE7xuQs4bfuYsAAAAAAAAALwUBJ3AK9M3EB3KpYLYQ/Y7NJx13av+uE911+paN4bO2FMsrVRIWltpXVTrXHV+GwS2bY8ZnUS2hS9PGlnXqQxquM7vMk1UfVMAAAAAAAAAgMEQdAI3IgyOzmmSVuc/pB5rqCB2aqprx2qXQzpbY52ufdYdEr76wevQijooPPRHou0xph2POI3yg1M/KPXD0M5w1AtAx/4HAwAAAAAAAADwGhB0Aq/IemU1OaJlMuzC61uvpW/w2hTCrgtJ6X6VpKe8+/i+2sLUQ8PVNmHIOSlLrZOkYe9xaOvgPLe1jY9jXCiamt0gdGqkX5Wzrxe5QQAAAAAAAABALwSdwI14LqUst8ozE61+wLkO2tEmU7NZFxsfU911wnHX9mMcErw2hbB9jzkmxHWB69qdw6uTtApTY8tdHrNq31h1b/HUaBQhZxp0VK7tto6Zuz//PmNT5wIAAAAAAAAArm/4f/0GcBZZPZ9nU3Xh5XpVhZ+SNtVfFws8D6nuOH9dLAxtG4fHxsbnrH3Hx2oLR8NtbtnvHG0au0A0VtfF9Ttr+xp7uNllbV/+9wAAAAAAAAAAt4CgE7gRcxv/ctv8Goag4VjaD/jyYG7RMCwNg9Km9WGI2HRcuM3v/DxH9e+hKXSN7dfny9//0Hty4/nzdp0br1e2CjB7hrLrlR31syRfcliYli/45gEAAAAAAADgRjB1LXDjmsLOg62tVrHlsDZtj3gw24B1brfL/tj5cWn1UGetbjpep2m5qUrxUDAch7Xv1L1dXbFt3bKHXDe8RjiWTn/eJ+KKxEjl0HcBAAAAAAAAAK8bQSdwI1bBcx2nWbXukOqfq235XMLQ1V+OBbKbdU2hari8tsqMtAqqf+7pxGi1rkLUud1f9qsLW7vq5jYa2imb1vtBbCyU7TPlrjvOre/7vM9LMKZ6XujMSEqlu7R53++m2/HSSquGaXeLkUwbm5aWnBMAAAAAAAAABkbQCdwQF1pKh1X/OMdfDrcfE6J2hauX0tXRulrbnfXh8rE1xoWo/tgPVl0Q68LKMGj163QS71YN1z2k0nKgrs5ZIj3IapZUN3BXL89l9uq78Gdhcti1lmX78jyXXHZ6jrCUjk4AAAAAAAAAGB5BJ3BDwrDyXMc1haDnqkO4RsgaWnnpmhtHg1WvQ7UpSF21TR3srVsnw85du04TLcsq9JSkuUy0nmqWtC/vBak1PxBdllU3qUptnm26aAgz6egEAAAAAAAAgOERdAI3Ypnbs07oOcvMzT3dMdbxOkbnCGFXefUz8X42rvexqaNzKH4gGoajvmW5DUJXhVQYs20RBQAAAAAAAAAMgqATuBH/+NUaSUoSqSylLNuGR0kqTb0syXjb3sraWWaMC0rd+NzB6bkdE8SOPdxsC2HD9+Nzvk0HF97+eb7dLUlk3t5Z3b09880eYF1YyQsymzo6w6lm+5ol2nSMHlr7cvtvjqmn1X2eL98cd9cAAAAAAAAAgHMg6ARuTFkHRn7gpVzbaU0lSdttXyQjWSVJlTiVpVUWCRHvWn5bmMzI5navvsvUOyyNha1t47EHsTF+OOlbRILNnfevYlyI7SR18FaW24DbV5bVMz1fyhSrx4aW0m4N17cd19eh4SgAAAAAAAAA4PIIOoEzGtckof35IVmSREM2fW3phkwSW4dtu3We9JuTNEmM6vC13r99nKRSeeK0of45+ozz3G6CxSZh0HhusSCzaZt7T+dWurvsbfXSJySMhZZ9ahhghuv7HtdVj+06BQAAAAAAAABcBkEngA0XjsW6A9u4fZtq7Br+sgsR3bX980a7FvPd9UfJDxyfcq0BuNfrwUh2BAH85ILdkGGAGa7ve9yhgSoAAAAAAAAAYFhMxAe8cmG4eWjI2XVuV8NuyHDZv7b7aroXfz/E+R2dOI+mQBUAAAAAAAAAMAyCTuCVu2RYeEhw6getXVPEopvf0TkGd+nlr0EACQAAAAAAAACvC3ECAEnt082eet6mrtGm2jUVLp2c3V5aR6d7Bmb45W9vqk1T1rY9n7OrAgAAAAAAAADGj2d0Ari4Q0JOnMfOMzqHvpmewmdghs/C7PvszLCeK+z0zwsAAAAAAAAAGB5BJ3AjXsJ0r/4zO9sqzmdupfuhb6KnU4JMPxQ99Pi+NbzO5DovCwAAAAAAAACgAbECANywsTyj8xCuY9LVPp2Xfqjp1z7H961hhykAAAAAAAAAYFgEnQBww17KMzp9pzwv85LP2gzDVAAAAAAAAADAsPjnWuBGJMkLbN3DxY2ho3P5AsPWNnR1AgAAAAAAAMA48IxO4Eb8qz9ON+PfLbbJ0g9eS9+Xhd0sfM5l5qsbS6BuXJIY/fROmzft7Z0x00mVZN4n0sdZNZ5k0vsHYyXpx7k1vxriZrUbCNa3qUUp3SX79aTrWGlm9uslLS3P6AQAAAAAAACAoRF0Ajfo452JjiU1xj/rXPqa7waf82e7N/XpKpeWuY0mpL+e2xH0D47Hw9ToXaa912qWGTON/Pb99sFo6r2C09To/m7vJe31Gj+tqv3WuaS0Y+cLmdVBpurAc9FS53nzedKm7zipAk0XaoY1pikUPTQsvXSQCgAAAAAAAADoRtAJ3Ih1XnXyhbWvSSZ9zHbTm4/7IZtzVMzjd5r2sS6kH6807+l0YvTuwBa9N5nZvMZZdt7X6hYsy20npyQVLVO+tjYXN20rpedgVRiKhhlvaqSlu7+6k9T9iDVVxw83lwUdnQAAAAAAAAAwNIJO4Ias83gdg0m2DQabQtlYSPvTx5eRE+b59aYBzjKjPLe96pBmAzwFugjehiLcwd/uBa8uIE2DZUk7T7PedHomsZMDAAAAAAAAAK6JoBPAVTSFsF31Wg7thj20Y/acXKjat45JmlSdnY+l1VNiNstDcwHpJrsMAtHUSCtVQejyZWTvAAAAAAAAAHDzCDqBG7EqrFZWO894vKZp44MUX4ZjgtZzhrFdna19xuG9DRXEhvwuSz/klKS7fDses50gtB5/Kmdfh7ofAAAAAAAAAABBJ3Az3DMOW591eIBDA1M/aO1br+WaIeyqsEddr09na59xeM5VOJfrCPjB5ksIOWOm3rM+AQAAAAAAAADDIOgEbsS6kCZp9359HRuYhoFrVz3GJUNYd2+nBLFd4eI0NZtA9ND6Ut3XP5tpsu3i9McvDY/nBAAAAAAAAIDhEXQCN2Q9cPpyzqC1zaVD2GOv0bdb1QWhx9aXrCilRWb0mFs9yby4kNO9x1f6UQcAAAAAAAAAtCDoBHA2Qwetba4Rwh7brerCs6Zx3w7UoaYGbvNdZvWQmc14Yyo9aLv8s1nzDS/LhvXBz9vK7j4P9NxSU3VyugoAAAAAAAAAGBZBJ3Aj1iurPDPK8ng9hTvHfVJdZzI1ei6l++RMN38FYwlhJ+nuvUxS6SnfHW/2qes62C9cL22PcyGpWx5L4NllVv8sTRJpXW6rv21Pj/+C+SHpzth7D44JSOnoBAAAAAAAAIDhEXQCN8KFmbHaFH62haJZvg003bbnzEhZ3TKYmc5w9RKha5OXErqGgau/7MZhjY1dGNq0j9s+fWG/5V24uY50cYYhaFeVqpDULfuB6WQav4YLQze1fk3DLt2R5OYAAAAAAAAA8Kq9sH8CB9BktW5uSVtJ0truVU1MdVxQH0wVbs6X1Tkf6mwyy7fX8MdtIWusHhu8urHrLPX3eW6Y3nQMLhHCdnWojqWDVdrtnlyniSZFuVf7CEPQrhoe17VO2oahm1A0+K+kH4R+Uy7f9LpxAAAAAAAAAMBFEHQCr9C0DjRdOBrWuVUVhNbmbdN6tgSsTcKgNTPaBK1ZbjW30rS+7lTarHuo66r+HvxANOaYbtNjw9dYN6wLY2Odr/79tY37fB/+tcLlx4PfnctyoWZYXwo/CH1eDXsvAAAAAAAAAPDaEXQCr1Bb9+c17QSr9fIq2OavyzOzCVZXYYdqTKSL9RzVBa5hB2zYDTuNBLfuWD+olZpDU3+5MXRd7e7/XEqqt68m43pI5ykdnWMySaTnoW8CAAAAAAAAAF45gk7gjEwyXKi0yge79I5pVt2LX89lDAHtdGI0b+mA9cex4NaFtX5QK6lxLKlxiuHYlMMuVHXrx+ald3Q6TVPfAgAAAAAAAACuh6ATuAG2vH4A6ILMcNxUx+rQIPbUsPWY45umGI5OORysXw8Yvsc0dXQuLxgczpLqmZrHVAAAAAAAAADAeBF0AjfAJEZ/97lKijKvi2/qZVzvMtkvMuatrJWkWWbMbyV9I6mrOv7yl4W1s8yYcDwWTUGsP5aGCWJjXa9NNeTud5lXr/kyt/aLqtfees/1XORSWVr96U/GkdY9F1VdF1aTSJVOCyTbqgtRj60AAAAAAAAAgHEi6ARuTO51DPoZ3nwlI1k9SSZJjMqySnG+SEoSo6d6v+e0qt/XseX33jneZbI/yAWaxnypL/BWVQj6mFv7lBnzmFdhqhv3qX0C17YgNgxlf52PK5Rd1q9JNd7WLzJm80blu2GlM1/tr0sSY+r30EjDT+nb10N9r2GVTg8kY/VS4emMZ3QCAAAAAAAAwOAIOoFXqAymuvWX6/xTsUZHF5ZWQem2Pteh25NXq3NV4yS1epZMubB6TqtaZDILb9kFrUX9W8kFrH1rOA6WjbYB4ma8ljbdhEVqlBZ2U9eSbM/8sCy083ok9XSxwbqdcHW77bC2Qf/cL0Xh3epcRg+ye/VSLhGe+hUAAAAAAAAAMByCTgAHcyFbV3XjstQm8JOqOl+57bvnduuvZb0Z2aAeJgx/Y+ti2w51yDFJYjS30v3BVwEAAAAAAAAAYPzG8fA2ADcvFoLeklhHZ59t4fK57+lhVE9OVbSbc66R3SQAAAAAAAAA4EUg6ASAE3WFlU2dncd2dZ7jnq5lLqPCG7tQ06+XnLoWAAAAAAAAAHC7mLoWuBFjCbZeK/f6H1svdU9jmbp2XWeZbR2dy1KaJeOtAAAAAAAAAIBx4Z9uAeCGjW3q2lhHp1SFiW1V2oaNQ9S2+wIAAAAAAAAADIOOTgC4YWPo6Cy8mWkfCqt5avaqtBsohsYQLBJyAgAAAAAAAMC4EHQCwA17MBr0CZiLUioLK8loUUoLY6S63q2sfptVy/NcmgfHpi3dqLNUWha79RL88DWczhYAAAAAAAAAMCyCTgC4gLK0O8/fDJevZQwdnb673EpTI63qWtbrtP/aFC0J7TyP1yZdoWkTP9Ak5AQAAAAAAACAcSHoBIALCEPNIUJOafiOzk2wKenOPRV6ZTf1bnqd1yUWmqamWh8LScNg1IWhhJwAAAAAAAAAMB4EncCN+Fd/nOppWaU5T7n0Yz1e5dIyt1aSvsiY58WQsRfO5S6T3mXbDPMnb7dJ6s/vq+Gk/g3/qyvfW8yikB5kq+lpXbi5stvOzgG0dYyG2+b5fvj5m3L29fx3BQAAAAAAAADoi6ATuCGPM1NX6efvE+W5VZYZ5bmNJknrXPqabxOddbENSB0/KHUITI+XpNK3kyqgfMqMeaxfWz+olKT7RPo4233bHh733sbNiodUdl7IuOrWr5cDv09To8L/1qbVszrvEu0EnG1Ty47Ro7X6NPRNAAAAAAAAAMArR9AJ3Ki8DjDzvDnommTSx2w3YfrpfpgmxR6g2CAMT2N+mB8fvsWC12O8vTNHR2vvZ0aPkd+e69RoUlitU6Nv71pPbxrGrWzRvO2pDjefiv7nu7YyTaTC7j+nc2Wl+9HedtTT8T8+AAAAAAAAAIAzIegEbsQ68pzBY0xO/K0QC09DH9tDwD5OPkHd6Xp0bT5xdWuxffxzuH0y77Vqut4p1rmk9KRTnCwtrSSje1tqIbPt5AzrC0JHJwAAAAAAAAAMj6ATeAXKiVGytpvqrwvH5wpMDzHJqus2VX+/cwk7Xg+t57hm7HznuF5nGHtFHxOrSZZIkqaJ0UfF7+vnLR2d6zK+fhlZH65btnTBnoKOTgAAAAAAAAAYHkEncCNWRUuwVW+b1vtN022wKWlnHPMk6fEM99jEhZmxOnQIe6hzhrHHGkvI2ebZJLq35aa2mST9179pOU8YmLpQ1A9H+wajdHQCAAAAAAAAwPBG8E/yAC7Jf26kvCptH5HYZSJpFZyrq0qKjh9VBaex46ap2YSqsUB2CId0w0rnCWNjHa2xsV/967tlt/+sKLVMG9LCAfjhZlfIeU5hMOqWY+HoOghBN7UOQunoBAAAAAAAAIDhEXQCN2LVlAfmVqtY7Tqu57lkqtBS2q+x8UpVcBrbf1XYnVBVhd0JY5vGF3ViN+wxws7WpnFsv9jymEJOSTudnH06OofgQtCmMHRdSm+KZVsDKQAAAAAAAADgwgg6gRux9qbcnKTXu+5BQekhvEDWXWNqqgDU5nZn3CQWhDZ1oXbtt2rphr1mGDtNzSZw7apSx5TGV7BOEvm3sE4SlXmpLKu6I7NMWpuk8Tmch5okVQjZVk/lzvN8+qkAAAAAAAAAACcg6ARu0LrncwZPNUmra10rWF1ZVV2kwbh1/1BHh2vjfvV4nW2D1pWVTGY0rTtP/bF0XMgZBq+xwFVS7zpGSVaFnX49FxeYdlUAAAAAAAAAwMtH0AngaC5QvVaw2tclg1frB6318kr7Y3+fg4TBa1cg28AFsVeZ3reFn0UXiVFeSElRqkwTKa9qOd5MthGBKQAAAAAAAAAMj6ATuBHPVwxe7pPqen4dk8/r8d2T7xodsBebUvgMyrR+Pme6fU7nS8PUtQAAAAAAAAAwPIJOAAdzoWpYx+A+2YacYwxhpW0H7KXv75rPam2SFKVKY7QujdLSKqmn1F1KSlRuqjTCN6oFHZ0AAAAAAAAAMDyCTuBGrNbna+GbToxWa3tQzXKrPNuv1xaGrmMKYaXdLthLhJyLxOiungv281r6dnr+a1zSspRmyeEVAAAAAAAAAPD6EHQCr4wLJ2PLbuyWD6krSYrUS9z7OYLYoVy8C7a0mylV75NxTGFbJN2v97LcH8eqCzX9emw4emoFAAAAAAAAAAyLoBN4JeZpooei1Ndnq6n3v3x/+ZxdoX21Ba/h8in1kgGs75DQ9dQQtmv/53Ic09ceoitgXDYExG3h6CUrAAAAAAAAAGA4BJ3AjVjl7duzvNRKUu4So3qc5WXnsaFpVl3P1XyWKFuWvdbv3/Nu6NgWxA6pTyesdFjoGi4fGsL22n8yXPeqk5ZWUr/7ODRYHKqjEwAAAAAAAAAwvBHEBwDO4R8XVpN6bLwuv7ey9ikz5jG3dpYZk3kpTRZJbFw42bZulUufUqMPud05jwtMXfXX55GEKFznB6/+Nefp/j018UNVd1zXulgI64y1E9bxQ+plbq0kfZExNt+GsC+sqfMgdHICAAAAAAAAwOtF0AnciCKXCreQb4O3ZSpTLqyeJON3TyZ0DhCxAAAgAElEQVSpNPWa7DbhaL5NcjYh6VNhf5cY86609nmWmA+F1YfCVmFnYfVbSd9Im2VX5a3LluVmv836OjXy93FiYz/8dGN/3Y8To/euUzKvuljdOFw3T3dD1aYE69ydsI471r0mv5X0mFv7RWan9dHmuyHqIq/eu7LYr5JMNd4ek+VWw8WwlVVptSyNVuXQdwIAAAAAAAAAuCUEncCNK4vm9Qt/Rb4fQj3X4agLSReS0aLUl80edjN+TqvlJ/l1O56aKkz9wbve97mVyYzsotQPddCaWykt7CZ4TbJtAPg2L+znujv1S16at9rWp8yYx+fSLiW5DtamGp7rSaWJhYw7vADYH4dBZMgPJps8batJUhuGl3vcer+6/f1jkltu5QQAAAAAAAAAvHoEnQDOoi3Ik+pQNRYKunXetnWw7ILWMHh9jqxPUsnvYG2q4bnCjtdz6nptYvv2OSbS0bkzPuS6Q/Mbavs8J3NIY7gHAAAAAAAAAABBJ4Ab85LCvVPFAs1Y4DmkuYzkTVk7V7xpdlnsj7vqRiLNmntxJfULT/tWN560XxIAAAAAAAAAcGEEnQDwQrU8q3NT51a6H+4WN/yOzYfCal5Imprt+L4jqWxTSsuu63eEpe7++tZZIsWf6goAAAAAAAAAuBaCTgB4oWLP6pR2g8+Hi03Ie7x5aqS0Djzr8VD2ukMDs7TaZxbcIx2dAAAAAAAAADA8gk7gRiQDhkUYn7FMXRvjujjnrqMzPaGb88Iap8uV9Jty9vW6dwMAAAAAAAAA8BF0AsCNcd2cYw2//S7OeaFBOzqP9WitPg19EwAAAICkf/0f8r8c+h5wmD/5Jv3Dn82Kv7nkNR7S9K/+u5+Yv7jkNQAAAMaAoBMAbow/de1YntHpcx2dD+m2q1Mab1dnzJN5WfcLAACAG5brl19zqw+F1afU6ENh9ePE6P2a5bEuT75L9EOZ/uKSPxbfaqRT/AAAAJwZQScA3BgXckrjfkbnXBr91LVN6OgEAADAmLzJjPLM6I2kPDN6lJTPWB7r8iQ1WhdWk9RoaaWZ0VkrAADAa0LQCQA3xoWcQz+jc2Kkj4nVNDGaJdLdJNHHdbm7UyLNZPXdrD3sXJb918eep3ludHQCAABgjJ5Kq8fEsDzCZX/sQk6pCicvUQEAAF4Lgk7gjNJkuGv/97+X6IfVdvnr8/bPOL9fWK34q86bVBbS4xujb+rlN/fVp9r7RPpYf8L9zUD3dohJx/92mra/6XFuP1tdNo0PDEfp6AQAAMAYuSAtW5bKZwnLI1p+nG0/1EwuPKvN0kpvCTwBAMArQdAJ3Ij3j4neP26X87dWWWaU51V1nhdWq2KbevrhqCSt1lZfFnYnFv0iY5ZrktJL+af3+59A3wTrvp1KU+/D8LfvjH1ayTxOq5lp54XMQ7o/S+2Pczv4x9tVWT2DcxF2c16JH5L641hIGgtFN9ULQ+noBAAAwNh8za3e1J/9ci9UeyrtTsgmSV9mRvcsX205D15/v6PzEujqBAAArwlBJ3Aj8nw/iHTr/G2TTJp4wefjLDzKSJLxQ9LYuSXpd4vm8PPzuvuevz5bLXN7lQT1KTPmG0nL3Npjx9NMmk7aPzHeJ9Jjw2/Wx5nZCZ27PE5ln1YyJpVsHbK5sQs5n1banPCp0Gg/zjZNPTs2sVDUD0TXXvj5plj2aSgFAAAAruJN8FnDTZXqT6X6LKu3S6v7OngLl6Wq+5Dt/ba7cPleRg9Fqe9N9Xrfy2y6Ov0pa93+dHQCAACcD0EngKhYSBr6eNf8yenjXY+LvDXKMmP8QPWQ6u4vNm76nrKsasU7crxzndj9nJMLMa3XSeiP/ZDTDz3DAPQlaGr2nCTVtrZ6Tf71nq97aQAAAOAgfsDp3Mso99r9wmVpt/uQ7e3b/XB5niZ6jOznvw9hGH0pdHQCAIDXhKATuBHr/LrXm5zpt0cYqB5a28Zt1ztl7He59gmEY9oCW3+5K9R11U1RG47XuaT0oFs7m3PMduwC0K56bZOEoBMAAADAONHRCQAAXhOCTuAVec6M7nO7qeXEKFkfXqXjglX/+Es6Vwjb5NBQs+0cbSFt31DXDz3D8aVfi9dqqIAVAAAAALrQ0QkAAF6TK0/6B+BSniStCrtTw68yr9bf1/V5Ha9JR3XXi9XnuuMwVl3I2baPVAWip9R1fvrX0pi96n+d0zm6cZvC0Gt3+r4W154yFwAAAAD6Wl7+74sBAABGg14f4IasU7NTJ4XVOjWaFLufclaSJt4x/n7r1HSGma6uCquJd76n1GiyLLWSlBY2Wtf1Pk+p0WMduIZ1M+dppD6qCmTb6pO0U8NO1q4qaRPKhnXzWnvnV8vYP2eTSdYcSLptsbq5F2853DY2y1KaJdKzSXRvy019aejoBAAAADBWdHQCAIDXZMT/HA7gEDYSpq0kKa8CxkZue24lEz9Pk5UkkxnZ3MpkuyFrG7fPk6ow1gWlfgAb7n9oCLu5x8I2hq5tYWwfLkz17QWeLsTVfgDr6qe8OZBVvjtVcFg3r1EeH4/NrO6EdOGmH3K6ENRVAAAAAAAAAADaEHQCN2JdSJN0v0rSMjOa9QgwV0dMb+OC0UMC0p1r1tW/Rz88tbndDWN7+lqfZ2W1CWEP0dQN63sKOmdjYWtbN6xSs9MV29oFW1/jUI/du1zFsqhq2Mnpd3S6cNMPO8daAQAAgDFJE6ko+42b6qn7vIbjz1Yj7+HHTPrn77bLkwM+/v27/9J/XwAAgFtD0AnciEVipDqU+2KN7mSrWlolhdXa2++uPM8DOxaJ0VtjG0NWP2xtGjt+EHtqeHqO8zR1w27C13q7v29oarQJWsPpgcNphtu4sLUtdN27dh2itnbzXsGy3B0nKrXUfvX3C48dQ42FsAAAAMDYxMI7f72vKKXf/Vjo64IHOh7rzZ3Rx/dp9LVtC1Rjgqel7C07fQLQonsXAACAm0HQCdwIm1spkZ5L6V7VJyIXaPrhZmzdse7KKkB1IWtb9YPXnfVnvJ++2kLYLgdN7Wu3x+wEpwcGry6sDDtdm6okrerlQ8LRa2jqkmzbNnSV9oPYWDALAAAADKUpQGsL2tJE+rqwytfe/tYqNaZ1fMq2Y48dy/XD+lVWH9/3C5bPpSkA9fX8eAsAAHATCDqBG/Jc7taN0uo53Dm27ljuXHW9l90Ers/l7nYl21DWDzaPCTmbgtKu0DTsePW7YYcWhrC+1dJqku53qjZ1rtr6uatj0tQ12bZt6BryA1AAAABgaKk320gYbMbW+TWp98kLqyw1O6Ffasze+qb9/FAwdlzbsS/p+mFNku3rHHuN/dc/fK9iFg2tmHcHJpd0dAIAgNeEoBPA2TUGrh3bDuaHtd7YBayuw/E+qcJPN7Z5PJh17gealtQPXNcNn0yb1vvn8EPe6Uh/y8e6Jl+Kl3a/AAAAeD1ioZu/LRbESdJ0alRGPmuE65v2C7smY/s1HfuSrh9Kkt3rxgLNrnCzzV1ahZ+xALQt/KSjEwAAvCYj/SdwAIdatcxfM50Yrdb2LPUlcKGl63B0XaX+ttj+TcvXEgau0X06PiS7DlVXHzWOTtVQV9ckAAAAgNO0hW6uJolRklafGZKGdCxcv7ecSGUZGUfO13SNl3T9S1qX0sR7v8KA0wWfsW0+OjoBAMBrQtAJ3JjlJNFsXe6MXQh6rnoury2A7RKGnPfJkSGsP1Xw9Ew3d6KRzAwMAAAAIJCcOKNN6U2B64+vZejrn9s68pnPhZ9t4aaPjk4AAPCaEHQCN2KVV9OUzrxPRW48TxM9FOWmHqrvcbGQtW081gDWGTqIPUdn6WokAWPX5/HP6wNOlkizHi9pOD3uKTW0LKXJAbcMAAAAjFGSSspPPEcSH1/LkNe/VrdnLPyUdrs/fXR0AgCA14SgE7gRP+TafEBNMukxt/YpM+YbSVleauVVXz5LlC3LzqqsClPD9VN//fN2v5nqMLNhHIavp4SxsWPaztM3kNWZA9lzCsNUqXp9N99jbquVbxMz9F/zzmX03m7HD4XVPDV6kN0sH6SUlj12WxbNdZbuT5/rh5phDYPPWSIx6y4AAABugZvu9ZQqxcfXqkNdf3rmyYXcZ5O2P7r0NQWgQ38GBAAAuCaCTuBGLNdWpv6QZdfSs2S0tvoHbx9jpCTdfhLLjKS8/mSUl3ora59UVjssS30jVSGnpP9sjT7IbpZd/d4YvffWr3Lpx4nR+zp888e5+7Sm/fD1lDD2QeWmo3We7q7zA1m3bebtP1uXe92w8zTRw5W7Yd29+X6rbWD9mFv7ZfMOS1pYLddW8p7BuXn/rWSMjCT9LLeDPaXTfTsPslJ1O1W4WYecKlR9Ah/gU/iy4U+c99Z73aNhMEpHJwAAAF66qZHKxNThnVWWmaNqkhhJVT3lPC/t+pcSfvbwdYWfEh2dAADgdSHoBG6I7ficZa1U5Nudwg8/yzocnZpqytN/2D1aT9qGpanrwltYfS+pqAPUKjy1+sEdtjOuPqUlmfSNqiCvj8enwn7OjHl8KmyWGROGrS6EXeVVULoTqC63y1m+G87+Oq+CxOdZYu4X5SZQlAp9kfSUGfNNXurXkja1vu+yx/RONreSCv0uWL9W7L2KfxR1gfWzZNQRWfrn7PpZuAb3AXwuo/fajh8KK6WqAs96PFot3aO/KWdfr3ovAAAAwJkVqZHqz4hVWEg9tF6bH356H313jPkjFgAAwLkRdALY0/ZcRxeW7sVy9Yfjrr8cnRrpeb0borpgtam6sG9hZGx0GlirufHCvefgLvaWrVxCVZ271JNkTG61cK2HkqzXERtWU19varbBpT9GQ0enN22tP35pHq3Vp6FvAgAAADhRVn+Gce4yaZFT+1aTHRd2Nh126NS1bv/wfPkVP2L96/+Q/+X1roZD/A//ZHwftv/8p9mfD30PAIDbQ9AJ3AgzzB+THmyt/Xt165qqr+37DKZu3QiX++o6xnj/KBCOxyLPzGB/zet3dL6tO4B3ns2Z2s34pXka05sMAAAAnCBJt39kurLVMpol6eVfp9jUtX2mrHWOzF+Pk+uXX3OrD4XdPLqGOmzNZ4n+5Kfp332x+sVydFEnAADnR9AJ4OYcEo7eumxsz+gsdp/Rue3ofFlvEh2dAAAAGIv0xMAt/CPTiarlNKPGqjHb1+gcr7/T1YUZm6K27VzTK37EepMZ5ZnRo6R8Rh26fvV+mGb1z8HSVuOhKgAAl0TQCeCm+N2bx3Zy3pKhOzr9vzqeJZISaSZbfUhPtZm2dha5yWXXPMgDoqMTAAAAY1IUVeB2aJWq0C4UW4dd/mt0zOseM8+r7atCmp7wQe6qHZ0YnTeRHwAXNg5VAQC4JIJOADfFz58IPIft6Jx4r/ldZrTyHv4aTrv0k+lh515H/po5/AvnveUzBqd0dAIAAGBMXHh2aE0yaWVf6YelM5jW/6p26Osek1vpv+RSvq6Dyny/K8416nUFmdfu6MS4fK7/sphZqAEArwVBJ4Cb91pDzjGaJkar0u7VQ00iz6cJ171pOd4PSpeRcVcoSkcnAAAAxuSUjk66/053ro5Oaff9CLvi+r5XvKev27tDHugKAMANIOgEAFycyw1dqBnWa/NDUX8chqMuEF16dcxT6gIAAOB1OrajE+fB644x+dr1wFcAAG4MQScA3LAhn9HpK+rPWefq6LwWF4JOkir0fFP/V3NdSm+KZVvTKAAAADB6SWqkwR528fJVrx8wLu4ZnXT2AgBeC4JOALhhQz6jM6apozP2zM1LcaFlrHYd54+fL3ubAAAAAAAcjI5OAMBrQ9AJ3Ij/9uepVkur5zow+vq8/T+2v15LZcH/0b1VP8lkZ1n10Mg390b3dSD300ejdS79Zsibq43p58+Fqk0VAAAAeC2+uTeSaPsCbskbWjkBAK8MQSdwI769M9Kd939mP26Hf+rtl+dWn3JpUgdPT7k24agkrdZWq3z33D/k0oq/CLyYn9/tfwj57nF33WMmTb1pkb59Z8I3JPoG/Ti3fMI5E9f56cZ0dAIAAAAAxoaOTgDAa0PQCdyIvOX/yGaZUZ5bZfVf9X17t/2r3fct5wqPC/341NwC98Oq+579rtNr+ZQafahD3kPH00yaTtpzw3eT5r+enGRqfC1DpseDNZ9W1Zv4OJVtGve62BWM+Tmcffkdn3R/AgAAADjWVRruXv5HMBzpp/XfRdPYCQB4LQg6gRvigslw3FS7dO3/ODNaZ0b39T7rzGhSB6OPsx7XeGt2jvHv/VnarJeq7rn7+rCmcd6wf1tYOwSTSrbYXecCSlcf0m1g6W93y0/Fdl//uHB8ve8KAAAAADAGuZWmV7xemkhFua0Yhnv952ky9K0AAHBVBJ3ADXkqpFkwXprmrGtmT/8Tz0lulXtjaTcYbQoy3Th2TJ5bTbyxJE2k7XUaxjvnbVh/qEsEpGHIKVVTzGaZ0dNKJs+tnrT7xsVCy53gs2H8kixLaZZcrgIAAADAa5AZXbWjMww3Xc7mwk/G1xkTMgMAXiuCTuBGrHMpkdXnzOg+t0pktVa1rvEYSeXEKFlblR1Tsh5rVgeM1whhm8LMPLdaGrM5pws+XQjrrwvHxwakMbFrxGr4/fQ9LnaedS6pxzS4Q1uW56nSNtQMK8EpAAAAAFxOGLgxvu6YsBMA8FoRdAI34qmuj7nVk6SkDjyfu+ra6lFSsr7Mn3uupepaa7s3PjSE7TNu44LVZSHJmE0IK+0HsWoJYaXTg9iuaYVj+x5y3Kaz9QX9lj81fJR2A8+YcwWqXdcBAAAAgNegaZZUf70fxEmML9nR6YedI3qCDwAAF/WC/gkcQJtHVWHnqrBSapQuSz2lRqqDz8aqbUh67DWb1iWZUZnbnWu58aPUO4R19UnS49o7Plh/qE3wWnfA+t2wfY49pRs2WVtNsqrj0p3HXz7HtMLSy+nolM4fOl5yGlyCTgAAAACIC0POpnWMzztuCp0BALh1BJ3Ajfhad/CtMyNbT8tqvU5AkxlNCquV7R6v022VtDP2+SGnCxzduklRdW1OCqtpavQUjFvD10gI+6R4sNq0PryvcN1zJIR13bD3dfB4yW7YcmK0dteow1J/2Q9h1TAug/t0y+78l+rSfSnO3b1JNycAAADGKIl8VgPwehF4AgBeG4JO4EYss2oqVhduumrqwNPmVqt637axC0hXkhSEpz53XlefgmU/cF1551kF55kaaZ3+/+zdS4z0an7f999D1qUvb5/znpsuI8lzJGsSa6ALjJHhKIk8ZwRo7QgIgiBZWNsshABZxIMgwMwgXoyTpRcBvNMmcAIkRrwVDI3hIHKCMazRXYEsnZHPaObMOXPe93S/3V1VLPLJgnyqnmKTLNaFLBb5/QCNP8likazqfvtt1o//h+m6VdMLK91746744WVVR6r/mAtsXZjq5q+VdsLeZwHvo3tCg92wciGkCy+z+essSE2WtnJ6o7vV28aqU9brfD2VgeesAAAAAIAd+UOysrz+8vw6AAAMCdf4AD0RzK2iWLqzZqMuvOXSus5HxfOLuV1NO4v508QqH6iW1Vm2/twLS932zchoYdfrlk3fZVcou8B229c4Lj4WF5y6Lzf/ypvOr+OeO463T7v5oml/Pl8X3vy9pDBrGfSnF7FVkr22om5XfzpZdithpDMSAAAAAOBzYdws98mkC/FYXr1cKg803Tpj070vAACaQNAJ9MgsMLpI7Eb1l0exVtUFo/n5WWAUzO1GYDoO0+ovyweqZdVtN8jCUn9ZPoT1A1a3TJKmXnDnL88Hso4fkvo135XqOlDz6xkviHXLFlal037XrB+UumkXhrrnuZoPV/PVX3eRyy7LQtNxvA5Dx7FdhainUmcY2LpfdbbXFkJaAAAAANifC+MukuLQk+VPlzvu8bLOTjo6AQBDw9C1QE/4oWZVzcsHoxeJXS13oli6kF1txz1WFKxW1TtrdKGnyy+S9J6UCysFyXq7dwXbuTHZdhKrIN5cp+z1VNb5et4Fuheug9WFh+F6aGApXefGrN+f20i6yd5eP0xdzNNt+iFlPoAtWlZV3ZDCVdUd60Lp0MDnYB7XX2dbnYbV25kGaVB5SJ0GEueOAAAAAI7l9VD6/Gv7Pfd3Xhz3WNrmh54sL1/u5B/P4x6dAIChIegEeiLfrVjXoyQldrP68sv8+cRqlnUzPkoygfSYSJeyG9UE9QPYouV++HqbSJfBZkjqh6/7WAWlsS0MYl34Gak8hM0HrpJ0E9qNDlR/X9uqC3S31YvEKpmmwaZbPt3zZ6EvtoWmZQHp6vGa3aPjww4TAAAAADZEBadyDPcJSZqH0jSWwlCKa1woDADAkBB0AjhIUWfiYxYEubpvCCvpafDqtpsLXN20G0ZWQbpfF8TOcsv9xx0/pKwKZMtC2Hy3q98J6z+3Vvdrzeq6UguXJ1ba0t2ItaqAdFuXKAAAAAA0gfATUhpySruFnCE/JwCAgSDoBHpiUXT2c4D5ONA0SjQZGy0i+2Ter747Gd3odB2F+bA1P/xroyFsSfgqeQFssg5Zi7pg3by/3my0GV669VbHkT2/7HEcrigE/TCZvmr/SAAAAAD02SyWLrZcaLnt9H9k0lNKAACAISDoBHrkIQx0FSerULJOlVT42I3SezzeyWgqu16/oPqm2fPcsTSpTgjrB69+MNt2IOsHqo/J0+VFwau/LD+0sL9stW7B42M6EQEAAACgU0YFnXax0pBTWoed+brL9toScs7ZWXxrAABDQc8P0BN/NZNu54m+t5TMY6KFV6dZGGkeEz2EaSjoqnvMXyZJL5P0TMlllf5jbpl7jnvcr1dx+WMuYK2q/vRDGGgyNqvqHruTWR3HfBysQk+/TqNEi8iu1r2T2VjexJekjX26afeYP+3465bZ9pj7evW4/rqNSp+CA1xbLo8GAAAA0Bw/9HQ1v6xL4pjapQoAwJDQ0Qn0xNJKykKt7+UfjKQktgpCo9Ey0UtJUqLb3G+AwFqNYullaPR8nuj7odEPZYHlaJ7oYZoGjEXTLtj0q88PO6+ygHRbldYh7Uul21zEUhyuA063r3xge+U95jo+3eN1O1x3nb5RFm5627wbB5rKpo9lHbBT2Y2u2Gm27GUYSKHR4jHRchrodp4o8G6qkcRSMlsHbEu7/r7mq/PTl6U/MjjAveFmJwAAADh/f/GdpWaPXMS3r4tLo5/8sXY/Wutq2Ok6O6ndqBL36AQADAdBJ9AzfuAl6Un4tXFLykgKY6s4NApjq0WYhaSRXdX3YyspTp8fJUpiq8nISEsvyMyFp8ksC1GXiW6zx6+X1t6PjLleWvv9aWCSeaKRpFfW6jaW3lymwerzeaLvaT3vuOWvTctDVycfvo4ec2FslOj71uj5Y7ZPr0ramP5obPR6wfT3stf1pjf9QpLccLjLRHEimVkiG0hmZiVZ2SC7wjJ/s1BJSXbpZRAahbM4/b7M1o/HBWcp+e+vq+tg257sjqljI93UHEPpMxWBbFQyAvK8YHl+WdG9NY/h2trsZx4AAAAAjsM/9xlvGYPNDWnbFXGchmzUblQAAIaEoBPombLAq4wLz4pCtLLtVHUSJjObBXV2Y7sLGaMoq9ljLkwNY6vvhUbJLNEsW/97SgPTcbYN16X6IgsAXejqB7BjpU2t6XHE2XHFT+bT1/U01F11wuamH4umM249PzA2IyO7tOv31D/5jMvfO//7FCkdW7zs++J/T6o6Opcjc/b35Sg7wS9a/mzLtvwPDlwo6oejdYNROjoBAADQF8G5nzD0VNkFn+48qEshp3T6DkbqZgUAYEgIOoGeqwo56ygK06q2v+om3WG/Lswr2lb+3M0P/oLcdKw0HKw6zm3vx7ZguMxGYGylJHcsRUFkfr4srNymah+n7OjsIj8cddNF4WiUC0FXNfuBpKMTAAAAfUDIeZhTvH9lASgAAMBQEXQCqJTvONzWRVg23YZdjrNME8Fw3eofQ51jzoe3RQFqFzo6F4nVJDCVVepWh6QLQcvC0Cgx+vGH+bYGUgAAAKDTJkZabBkiFeUmRz6NcRdYTvmeAAAA1EbQCaDUtuDQqQpD2z7Wqummj6doKNqi/dft7Kw65qKwtGhbp+7oTEPM7bVqWKYoqa5tcvscB3oyjDEAAABwCocOVRkQqh2kiaFC5yXnRwSgAAAATxF0Aii1LcCs0+3ZhWPdd0jYfY7BVzUs7baO2G3HXPf5Xejo3NbNOQnKvycuAN1W28RQUQAAAABOoSwABQAAGDKCTgCltnVJdmXI2jrHJzXf0Zk/jrrhZZ0u0LJ9FO3HX3bqjk6pbldnt4auLeK6SN00HZ0AAADog6DiwkN02zQoDj/HRpq3fzjoiGv+SQMABoZBLwCUqjMUbH7I1Pz0KY+1rSFr88fhXrv/fpTVsvfPf37RPorWK9r3cnT6MxzXsVlWz4XfyUlXJwAAAPrAdOB84Zwd8/2LrXQbp8Glq9u+bpfFQWd06qtdcVL3fP8BAANDRyeAraoCzy7cn7PqWE/RXVo0tG9ZLXrPdnkPt93b8xw6Os8RHZ0AAADoi+DU97qApDScjKLjbIuOzmE71ecxAACcCh2dAGrb1uF5ilCxzCm6Oescx7Yhf/11d9m+P912N+2xuCuRz+G+M3R0AgAAoA8snwodpKvvHx2dw3aunwkAALAvOjqBnvjcm4Hs0urVY/oH7SeSFke6GjSv7L6Xpw4Vu66oU/OY79/ISG+PJI2Nnl0aXQbS9Uh6cdBW2zMNNus8Wd9zposVAAAAOHdhIBlO4/YWdvS8gI7OYeMenQCAoSHoBHrip26MJCO98fSx5dLq5XI9fxtJdrm+ws+Fo06TIemQ1Q0yf2S8Oe9CS+eNqdHY++39+pUpvVzz0wdrziHoLOvizHd5nqpKT4NYAAAAoA+4YLV/2uzo5Oene7jNCgBgaAg6gQEYjUza6Zd5+0KSvJORgnA07z6WHgvOlq6sNW0AACAASURBVBZzq8eSkGoRWS2WxY/5uhysrroka7i52UzALrx7UE6mRtcFz3n7NWMfYpmrUKtaZ1/+c/LL6h1tN3WhU7OonsNwugAAAADOw7jhbDA00gPnMIPUpVsKAQDQFoJOoCeWy2bzramk6wuj5dJqNPLqKA1Sh8SEko2f1oI1tz7vfpGudB9v1qr9StL1RNY9VwXbcetEy/Txc3Hqzs2qjk4AAACgb4LQaNTwuWSfdTVQ4haNw9XVn0kAAJpE0An0yNwYTa1VNDIaL7dXSavQsmhZfrqo+o83qUthqgsb8/UYzysKT920jdcB5yrk1DrkdI/564xP+Fu+zeGSAAAAAOzHcmuG3iHrGrbLhJNxAMCwEHQCPTE3Zl3jmlXSPJZkjKZL+2RZfrpQFnK6kDVf64auVWFsNDKr/bRto3u1pB5TUQjqT3/6YE3ZPlddnYvyrtBTKBsSFgAAAMDphfxt3jt0dA7bY9CpjwQAHOAL3/z661//7N//m03v5zf+4Gvv/8mXvvp+0/sBmkLQCfREsGP7XDI2CiK7qvPcfN06924ushG2asfQtSKM3ZiuoShw3TeIrepm9es+yjpnqx6vW91zTj10bd0hYf15QlAAAACgeWEomUCSJRTZS2LT96+D6OgcNjo6gXb8T3/Y/GUlNxOrNv5J/+bPf+Vrf1tf/WrzewKaQdAJ9MS9pGBklCzt1ippPa5oRQ1GRpeR1WNJlXYPWLfZNWgtqoWBa1mtE7QemQta/S7VosB0WfD4LsFrG0MKH8s8Lp6uY1ojyC3qKN21ro6PjlQAAAB0TBhKcVy/Sml9bWT0Kjif84YuuVSw6obd5/1vEh2dw0ZHJwBgaAg6gR5xIWa+Xi6t7rP5a6WhqKTC6Y2aPU9l1VM3ZN0awtYIYCXpuiKAfRwZXTYY8h0axrou2GiZbuvY/I7VIagTjLp18nX1uNdp6kLMfHWPufBzfNhhAwAAAEfjh5d1qgvcLi6NLrp154uztOv73zQ6OoeNjk4AuzijPgmgFEEn0BO24n+lVwXTZmQ2wsr7klpXWci6re4dwkrlAWyTQax0tG5Yf/oYXGh6K6Op0iAV1YpC0qJlrms0P+QuAAAAcGrhnreq2Pd56D46OoftMTB65+LURwHgXIy4OAY9QNAJ9MR8ZDRd2tq1KhhtU1EIK0n33v+yh4aw0n7Ba37+6N2w2fQxu2GvJSWSAu9YcLh8+HltrT5Ipq+K1wYAAACA06GjEx/NpDefnfooAJyDjnxEDByEu4wBPRHMraJ4XReRdGdNYZXS6V3qfNRutVkY2/bXq4p5Ny2pM92wbt4FmoeEwajvvqH7twIAAADAoejoBADURUcn+oCOTqBHZoHRRWJX1RmHaWB5Y9IQ9C7YHm7eGKs7m27nLjBSJC20rhdzq0hSEO9e7wKjm2W6fb9K0tTNm/p1127WXasZGVmvdrkb1uT+OllwhtuIa2v18tQHAQAAAAAFJkaan/ogcBIMSQ1gVx35mBM4CB2dQE88JmkX5CwLMWeBWX35AaZ73AWhZdU9p2x9f/vbvvztuBA2itNt+cfmvty8CzN9+eVlXat+d+suVdKq69WFrjZX/ffHdaBum2+6mpJOWDSDjk4AAAAAXbXgVHCw4uy2K9yjE0BddHSiD+joBHpi127DR0lKbGndtv4u3HNmo83Q0/G7UP3qh5xF02XPc7WOJ8+JrS6Udp/OKjpf852u+flVGJvrhFUkBclxOmGlNIyVpNtIujF60uV6SmMj3dT8a+mdafXj86T+8vz9NJtARycAAACArroM2jkvkugg7KI45h6dQBv68vuPPgn0AUEn0BN2aXUnoxt163+n/LCvReqGrXWft0sYO8uO61GSCeqHsGXBaj6kPWYIe5GkoeeF7Gr4Yd9GIJsFqpMz+S0/3jK+QNnjdc7bIi8MnZdN7/ghAB2dAAAAAPYVFpxO3Iykd69239ZlKP2/LzaX0dE5bGFIRyeA+ujoRB+cyUfgALa5k9E0SnS3LTGSdKN1KNp0vV1KN1Jrw6jmg9Vtddu9N6tC1KpldZ7vXAbp0MP+sUkqHPbXKQpXi8LY644F36fg/5Pwp4tC0qJQdFW9MJSOTgAAAACndBlKj3H6lTcx0kP7h4QOiOM06KSjE0BddHSiD7hHJ9ATny6sHsJAcSLFWTAzjRJNs+TGn15Edq96J/OkTqOksLqw061X96tsP3Wr62ytWyXpdvn0/XzILrEtq0XLXECZn99WH7Pvlwta8/fZLPpy92T1p139fmJ0u5ReLKT7gtfWtkUW7G6rXTAO1l/PRunXW5P06zOX6RC770ylcGL048Gc00YAAAAARxPZp19ligJOh47O4erLUJoA2kNHJ/qAjk6gJ2Yzq1lB997ISHaxblMbWyvJZGc+Rs8fEy0kmWVa41B6CAMp2axXUZLWeLNK62DVr4vYKg6NruJE83GgafS0SnoyvYisNDZ7V7+ztaxKm+GoH8o6N3G6bDpLpLFRHKeBb+w/L07f76usuu7Vq+y5N8vt9SE06/VzHbFx7v6Tr7xLrJJ483u9iPzvudXSpt/75zdGpzzPqRtyRiX34BwH6WNVtU1uf+NAum131wAAAAAGqCjsHG/5UJqOzmGLY4auBfqk6SCSjk70AUEn0HNLK8m70jOO7eo/yKWVPgrXQ5vEsRQmiWJv/ZGRbCClt/xIVvU2kAJr9UpGWhbX5/NE3w/TMDVfH6ZpYuQC1I3ppDhs3Vb9bRWFr676Ya0frvoh7J0Xys6VdsS6fXzHGL0eJXo5DTSaJ1pmNZ4avZpbvQyNJKtPpSc1id17aFc1WUpLa/VhNu9qXv4Pm7I/RNzypU2HxV0Ur9aaSWC0SGxlLeMC0G0VAAAAGKqg4u9p9FNVp6dER+fQMXQtgF3Q0Yk+IOgEBqgoIPPDzifrFgyJY5K0w3GU5pob1fmelcLE6nvx07qZUJVN1+P2vfn8mvWxZt14bnavzdL0zZb+kXDIVVK7PNft/zHRSTs6pbpdnd3/q8p1kbrpx9MeDgAAAICemWXn3hfhev5ijxO6SSg9dOA2JgCAw8Rh80OZ0dGJPiDoBAbOBZtFIWcVv2uwqOa3n6/H1MX/kE99TG7/59DReS78bJ5OUgAAAADHNM9OjS7CdeApbU67x7aFn4sGzrtxXhi6FuiHiyRR0y0MdHSiD1q+uxmArmoigMTpjEz69diBQG5bR+c5avveoAAAAAD6Iyz4cgMI5bs6/Wn32CxefxVtb3LqYX1wch/NTn0EAM7FqZs1gGPgo1oA6KFz/CNlnmzWLqOjEwAAAECTyro6nXz46eNCZtDRCaAuOjrRBwSdANAz4ZlevTsNNms++OxaBQAAAIBjipL6F1UWBZzOuZ4TAgA2zblHJ1AL9+gEgJ6J4/RqrKXtxj066ygLD08dZvrHlQ9iAQAAAKAJ+bBz11tn0NEJAP0wjblHJ1AHQScA9EwYSst4fY/Oc7qYdxqk4WLXKl2cAAAA6KM/fT/SwytaOfZ19czo33933Ph+yro8ywLQ0EjB0nzxf/jW8huNHZS/vzANV6ndqR/NpDeftfHd381vfWTfa2tfv/qO+UZb+8KwNflvuQ10dKIPCDqBnuDqG6yccSh36s5NhqkFAADA0ATndGUkNpQFoLGV4kDmemq+OJ0c/8OCxEqBSWuS2FUHKbU7tav36Hx7pN9uYz8vo/gbkr7Rxr6ARv9Nt5De8Jky+oCgEwB6Zmm7MXRtxBVhAAAAQKdNjDQ79UGcsWNniO5CS3+EmUOko/00c2LmZ6zcE7Rbhj508TSUFJ36KDAEffndR0cn+oCgE+iJ59fpGdDtPFGyPPHB4KRGZv3zMA7tye/RWTYkLAAAAIDTC/jbvDP8cyY3z/kTdtXloWvbMB940Avsio5O9AFBJ9ATX3jL/a8UymRXFL1aSPPsqpx4lujOrv/nepFLv17eM0ZnFwUj6TXvrPZ5YGW8v0D+2uV63devzJNrsD59kLlv9hArFQ0FS+gJAAAAHE9wwAeUZmQUBLRy7MuMzF7vf1jynHlcHNJsC26KtmfDQGd9XxPsrctD17blx4P5QGNeABgmgk6gR0wo2Tj9kqTrULp2Dz4L9E7Vk9/aHG9hubS61ebZUj4sdW6XUlxx/nQ7TyofP3fjsdGzisuf3pgUL//hq6fP+aFLbfmU4fwvsyoKP293GFZmWnNokKJA9dDqjpdwFgAAAADQRUPv6JSkD5Lpq1MfAwCgPQSdQI/YIw7PMRoZvZlfuC0sLVV/0HoXsCZLq2BUXi+XVo8N1rem1cdpY606Z20sXU9kH+LiFPIqTB8rq/46knS/kLmeyBZVt89t27wKtwWm56vuMDRuvaI6Dcu7TaXi6h530+PDXgYAAADQCcEhLaEAOmfoHZ3coxMAhoegE+iJ5Y53jh6NjB4lXUqldbm0Go3MqrZhFbC6/VXUywbrttDYdc8694vNkNMFk5J0Hz+tJkyf47bjHnvIWjYfcusUhZ6SJO/5+X2antwUvQllYWl+eb57dN7jzmQAAAAAwPkbekfnPGboWgAYGoJOoCfm5mkQGYRSEktTaxWNjMbLtCaxpKXVWNJSKq1zY6TsOdoxSD1EVQjbFduC0HzwWfb8/HaKlrvpVYjpbbto2gWex+zwHaqyQPTaWobCAQAAQCcUnArWFozS80bsJxgd9v4DTRlyR6cC6YOI83X0xJh7JwF1EHQCPRJEVsl4fZaVuKE6jZHidV0tq2HjuUdWFMS6cLUqfJ1au6ptKOpqrepyPUUnrG9byIrD3fNpBgAAAACgo4bc0SlGYgKAwSHoBHoiiNJ7S8pq436TydisAtC2qjueosdcSCmVBLHaHqrWXW+v9zFcH9dq2gtg/aA1P7+qWg8l/Chp3HA3rB+q5mu01C63SEVN19bq5akPAgAAADiCCdfwAb0z6I5OMXQt+uUEPRTA2SHoBHriXpKWVtfe9L0kRVnItqUGVun/nAfWSxe4+tPeYy+VDu9zmYV/bQewUnkIG0RWidcMmeS6X6uqH+D6Aex0aWsFskGojaGFXZdrEm8+VlRdqOrqfSxNXeW3fCPo6AQAAEBfxCF/2+6La0qBbuJWMwAwLHwEDvTM/Z7PS7yuw3FsFWUnu27aX7YKUzP+/L2k6yxkzYev925dF8JKrQWxLnR13a5Plpd0xOY7Y8vmb7Nt5IPTsuVPAtiZ1XxsFMysNDaam/W0e8wffnjbkMJu+Sk7OsdGmta8lcBr4835eclQM2X3zGwbHZ0AAADogyA0GjU8Ak0f2aWVGRkFHQ+Jg6Dbx4fmDHno2mkoKTr1UWAI+vI7dmklboKFc0fQCeCJyDtZc9P+snyYusv8IUFsPnQtq04+gF11uebC16Ll/jK/M/bamw+8MDRxNVue5B6XTW8T4QLbJLK6HBs9Ruk+H0uW5YPYXYYlfhwZXe75fh/LpOYffc9y/xvtej4W5YLRfFDqzx8jLKWjEwAAAH1gal6YiE0mG0eQ9w9dNeSha+cxQ9cCu2BoXPQBQSfQE3ZpdWeNbsz2Oh+ZdEjVknps5kj/Y+ZD17LqVIWqZY9tC2L9xxMvDHXzrm50rnrvaeKFqY9ZGHq/tOuQ1VsWqCR0rTEscZCFr0MxDqrny85w/IDUhaF1QlE6OgEAANAXlrAO6JUkGXh7ViB9EDF0LQAMCUEn0BN31mgylu6ip9WFnG5ekbRQeZ2MpUUkTcZaBaDS9umqeqwQtokw9lhBrC8fmPrdpv5j8SxZ7d8tT3LB6T66FHIuEqtJYErrKfmBqJsuCkWjZDMIvY8HfNIIAACA3ggCKSTo3FvAe4cOCgIz6KFrVXIrHABAfxF0Aj0SzK0UGC0ipeFhlIYx81EWYrrl20LHaF1dACpp63RZ1dhoImmu7XWRrb+IpKlRYdXY1Apgd1EnXK0KXbcGvSMjN3LMLNufGRnNS5b3xSKxG1VaD2fblbBzmyhJQ9B8EPosZigcAAAAAN3Uo9NK7GHIQ9dKDF0LAEND0An0yMwLjO7senrh3YTdLa9b81wgKkkXidUsMFuXBXO7Ojb3eNm0H9AWdam617MtdHXby1d3XBeJVTI1CuZpvbNp2Fq3RrE0VRpkLiLpJhduumDWt8j2tRGSjoxs7rnSfsGrzy1bdODkNt/FmbdIrKKkAweaGQfrcNPNS5vLAAAAgD4wxigIT30U5+tyyMODAh32QcLQtQAwJASdQE/YhoYqzXcX+gGoCynrLqsznQ9YXUjrh7VVXJDptpevbvuzIAtHg3VI6nfE5ufzna4KjGZWUjbEb35I4FXQ6y8Lpbts/mb59HF/ud8Nu9hSg7nVPJequgB0Ep9+CFu/q7NsCNsucfftjAqGu3HLCDwBAADQB6+NpLvAaBpL81DUHeuE8wJ00NDv0TkNtfqcBwAwDASdQI88hEZXsT1uPdG9Hk3W6VhVi5R1otYxyw2hWtQhW7R9F4huOw5/Wb4Dtmz5wnWnRuvuWOWXB5tdrJLX5dqBE28/zCyr56YoBAUAAADOzc1NoJtTHwSAowo6fnuYps1jhq4FgKHpwEfgAI7hZWIUx9KdjlMl6Sq2usuuAtxWH0KzMX1odR2qVfVUX/njmAXlx7tNPlwtWl5numjeLq0WHcgRy+7P6ddzQ0cnAAAAAKCLkjO9oPhoAoauRX+Mz/NjM6B1dHQCPfHicbPFLMwFMUEWIj6PrZbT4Mm60yjRfBzoRmm4eecNc1In7JzOEsXjIF2WBaYH1wruOOvWMNTRu1zvrXTlVbssePxIQ8f670ec6ya8nSelj33+plt/EdHRCQAAAABPTUPp3avdn3cZSr/78vjHg/M19I5Ocb4OAIND0An0VD7wirNA6SNJeiz5q2+R6GNJYfA0fHpT0svQ6HlsN+qzkevCDFZ/TI7miZbTYCNAPbTmg8u6naaujmaJ7sbBUUPYm6zj9cbrfI3dPTclXUWJXnqJ86ulXb1f94nVdWD0ammVFISh+e+ftLmOSSSbbToMitZPPSZSWPxQ58wTaRqcrgIAAABA112G0mO8ro9x+bpDz7uG6kyvJz4qhq5FG/gdC3QHQSeAJ4pCs48kKbFP6ouo5C9oP0xdJJU1XGbzFfWFJMmu6l6WJWmgpOultfcjU/onStF78nF2LK764aOk7Fg3n/hybmWDNKh8EayfUxVWFvH3U/W8y0Ba1N/sybim1DaqCzXzlWAUAAAAQNe5YLMq4MSwEb4wdC0ADA0f1QI4uTgp/qp6rOwrWabP86vbVtljcSLdBmnImV/HP5ZtbI3fqG6dfN0l5NxFWfNuF5UFkMeu86T4yz22SwUAAACAY4ls8Rewi6F3dE7PZVgrAMDREHQC6Kx9wj+bdUb61W2r7DF/f/l1zt3lGf2Wb6ur07dvWAoAAAAAbSEAxS6G3tE5jxm6FgCGhqFrAaDHzukenadAJycAAACAc1UVdi6t9OpVsycwb0ykcCR9/NDobvRjV0bfnUtJ3Fy6G4RGPzqVvvPQfIJ8c9PsFbR26CF4IH0QMXQtmld+AywAbSPoBIAeO+U9OrnCGAAAAADqm3n33bwI03m/7mIRJfrDP4uOe4A5f/fzY/3cm4H+we/NG93Pf/trF/rav1zYP/84aSxW+Km3A/tf/vLE/L1/OmtqFyv/wd+cNrr9wYcvXKAMAIND0AkAPdaVjs55kg77WlYBAAAAAOtg04We+bpL8GlaOtdqYz/TkTFN7mc6SuPBtt6zJg2+o1MMXYt+GQ/94gWgBoJOAOixU3Z0SptDvVYNB7vPkLBVwWmTwSrhLAAAAICm+F2dTr67s2y9vLCl1r4+7aet19KkHryEg32QMHQtAAwJQScA9FhXOjq3mdc4SS97TlWdhuUBqwsrdw1J3fR490MGAAAAgJ2VhZt+8Ak4Q+/onIaSmh21GQDQMQSdANBTYXD6js5TqwpQ/cfqhqP7dJ4CAAAAQB1RIo13GD1mW8gZNHzV63jUzn6cNvbT1mtp0tA7OucxQ9cCwNAQdAI9EQZSTAiDDD8P+ysKR6fhOgQFAAAAgKZEJedxuwSgbYmW7e1r0vAnmE1vv01D7+hUIH0QMXQtAAxJj/4bB4btV35incC88lr4PnxY/4V7u9wMv148koSdkzCQXvNuDvnGRJpk46dejYwus9/oV6F0FcpK0v1C5v2hn+QcQX5YXEn6kHt+AAAAAGhJUQC6LfwMGg5HVx2dLYWwbeynrdfSpKF3dIqPugBgcAg6gR56NpFsLJlQejap+gt3HY7aWPrEuxpzaqQXkdWi4L4GL0rGQiU4XQsDKQiNno2evv9hIL19+fQ5b4w3170OpeuJ9oopw5Ex8dLa5dJK4dDPco7v2lq9PPVBAAAAABi0su5Pp08BZFv76UPQOfiOTjF0LQAMDUEn0BO2YLjNomVV3sz9RrgOjXSRBqYuOM1v02wM5Vk9rucP5rsdT7K0um3pBvKvjaXL8W6B4HWYvn7XPbmLh1jmKpQtq7tuLy9ecmrTpPvBXyILAAAA4JjmsTQ/csgWBO2ct/RpP229liZxuip9wAhMADAoBJ0AtnLh5qFhaj5I3Wpk9M71Zsi6T22Cv+2HWGaXfdk47dS8X8hcT9JwU0qHmfXndz0e99xjBqYoRkcnAAAA+uD+wSqJOW3YVxAaXV8dL1U65oW+0x50JmI/Q7/seRpKaumieQBANxB0Aj2xXD79SzYaGY2X9knNr1PmUtJjzdqUfMi6bz22fHdr1XxR+Hq/WIebbh1/eVlw6wJSP1S9CmXvYxkXuN5nQemDdg9Mj2lspJuKny/fZyp+iMqGY5oXLM8vmzf0/aejEwAAAH3w3Q+Xenk38FTkAM9vjH76J8enPoxSQUMX/jqj7Hyv6f1I0mRsFITN/axOxu29lqYN/XR1HjN0LQAMDUEn0BPzor9k42x5vubWmVqruTFP6n22St1aZlpwOWFZCOuHsVUhrNRu4JqXD1Cr5uuEr/lQ1F/uL3NBqL+OCzaLgtE+GJdciVy0vOpMJh+YulDUD0frBqN0dAIAAKAv+hDsoFhbgVef9tOHkHDoHZ0KpA8ihq4FgCEh6ASwCj/z1ZcPQf1lRdMusJwbs1sI64exFSGstHvgWqQohJXqd8O6kNWt44euy6XVaGSezCs37SyXVqOsCTOarx/fJbBsupP1nOWDUTdfFI5GuRB0VV3g3IezXwAAAEBSwBCnvRWELd07s0f7aeu1NGnwp6slo0IBAPqLoBPoifmOV+xdLq0eR6ZWTcZGtzK6jKxuR0ZTt0/vr+cn00Xdo/u8rhohrFQdiO4cwkq1u2GX1irxlt9n4ezjyCgxRpHSEHSpNBidx+kV02OlgWgSr4/fPe6mVRGwbuuGdQFrVwZRWiRWk8CU1q5xIWhZGBol0rOYoXAAAABw3uLQSBp6+9f+4h6EYuifwXd0iqFrAWBoCDqBnkgK7tGZd6206zEYGT1m3YJ1ahLZ1XOSpVXgbSdZ2sp6ubSrdXdxaBDbRgjrb9tt15+eWqt5bJ52qLplcW47XgfrtoBVSkPSuTGaZqGtq5JWgempLRL7ZLqoRsl+349xkIaOVfXY3HYfj79pAAAAoFUjQ0fnIXY8zW1d2NKwxG3sx4yMwgbv0Wmyb2Zb71mTBt/RKemDhKFrAWBICDqBAXFDu9YJRfP85/jb2RZ2Po6MghrruuDSze8SxLoLkN2yuV3v79jyQevUpPtz1V9+q/TYptlhzK0kY1brF6kbwBYFufnnzo1REFmpAydqVd2cfhi6KzfE7LYKAAAAoFjQwRFWcB5MKIUTfn66pgPXPJ/UNJQUnfooAABtIugEemJWc72LI+/XBaDb6rZ17iWp4DnHOLa8Op2ofr2WViHsRqfr0uox295jtBnQBpGVCgJctzwfxuanL8fpNv1aNDyxC1j9ab92gR9mltVzQ4AKAAAAoOvClrp1+7Sftl5Lk4be0TmPGboWAIaGoBPoienSaj4yT+qF0hDU1bqB6CkcO4R18sFl3XDW1W0hbFGo656Tf64/7x+Xv62N4NSr0ubwwy5odea5ZY/Z+qfmh5nH7ug8FYauBQAAAIYt6MDIOVu11a3bwn4m42b3MxlnEz3ocB56R6cC6YOIoWsBYEgIOoGeuLNGiqSF8jXl6o2xurPmSZ2M12HpItLGfNO18RDWBZAHdIruGsK6cLnKroFrMDK6L1hWtN4qQD1Sd+yx0NEJAAAAdEcwOpPArqOCjn+q1qOcs7X99CDnHHxHpzhfB4DB6fifZADqmoy1Ciirqgs176Lsvo6jLBD1wlG37tPQ9LhVY5PtR7pIrJKpUTAvrm2FrsfqhHXP2/Z4UfX5yx6W9sl62zpMF/H5BInzRJoG9SsAAAAAoJxp+rwpbGk/am8/bb2WJg2+o1MMXQsAQ0PQCfTEIqpXq6arlhWpG66WVX96FmThZ0ltOnSt6oSdjLVzAHu7pSvWLtN7debrTNroqHWPzbPhbf316rgOz+NSznlSXl2o6dddQ9FjVwAAAKAvJudxyoAO4tyomwbf0Snpg4Sha9EfrXy0xwUSOHMEnUBP2KyLz4zq/e9nl7b2umXqhqt1Q9djOzSI9QPZXQNY99x116rRROshhv3l/ryUDi88l0m7b7Pn3CzTIYbdetvC1yiWkqk5q47OsmBxXjLsTFU42kYFAAAA+iA+k4sju+bA0+lWhC19b9vYTzhqdj9h9glpW+9Zk4be0TkNlX7OAgAYDIJOoCce3B/jVrqKre5kFFbdayU0q3Xdc930MauUhq9+EHtvpWt3uEcIXMvMH9NtHyOQ3VXdTtqNMDWzCkO99e6sebrdqvA1ULZs/9fQtrqBYhc6Ogk7AQAA0AdBaDQquB0G6gk6Hor1aUjZtvbTh6Frh97ROY8ZuhYAhoagE+iJj1+lyUsQGn0iSbJ6VhIghoF0ozQMvZOR4vV8KD2p27ig9KHgJO8hSXThkwAAIABJREFUNIqX66si42W6r3svEC0LXP3pvQJZSfe2eH0/fM2Hsi6IPUYIu20b+4Spdrl+L+JYir3Q7T6xsomUZK/n4rp/Zzin7uQk5AQAAEBf9CHUOaWuv39BS6eDfdpPW6+lSUPv6FQgfRAxdC0ADAlBJ9AziTdU6W1sFSdpsJn3QpI/APunodmYfzYyerVMw9JPlpIJrBaBkZKsY9CrfnDqqmJthqixeRKu5kPWsmkXut5knapFgarPD17LQlAXvl5579dDaHS1tFJo1mHilq5Xf1nR41eSbpfFxxkXBGYuqPT539O17p+5RN0/RAAAAGDwbMfDOuyvrc6+Pu2nD92Qxkif3Caf1WvBt099LKfy14L5s3/87ehrpz6OvvuFZyP7rVdL46q/7FjT/rKuWXIhPNAZBJ1AzxWFnEXyYdqLyCoM0rBUkhRLtxXhmgtO8wGqtA5RX8RWQUGL6Kfec24D6fUo0adjo1ul05+ERs9GRh8spWcj6XZp9Xpk9Z2xkV1YvQgkLdN9v/6YPvf1yOrTsVESWX061uqx74yNlD1/MQ30SbbNF4l0nazrJ8v1MbkO2SS22dBEdrVMkj62khL/rxtbUouVhdF9sm0oWAAAAADtC4L+n4s0Kejwe2fDQOG42U/h54n08UIKx81mEN95lDQOFI4bvJp2bPSdx+ZfS1s+CoxuH+xnT30cpzBPpD/V5Wcl/eKpj6Xv/jKS3piO9JeR9CMXI82tjjrttr1M6ow3B2DICDoBFDrmya4fohZ3J3riLFiMvJAxtqvA1VV/HcXrp7vl+Zp/zifSagzSVaibBZIvElv6+rce/x6a/GDhMVGt4Yeb4g/zWjUUrL9e1wPRLh0LAAAAgNO51PFCsWkovXu1xzGE0u++fLp8ZKS3L5oN7f5iLv3F3Da+n3/2YXoe3uR+5km6n6ZfS5u47QqaNjXl88eaBoA6CDoBDF4+aGwqePQ7N9vq4rwMpEXzuznYPH46XVafCLb/EVwVnu5a3fT4oFcMAAAAnJ4xpnDUnfVoNsxXzc9O8GH8ZSg9xuv6WHaeBAANm9v085imKwBsQ9AJ4Kx0aZjX/LFsOzb/sbZew6k7OluRSPMtq2wLTau6Tf3qTAOJi2MBAABw7t66Mopjsxq5JghNFuIFqzCP+fL58aT9T+BdsEnACeDUXAjZdAWAbQg6AZyVJgPCsqCybHmdTtBjB7O7bu9cOjpPpbRLNDMN03WmWVrsB550dAIAAODcXaw+RfY/Tc4vo1bX44lK7pTSk9tGAgD20EYDA9fO4NwRdAIYtsBIiZUCkwaIiX26XHY9X/C8qhqOsjPSGuvWPka3vRqvaxAdnQ2qGjb3w2T6qt2jAQAAwL7+xm9/9d1/9LNfeffUx9GEv3yw3Iuv5whAAQAAyhF0AhimfLjoAkf/sWPVOtvcdpz5bdV8XXR0NuPaWr089UEAAACgtt/8+a/8+odLfeXUx9GEdy6MPpoRdg5RWQAKAAAwJB250x0AtCwfNtZZZ99aZ5v7HGfR83LrP/JhRyPuDZdOAwAAnJNvvVqu/oCLsvtR9q3i/M3i9Zeb9ysAAACeIugEMDxVwWJ+vV07N/fdZtm6u+4z9/glv+UbcW35MAkAAOBcjUPTy4p+uMjuPVIUdhJ8AsCwhHyuB9TC0LUAhueYw9LuE3YWKXr+PsPe1u0UbcHYSNOaf5C9Nt6cLxt2q+hemadARycAAMB5+YVnI/vh8tRHAWxXFGJehOlyV8vWA4A2hac+AADIEHQCPfErPxHqVXYzxsfIapYtX0TSbXZCHyfSLTduSXUh3Mwfzw7rvjYNsqu60sDt7cv1wz9+kS4zoWRj6f0TNh9Oaoatz3L/Gz3bcT9R7sc6/2Puzx8jLOUenQAAAOflW6+W5kcu+AgE56ks3PSDTwAAgKHir3ygR55NXK0Kl9bXW9lYuvdOiuZWevm4mYrdWynOnTj1NjDdN9TcIRgNjPT6ajxZs6p+UClJF5KuLza/j29P9OQAwpEx8dLafHWP38caROvhOKieLwtO/YDU/UjXCUXp6AQAADgvdHTiHETJ03OZKoScAAAABJ3AoJlQeuaNM/FM0lvTugFO8QAV+fC0yIcPVpNx2m26a5XWHaqHWAeLJtt++ro3p40m46frXEi63HL2+UOXaSj5IJkrybp6+JFLcSATJum2XKiZr12ySKwmgSmtp+R/G910USgaJZtBaFeG0AUAAADQL/nRapxdAlAAAIAhIegEesIeMXhxQ57m502Nwffz4WmR6o7T7rsKZR9cp2QoKd5cVjS98ZyK7dbZvws561gurRSe7v1eZF2wC68b1oWbrqaPdfNnwr+i2g2v64LQZ/F81xF2AQAAcCIMXYtzVxSAEn4CQP+18bFe3Lm2CWA3/JUP9MSjpEuvHiIfmrr5Y4apTSgLYqtC2roBru9+IbN6Tvae+CFm0XSdEHRbELqzDt0V3nVwLkqG/y27avkUxsE64HQfHPABAgAAwHn7hWcj+zFD16JnunQeBWB4Rt28Zh3AABF0Aj2RxNJ9Nn1fsk5wguDLD1/3rXVVBbH7PlYk3/Fa9zlSdQh6dB0Jpl3I6U/na5e4DwuqPjQg+AQAADgv33q1ND9GRyc6bB5Lc84zAAAAdsZf+UAPTa3V3Jgn00lF8OXWO3bNh69VNQjTcFN6Wo9hvLSKRmZV82FqnWD1UdLlHgFi17thm+QHmfmhbLsWcgIAAKCf6OjEObiNTn0EAAAA54egE+ghF2zmp+s859g1ryyEnVqrebx7Y+MuwWuUG1PjPk7DVVfHS6vHkVGSzeertF6nDYd0t3ZNWSdnFzs662CIKAAAgPNCRycAAADQT/yVD/TEvIWsaHqEfK8shK0byJZtr1aN9WR6vEyD0CTeXJ7Em+GrW29ujAJtdse23Q2bVzUk8bjOm9iCsk7Ocww5AQAAcH7o6AQAAAD6iaAT6IlkWRwYBSOjZGn3qpeuw3FpdS2tpv11umTXIHZuJZUErPnHbmU0zabzQwC30Q177E7YLpon0jQ4XQUAAAAAAAAAnBeCTqAnopE0Xj6tLgDdp95LUjb/cpR2NRatu4+iYNVt0w9Qt81fekPJ1u1q9bcxtyoNb/PbO6Rr9tBu2H07YaOlpIqOz66YJ+1VF2rmK8EoAAAAAABAPR3rfwAwYASdQE/Ey2zU1Xzdw4WeBqbjbJinskB111oWsOan8/MukHT8ALYufxtFXan5x/wA1O9y3aWj1Q9U23KMoYbb1Fb3pgs9i+wanAIAAOB88IHsU/cPVknM7ST2FYRG11f8YAEAAJwSQScwMBeSZlumZ1J5YFoWqO5a97W0G0HsviHrroGrm/a7XPfpaD2kC1bSTsMG31qr5wftrV1tdXU6hwamAAAAwLn77odLvbwj6NzX8xujn/7J8akPAwAAYNAIOoEeWUTSZLyu06XVfGQ2lst7/LZkuav+NttQJ4RdTfuB6T4ha0Hg6rZ9SD1Wx2vdTtgqnG5XO1ZgCgAAAJyz4AxudQEAAACUIegEemIRPa0LGSkXXhatl6/+ev50Xfn91a11QtjbgjD2EE86WQ+tx+p43bMT9sKbnkm6rvGcpkRcGA4AAAB0WhwaBQF/uO8rDhm2FgAA4NSCUx8AgOblw0wnHxT6XZz55+bXL5su2t8utTQELVlvny9/W7c7HuM066SsO+8v29XFDtOuzryvrqAzEgAAAOgm7lt6GN4/AACA06OjE+iReytdm/LqPAZGl4nVi0W6PD9fpiwA3bXjs0pZwCgVh5755XWmDwle812yZfP+68kv22afDtd8p6sbtvi+/m4bUXcY2NtYmu74IcE+99Y8lnnC0MAAAADoh4BL4HvpYZHoX/2bReP7+e/fm+r3v5/o//yjI34wUOA3f+1Cf++fNntJ79/9/Fg/90OB/sE35o3u5zd/7UJf+5cL++cfJ41F5T/1dmC/8ssT0/R7xve/m549C/Szn+NTCwDDQdAJ9MQHs7Rr8IWk68DInc7E2UnrraQwu/fK1SLRfTbEzr2VFNtVIHa/Y/NhVbBat7qgdZuyILZOALtvGFvWDVs173eb5pfVmd4liL230n32zb59TGu8kO4TSbL66Ymk6Q4v+FQSaddTiXm8vU7D8g7SfYJSV6eBREMqAAAA+iAIaEsEgLa0EXS3ha52AF1B0An0ROzdwPE23h4aBsbKeFfuXhec3IYFV/aGoXQVWz34QemB1Q9et9kliD1GCJvvhpWeBrP5+aJQNR+6rl6/d4Ht7aMU59Kz+1wAHOdu1BkYqSgj5rOK1LzixqZlAenq8S1dqFwbCQAAcF74QBYAAADoH4JOYKASK2nHcHTT5vqB0UZwmlcUpDoLmdL2uHzYOo0Szcf1xla63bG+SNzxFNc1u7oHZhpElr93+WByH1WhZVkjbI0GWZQoC0enYbvHAQAAADQtGEkBf+fuLeBTNQAAgJPjTzIAR5EPTvN2D1ILdhAYSUaa2/V8YhWERkm8rq22M7r9u8OJ1/NH3AU6YB5LsZVc8/GHyfTVaY8IAAAASI3N7reCAAAAAPqAoBPAeciHl24+yEJFr7Z9XC6ITKw2Qs6yYWVxvmqOsAwAAACcDcPfuL1VNerSOe6rrdfTxn6mI2Oa3M90lP7L7tN71qfX0vT3HwCGhqAT6Anuydgd/veC70s/XVurl6c+CAAAAOAIuPc89mVLbkFzjmwiLQ4diaoj2vq+8P0HAHQFQScAoBFjI01rXqH4zlSaV5wkFT1Wdi/NNtxz2TsAAAB6ImbYkr0d8/am01B692r3512G0u+e6CrMPnWkmUCa9OTfggmk+dJaSY2+IL7/AICuIOgEgB5KrBQe86x7T5OaLa3jIP0q86zm/qJcIOoHpBvTB4akdHQCAACgD4LQaLSki2lfwQmCkctQeozX9fGEF4D2raOvL2yyHrq26f30RZ9eCwAMEUEnAPRQYKT4hCe8p5IPS/35srDUhaNFoWhZIEpHJwAAAPqgTx1Zp3CK988Fm6cMOJ0+/fzwWrq7nzb06bW0acxHIwA6gqATAHqoKx2dziKxmgSmtJ6SC0OrQtEo8cLP5LTD5gIAAADHFPDJUGdEJc21XQ0T+tQFx2vp7n7a0KfXIklBhz4PAoA2DOrP2S988+uv/+tf/PKnpz4OAGhaYLrxh/oisU+mi2qUpGfu4yANFatqm/x9Psv+x3Qh6LN4XndEXQAAAHTAqKNh0SkFgcQ1fPsLWjo/6WoA2qcuOF5Ld/fThj69FgAYokEFnfd3sze+8M2vi7ATQN91raOzqpvTD0PdMLLbapvy+247bAUAAAAadeIRVs5Wcvp7m5YFoG3pwsW1OB2+/wCArhhM0PmFb3799a9/9u+/+xt/8DWdU9j5hW9+/fX/8cf+m/9CkpLR+I+DZfQz7rFt80OVjMZ//KvvmG+c+jiqxIlkvZMiExiFB4Yn730m0KtFOj230sMy65aLpNvler+vlqc/GcPhno2MJuP1/I9O1tNvX6QfVJhQsrH0/om/5X6YWVbPySnCVgAAAKAJlzKKzfn9Td4JodGljhsSz7z22oswnfdr19AFN2x8/wEAXTGYoPP+bvaGJP2jn/3Ku7/xB1+TpCdB5299ZN9zQeGzyfiHH+L4i1dh+C/8mpjwJ+vs7zLQZ8seC3f8Ozi26+fFwXjzQS/pyD+exFZBaFbVX9bU9Db+arE9bL50H5K++cnhJ2qhkW4fE7scB3+5bd2/Zh4++iAxrz5Zbh7gLJE+jdLpaGn1gwerxaLo2NJlk4nR9GK/E6WrUFaT9CzrmaS3pubJ4w+xjNyJWCjZhXTvnUj5AanzYiHFubGMCEz3ZwLpjenT7/Ebk835C0nXuZ+FH77a/Wfj9uH03yc/zNzW0XkuxoH0eOqDAAAAAA702mtG1zEdnftqYhQdF2y60DNfuxR80tE3bHz/AQBdMZigU5KCZfQzyWj8x9LT+3X+9veXvx0YvadxGhTOrBQEoWZW7/l1nKzDwzgwGpungWISW6niqr54z8/0tz0v//h4ZBTbdXXL3Lr7TktSWDBdJ4h06/jruml/edG26gbEde4vMgmlxZYVYytdXwRG0mfdMfnH4Y5xYaVPdPXZq0C6GqVBazGjf/4Ya1Gxz8XC7h10piHm2jrY3Jz2500oPfMOuCgg/Ynr9fqb2yk+ThtvhqdFXhwwvo7foXqIty/3f+7VyOhyy2/Ptyc6enoX7xcud+pTiz50dEp0dQIAACD1N/a4a/ufvDr+cexrPDIaD+qToe6bFZxP57s7y9ZrGx19w8b3HwDQFYP5c/ZPvvTV9/Xd/6708dncvHflBR9jU3yvgzjIQsPsHhZRNh1baRwaRV7d17abyUd2vU7V9OqYG5guU7WOH1QWhZllIWh+/bJlu3TKbgs5V8fkHZu2TOefU+TtK+nFvHqfy6XVaHR4NuUHm/kQND9fd1t1npcPT4s8mxz++oqC3F1rne0XVslcqbwe/OJqigOZMEn356bzta1jAQAAAIboItx+Dg8cqizc9IPPIqFp74ezjX219Xr6tB9eC/tp0q6jFgJAUwYTdErpvRuldPjaL3/7H75wy7/6+9F7V5eblyHtE1S65xx6M/g6z/fXcdMunPVPsvz18uFt2XxR9bdXFKzW4ULBoqCwrO7zXKk8BN01EM1vY99g1Xk+MtqWgyWx9vqXaVu+mtOceJicoiB31+rLh59bt6Hq2or0e26eTEvmKkmD1+Vyzx/WE5gn0jTobgUAAACK5M+rtym7sBqIkvQ2GXV1oasTAADg1AYVdJb5zGv60q4jQo6MtLTVtW35oDV/4lQ2706yyp5ftZ1tJ2f+CdyhQWtV6JpfFtvNbc+S9bq7Dh1cN4wt42dMbz8LpI+qx7xcLqXJdLdjlNq5X6A/2mvbweou9glh8+GnjdPt7FuPqWzo4appF7iORqbFHtP9zZPuVBdq5ivBJwAAALbxz3+3rQMUKbtNxi4BaFtiyw8zAAA4vUEFnb/6jvnGb31k3/uNP/ja+9c3F6vln7s0f2fX2625YHRbbYMfqu4TtjZ5klUViu4TtBYda9GJZFVHapF9hguu293qB6JjI702Mrqt+AGJt9zjtUziBY9BKI2XVlGNHwJ/vUutA9Oi5W2EqfvI327zWCGs286+tcyuQWjV0MP56Xzgek4dnV3o2pwn69CzSD4YBQAAAIrs2uUJbFMUgJ46/OzD0JsA9nfiwd4AYGVQQackffnb//DfSHrDX/b4YN4Lr4rXz+dEJk5kw+BJPaV8ZnbMsNWFpvn5Y3auHhK0FoWgdTtTdz2G/L7KulW3DUP09qV0e1e9r0Pv05nE0tyY6huGZvz17mssP8TUpuFpEqdhbJltgWt+eVcDWGl9zH618Xq6CXUD167pQjenb1sgCgAAgPNyqsAxH3oSfOJYyro/20JHJwAA6ILBBZ3/+he//OkXvvl1/etf/PKnUnp/zvBqHVTmg70nYWEQpMNA5mtOfjv7OMXwt3nbQtSmOlf996/uMMFl000p6zTdFppeXAXSXXUCtc99Oufefqcd+Nkp4oenScVbUCdwdcurAtNtXOBat/s1ryyE9efdsnzNTzehqSC1rnMfkqtuIAoAAIBhm8XSRc3zknP/GxnNmcfSvIPD01ahoxMAAHTB4IJOSauQU0rvz+mmq0K7XYOzYwSA+24jHwQWLdulnqKLtShsrjNMcGVIfQTH6GR9q0YKue99Op15x0+ejxnEVgWmdZ9ft/s1r6rr9V6HhbD7yne6jts/BAAAAGAw/JBzl8ATKHIbnfoIAAAAzs8gg07f5y7N33moGY4ly0RJFvAFcaJgFFSGhGWa7jSsCgC3VRMnUpC+rnGSKAqC2l2svkND1rJw9dRDBR+jk/UikK4Do/vkuPfpTLKDCrrQCrxFl4PYLoWw+ygKba2VTnmh7bb7YgIAAADnbBY/nb8ICT4BAP0Wdv8jSAADMfhfR//k39n3byb6bNU6QbwOOIvmu6IoXCybHpk0uHVh7aVNQ81TDgV7qF3C1apttOFbP0j0/bvq8S8vr8xO9+n8kdAqXm4uCysuZRgvyx9ryjmEsF1WJ4Sd2/V6bvqj4DTv+zSQPjOul2r/yGXx79TpDh8MbbunZp1a1zyRxouHb37u7eu/Vf9ZAAAAOJX/5wf2q7H0lWNv969fb4aaLvSsCjj/8PbYRyH92b3VqxNcaIn9/NLz5s/RvveY6K9fGH3uDc7DgSH6n99vvsugjd9lkvQ7LzvcMdGw18fS56+bf58Xib72t98yX218R0BDBt/R+daFfl2S/uJh+aUPX5ovvhHoi5L02uv+J95G17K6yC5TicJAodLAMwoDjQtqEgatB6LbhnAN4kQaBetlYSDXVPho1l2a+ecGcaJRFojW6WStCllduCptTo+TRI8m0KVN6z4B6y4drHW2sa9XD5sB5g9m641+GhtFC6tXJd2cYWhWQ9buEnJKWoWcF5JmruaW+dOx0iA0XrZX/Te4ayGs1P0gtm4nrL9eENnu3rS1hvkOH9a4dcvqar2Ce226kLNuIDoNJG7VCQAAAGkdbvohp9/lSXcnTuV/+YPITkfNju/zn/38WB/cJvb/fj9udD9f/qWxvv47zY7t+x++G9offy0w/9vvNbufL//SWP/kj5Z6/9Pmwpt3Xzf6zz8/avw94/u/uza+/88ujcLn/KcDYDgGH3T+6jvmG9nkN/7xt6Ov/d6fJV+UJH0/VhKn99hz1XHz07HRaOo+6k705qXRz/xwGoRGsdW4IuQcx4k+Whrdfrr+qHwyli6uAgULq2Ritlafu+9jnG3bBa0PMvrBbbqPNOCKdVHjfQlGRm/cZIFkFoi64DbZEiJWBq7e8/3ppUn39WiCjeflOzDdMbjlQfY+StJ1YnUfmFUtWuZqtEj0+Lj9fXix2JwfzRMts0Tk5X2iebTbHyXPXqsOvk2we8CZN8vVqmkXjrZRNwJYpWGmfywbWg5g80HshaSoY0HsPiHsXFaXDRzLuSoLTZ8EoXHaSZoPRPOV+58CAACgSH7oWj8IJexEmz64tWbrvYcOkMRSvLD6+Naaf/tJ85eCNrmPJJb+vTcD86MXttH9OO9/2vR+0s+fmn7P+P7vp+nv/+WV0c+0EHT+0f1wOy0BdMvgg07ft/40/o+NN7i4CzeD3P8Lbn4e2Y2g6/VJ+kAUW13JKpJZT2fh52o6DPTt78b68GX+k/f67Uvu/n/54/NNx0Yffbz7+DXPngWFIa/br1tWNr2Lqn2UbbNov9vqvvz7LAahlGTjAe2zzTiWwornJXvcm9O3iNLAvG5tUz6ALQ051W4A66rf9TqTpDPuho1GWRjLb/i9besk3WVIXQAAAAxPWZdn/n6ewDk75LOWrunTa2lLn96zPr0Wqb0Lsz9ttvkVAGrjY3CPCc2vHPL8ty/TkFOSHmSkLdMvHg+7cqep/4RNIP3QhezHkUzRPvxlZdO7qNpH2TaL9rut7qss6N5LYivv1J0kUpxI+4x4vIj2q3XtEqAeWk+hLHg9dTfseJkGq/khifMdsn4Ie5F1y14spfuK14z9zWPpw2T66tTHAQAAgPoqTsUa4wecbvoUxwE0ISG4HzS+/wCArmjvBpId99Xfj947dBtVXVZ5s0Ra1L3hXstsIt2p2Xs4DFVcI9u2JffvrOMxG7J3MTa15utOPwZm7yB1n7rrV1vHdQplnbBVHbL+suvmDg0AAADAFn4HJ92c6Ju+dcFhN3z/AQBdQdCZ+V+//b//R4du4/lV/bfz7sBuzl2YYPcvNCNabA8xXSffrv5qYfVilujFQrq/t3qxSPd3b9f1MgtRJ5FdhZ51pi+98NUtK6plj9UNXvcNYrscwrZ1TJ/GRh8/avX14X369WrZzQsqzt215X0FAACAFLV3ag90Dh19w8b3HwDQFQxdm/mVyX9y0LC1l5dp6DJLpIsaQeHdop2GycfZfh/GP78OCu4fikPVyUaWS6vpHvfpTLIT7Ht5O0m0utekJL3YPBoFwXp6ttqn1Uxu+FyrfCNjGNp0ONR4swZKA9HCsDOunr/M7nXrB7H5xyqns/2WVbft/GOLsVkFurvMS9ppuiicfAyM7rPg+36hVY1j73u2zLqAs8e1kO4Tu9GyaRN3cULxD1d0wn/GYyNNa1448VpuyOJ5yQdG2+6d2ZZ7mt4BAACQcWHnmIuGMTB09A0b338AQFcQdGaszK8EB3xu/do00Cw7uZnVuKLz41dJK4PDJnsOg/ryvp3jG6I4lsKKPwaTFq8I9vd1nw/Kyo6jpOM0CGwWpFpdyugxV2dGui74mYoT6dNQurLSg9msLmgNQyleWCWhe/+sbt37GBcHr35NX1/BY4ssyM3m40ebZYjZ8mXZ/Hr63kqKvPcuNx3HZf8Gj9MRuK0Dexyuc9JTmNT8xfos97/Rsx33k7+SPh+U+vPHCEuvrdXLwzcDAACAHvH/Jj2X0PP+wSopPWfBNkFodH3Fhyfjnny62JfX0ba+vG99eR0AMESD/RX+299f/vb/9V2ZH35u/8WHL80XX+U6Hx/ud0ubJl430kWw7uwsqpIU7bj9fe3b0YnjubgwCr1hVt+6CpTEVuOJURhK33359GfB3b/VWskYaTLt/olTUWiar7elT14/lq+SNjocCytKnbKjs035D5Ly82XBqf9hlAtD64SidHQCAACcnzabj6KkPOzsUhPUdz9c6uUdnxvs6/mN0U//5Hj7igAAAGjMIIPO3/rIvvdXc/veT70lSfri51+3mr2VPwN5eupRdF/Nh9goWli9/fr6+fnOznyNE2lyE2iy43Hf37XX6je+NFr06GRnMjIaX9YPJt7acr/Vm4nVuGBz40mg61r/qtZP/vD2aRfnfL5+74PgPIJOdNOpOzolaZFYTQKzql3if/jkpotC0SjZDEK7MoQuALThv/7D/Vt9rgKrX3qJpIciAAAgAElEQVSzSx/pH9dloC/96jvmG6c+DgDddC4dngw/CaQiLqYeNL7/AHC+Bhl0/sXD8ksXJv1LPgyk+8CUD9PpuSq42dyVJGXDlMS5bYRlV28G0s/96B5nEjs+5/5Von+1Z1j5uTeMouenP9sZG0njQBdHOBT/3ql176Va7nhhzetjoxfz8u9Tm0PZAk1w4aar+eBzkVgd899UE8bB0yD0WTzfdYRdABisKLYah6Y3FQB21fR9PMNA0h4X48WhURD05yLntsX8n9ArDF06bHz/AeB8DfJX+Ocuzd/5d9mN9vLh5DE1ue1twkB6cUAb18VFoIvjHU6jtg0V7Kp/79SLpdVs1I0TkvHESBVBpyQtl1ajjhwvzsuph65dlNwn2C13NX+PzTaNg/XQYvkKADgOFw72pRJ4AtjXKf/uLcJp5mF4//qlzY6+ScOfyDa9/T7q0/cfAIZmkL9W//xW76nkFgpFw5GeoziRFtF+z31+Yw4Kacs6WZuybajgWcFr2TXk9O+vWrT8kPr6pdH376r3n8Qa6L9WHKoLQ9c6+S7Orgxn6z5sKqtFCEEBYNgIOQH45olUMADU2QjO+NiBY6Kjb9j4/gPA+Rrcr/Cv/n70XtUn1NEJR2w5dsh6Oz/NpaKn7mTdVZykz3O1qNuzKOT0lx9S6wzLu1xKk2mNFwN0kAs0812cm5UPjAGgjy75/Q6gQ5q4RmGfkLNr10oEJ77wEAAAADjE4ILOz7ymL536GMocO2SNZ1bB6W+z2ap9QlY/5IyT+vdszW/jEJOJ0WJR/gMQxwRB2M+dNTr1Bdou0Czr6DxHXRtyDAC66lHn+XseAHbhws5z7+zEaU1D6d2r/Z77p1tGiQIAAOizwQWdH740X/zM81MfRTtme4wtn8TSG6+HrXa2nnq4YBeOHtKJemgX6/Op0fcrgk6J+3RiPzfG6v7UB5Ep6+gEAPQXHZ0A+s4FnG5ApXMLO4ORBneB9DEFJ/pU7TKUHuN1BQAAGLLBBZ2fezN895Nlcx+ud+mEpqpDsEwQSvcthw+nGi741AGrb1zjYLhPJ/YRndFJb/5K+KYrAKB5dHQC6LuP5qc+AvRJZOt9VuHCTUJOAACAAcYmf/Jy+eu//Lb9mX+7MD/y4Uvzxeup+aIkTSdGZofgq+zD8xPdFnN1TM6rPULOoTlFwDpPpCgL2uM4/XoVW0UlBxMEaQgahNrazfm33gl0P7OaZfMvFuvHPo2sEoba7K03puufjTcm0oWkYGT01lSK5lYfnO7QavOvgD92db8b85WgFACa94z7vgFA5+3yWQiaV/ZZxT4Xawctnre0sa+2Xk+f9sNrGeZ+4q7dDBoAGja4oPOrPzf+hqRvSNI//+7iv/pn/5++WLTexcXT/xBenxT/JzGeGM0qhnoZj83B93AsUhWyLhblz9vmratg58C2Tx/6z2JpGZenoIuFlBQ8HsfSba5b2CbSbFa8rTCUJtPqPzxGI7N1Heftiezb3s9oHPhjxa0n55H04F316YejkrSIpPvcIS+i9jt9h+SNgu/x27l7szwfGV16869d1fu5uI2lc2mmaap7s+r32aFBKgCg2iv+fgCAzhuf+gBQS1EA2qWRqgAAAE5lcEGn7//4/eQ/HZUkdEXh1GxmFYZpoLVRb493TJOpUXjg/TFeGxm9eNz/U/gfPCRK4t3+Wr6T9CDp6ojVeZA0nx/2IVkcWS2igzZxdHGNIWbqrOM8xLmbYP3/7L1rzCTXfeb3nFNdfXtv83JmSA5H1JASKdHetWyBWkSGbXFowwiE7CIb7MJ2/ClBgA0Sby7QOmvCBlZjLBYghM1igcTZ7McAQT7k02K9SJAYu5KcQFGwVFYyY4WyJIo0PSRnhpx55730rbrq5EP1eft0dVV1VXXd+/kBjf+p6jp17+qq89TzPzF1h8Y5NjxMeqzXpxvFCOofRwi8JmGiap057m6eZtgRGERcWYcWMLSSyY5WRwh3rpQZze+D30XFJMuqC0W5OoPkJaASQgjZzL4UYNdvhBBSb+j8ycaGpEulUFVXQIQQQgghdWKnhc6nn+pYH52ka63WwlMw5sVsS0EP8PtCcraYz8WZh/Eo2x37Sc6x7SgPEDFuWNdVCBMYcyVwDg8tKC2abioPY1ouh3s5rXdOraNDQI0AYcaoaVfdsIHV8bbzR7oSwvKgNsa5UoAvaLoSwvUU9PcAgMU4M2IxLRZ1Z3TRhFKUgEoIIWQdOjoJIaTeSEugM+e1OisyZ5F4Eng+71v+ODMSQgghhJBVdlbofOnrd547eey9AtTjLby82ca82G3jDqkpnqtgbei7aj5XG/vnzBPTGZqkXDhbCLFmeQQIc/woTkGOf4FhWS/LQ6a3mEdM1I5mLcgGxdUogTQ4blGvsh803y4mhBDCPjoJIaTexL14SzZT1P7TwqYWPk2x0/yewichhBBCyA4Lnb9x7feeO32sYKEx3delwvNU5k6tFR90aoXnItEvdeT6qVHzjnUjrRAbWrbgi5nbRkMQzSrAmuWh5btNTRF2TZCNEkgdaOF1Oa4mbEoDSwghhBBCCKkOubMtQ/UlytkZLAenI4QQQgjZRXb2dvaZQ7x6+rjqtSC7juttNgW6KdJkjtxiYp7UQoR1c4oGeQiw2zhlTeHUZD5XQIV97hSZDjZNn5p5M/UAO//ZEkIIIYQQUipSbkpoQ8JwPcCSyPyCeVpMQdMsm6InIYRomKyPELJr7KzQ+b0fuL/YG7bXSuRl7EOurJt0smRTP51eGf10ZmAAYJxgOBjrKsI23Q1bajrhAphmOJ66TlzsWdEiahqhNBh7UptrCSGEEEIIaThMM54aa5EbbJDzY5izeMiwE7bNbBI5n78i0S245c/qChwNhfrsteJPpM9eK7bR6mgolNUVoujlAMBzh8UuQ8+/6G3h8c9G0ce/N5DYt4BzvghBCNkRdvZu9iv/56yNGWsBAK4LjC+yNcF3OgL94c6eFpVgdQSsDaLYQYIboM8kfHiRNUxHmwdJhdfgcHB8HdlGbHWmCn9hVfMGQ08Cz9jJLrVPD+rzlkUv4W/Eno3eePHa3l8pdm0IIaR6/os/dRUADKXCyPPvEzeVdXyyA/zclfpc4/OmK/Hqr14X36h6PQghm/m/P1Z3uhJfrXo9iuL7FwqPnfT1Pn7o4Xze2uaRwtnvCFx9Iv3/3M9fWW936VnAYcxzfZz4+X7IQ+2HYw+f7gu8eMw2HkKS8t2TqtcgP07Hnprb8s+rXg+yPR3H+yQAzG3557qc9/Dcln8ulfuTorcFADzX+59/5Ub3n5SxLLI77ISj848eqNtmA8SdN53bp495I0/qyZ4UuPBWz8/5XKGzIe/ENOkpPV8We8KvlyYWyTYibPC5LulwFSJnWhF2G9cr06umJ8pdmlQAJYSQtqLFTLN8xVU4wfp4c1pCCCH15fBQYM/lNTsrm15azhMn4n32OAH0H357VszKGPz2F7t466GHf/Fn880Tb8E//XIP//H/Oi10GX/1Mx289IQsfL/90y/38I/+tYMffFRcnqDPXpP4yl+xC99nbTv+R1fa8+Bv96SAwq2q14PkQFfCFoAN3HK6/kU/z2HLU7CUuuVK65b+zlH5R83Qsr5Z2r4jO0Prhc47bzq39yS+/q2PFcYe3gWABxPgYuC3ZD+cikVqUJ9HU4VZYsWonniuypyC1mr9GVEN1/bkygPQcRcYGsJlT/gi33HfH/7f31s9B2dTwHEUlAe4rkKvJ9DNQXXUp3raWBg1FmGB/NywdRZhSTRTF3CNLk/veb3zateIEELKJ+jYPLHCnZymyzNJH0HP9uNdLGVzOgfem1S9FoQQUjx2R8Cu0fWXpCdKAAXK7Z6ojGWVtT1tWg63JR12C977sITfdgG0Y3t2HfN4AkBfFjAsBCwBSGO8PnfyjoQURetvZ585xKv6jYGOwC3LU/hED0BPwk2RPv6jmEy3J6P4N7AezwWcFJly752y57e8eSpF7vugCBnG1WH0d0/0t7/zOuwonBrpg1xXAYbLbD4Hur3VOlOsnmO9BJmpp1DoQazFOlC1CKsF1JVyjYXYKBF2KupxPNuCxd1JCNlRtHA58sSKi9Mcf+AonNnpHZ0dAdwcFLLamTmwgQ+mALM5EkIIKYupC0wlMPX8bkAIIeXTlmf+tmwH8QkezzyHzRf6Sztv+IxFCqD1QueLA/Els4Fibjb6J/xRdQRwrRv9S7/WzTmtwTPbze+Ntx388GG2K8aNYwuffaped9Q3yrDq5Yib8WJt/pnYXRHbsua6CmFd7NpzwOn4cdpRcOe+Szcq6umCsQzSCKpVCLKmcBomolYtxK4RkSVmMC/J7rpj7CmFFnXdQQghGwm6OE2BU5fP7HBH5ybmCvju43q95esoipyEEELK53TRz2pUdxqEkOKxAMwU0BWMjPWMbsy5GvZd3PRWSF1CmkjrhU5PWM/rsnA9KEtirgDb88tJaFojx4dbpMV/8VDgSkQrU5K0Y0XgNGz/Z8Xczhs94ONR/PTBfjvd+eIPyYh6fFQMTm/WC0OLo04OcTRXsSKsKcais3SsBmMatEgaVU4S9bLr6oQN4tWp1bhFXNApSwjZMbRwecVVOLHEWjnM8TmU4S9mhTHxAGaKJYQQQgghVaKtJ9rv0hXAZK5wOtqRxklCcqbfBw67qxoMBVVSBK0WOl9+4/WjucItLVR2pMTc8wU7R8rW2qS9OSBTpOVNStME36IoQ/C9OgTwKH4az0Xpv+A4cTRt1PPbFMPE1z78xlAdgWX/sqZAagqrruFaNYXWoJvVnYe7XO05MEJ4nTo5YcME2aqwBXCQ8AdzvRf//TQio3fY+DLefqajkxCySwyNzlpmNgDPH7cUM1cdn3oaQgghhBBC2kDQbEAI2cx8rmB5Ivnbr4RsQauFzq/d/MpvTsy0tWo1No2k/6fjSfYNnO5Amq5t70vK2D9dW0JKD15MI6Eb8t05BPahNsamMwlEYCmO6nKUsOpYgAwZ1tNpN2mwvmMB0t3shA2Kr+7cH5eHCzZpSmJvri6nl2hG6lp7g8E+6vv9BPN2jN/KNKqcUhylo5MQsmuYQqYpfJrfEUIIIYQQ0laePKxXV19RRPW/yOFmD6cpJx0XVz8P3n9I7yYpj1YLnftd+6mLhfjguApDKDiWhO16cBKmrU1Czj10RpJEYJuMt3t9/vhA1l7obIJQmQdHtsCjmI4d544CBsudMXOALhRm2BxntkDXUZExLbpe104utlYluko3ftgUTOOmiyJMfJ0Aubhg06Ykdixgj/cUKyKpWQ4TScNE0cto7Es6Ogkhu06c4EnhkxBCCCGEkGoIilQcbs9wmnLScXFlQppEq4XOn4zwH9jW8tc5gv9KgoMcX00A4ASGbUvAcVUhUYu0UfFkSxVvG02kToJvGzgaAI+m8dO4HqA1+1NDoLOMgyEtgYHni5gAfEEyRuQ0o55+Zvui5MxZFUn1uMvpHZVYbA2KrkUSJsBqzOGochMwj82FsdoDqYANaWGLZuYpdKWIjXXJYhEmipqCqHMpfgp8wpsmMZQSQkgrSOPmpNhJCCGEEEKaiBXTNBH3HSEkmuBvh2IqKYJWC522pW5VsVxn8WstIjoBsTYYx+N81j1T3ZBxVYi9m2LR5CH4StfDsCexSXr2XAVr0R/ruWccO9PY6yi/u8+Z/73QKTcdhT0BYK4WjkMFx/Knc4zpXUsALvAYAlis0lgGxgEYLMTKsfSF1fFivbTI6rq+ABuMWuQMCqtpy1rENMVaLXCGCbB6mV1H4VxPj2V5314XdosQZLu2L1LqfWfiBX6ProuVlMXjoBA7W1+/p2sgciaJTkIzui39aeNikej52xI4LXZRhBBSO0wBMyhmRpUJIYQQQgghhBBCiqS1QucfPVC378ek/dT0m5FePTEXW7Qp7e3n/2pSFWLvplg0eTh8XUtiDwpSIrafzvkcsO1066eU4WYM7o6wZTkKnofLdZESGEBgIoC+AiaL0+YE/rAlF0LpQoRzgEsbrgNf3PQW+q3nGvtrZkwfU5aW8MVUxxdTV0RXLEXYsRQYGzqxKcDq6BmirrkeDoCL2eowZgquJeC5/jo4M7XinA2iRcmpBfTc1bgiYM7MWuv7ehvizp0y2eTm7Mrk1x4tiG6KhBBC8iVM4ExSBrbvdqDO8G+HEEIIIYQQQgipltYKnT8ZzV/dM1SIvgQm3np50rLWiZkDyLJyyJJEZBV1AWBfCpx60eKsL5gV33qoBTcdx1CAAsYATFPhGNjc4rftb27hqnyEwMJj0NMGY9ZlX8Yk2+Iu9osR4wju623IYx55kMzVWf9WcO0a1eUtDfSEENIogkJm2PhjpfAoZBpCCCGEVINM8VJpE5ZV1va0aTncFkIIIbtAa4XOFwfiS+9NlsMXXng5CSVkO82N0+l2KlLbhN+01M3ha3dFbKeknrfaTychdWRz/5zNwHSL0jlKCNlFgqlpR57AsVKAtXgJSTB9LSGEEEIIIVXwwePVhoq9LtThgOowIWQ3aK3Q+fYpbtvdfOblNqhB25tnr3s8kLHb2hQxLcq9m6RcN6H3oKvw8Sh+GuUpgPctpMZscnQSQghpBkFH51AqTEPG6+8IIYQQQgjZdd772MX9CwVnZtwrDwSePJJ4et9vbP32j4IdYUXzxReWfVidjj31wweeMOdtIAAXN48lnr0anf7vdOypdx56YjRen8eTBxJPPSGxZ/vtjn9yd7Xh+XM3l9LChaPw4/uracyu70vcOApvUN60zXZX4MXrUn3/rrvS6HllKPDSM6uSRnBedlfg5U+uTvPBYw/vP/YQ3Fd2V+CZo+V6Buf13NPW5XECgA/PPbzz4ep2msdEc+EovPnuekP9cCBW9pvm7Xsu7p+tN0wH5/3BYw/vPlgu/9Z1K3QfR20v4G/zcU+sHNvv/Pk8dNq4dSGkTrRS6Hz5jdeP9nrWuxeeulX1uhSNHdC3pk5xjUp1FHwtub5eUe7dJOWkyyyDiQuMXIFNKVYV2xFJDNMGdYw29YCeXI+EEELqgRYvuw5wYokVwVNDJychhBBCCCHRQhcAjMYK74xdvAM3s3i0ENw2NvrcfeTh/oVaE/6AS3Etch73zzw8mi7rhomhmrBtPXwye5uUM1PwHamrouLJSOF07F26VYPiKwA8ubdcbtxx0MvRwmGYYKjc+OEo7j0Mb3CO2ofnERn93r7n4lNPJe+n7sJReOsDN1a0dGYK92cK1w6Fgi2EHkdIk2ml0PmdL7z2+Dfw2nOAL3q+fut3Pv/t9907XYFXpLW80FmW/zHpWAL9BvVxaeqazmx7JbJAnbQQnMWfS1DwLZIsgq+jfOHSZO4quMa4yeKP7txT8OKctZZAtwd0GiRgkerozRXmvarXYjNm1m0tbuoYJYBWHQkhZBcZeQKwFYbGi1g9d5m29oqrMOM1khCyQ1zPcK/9YJr/ehBCCKkXceKaxu5mb9sLugrjcGYK733srjg7oxyEQZ6JcGSavPfx+rrcum5dugW34advWmuuzh8+8MTLn5S4cFSocGhuZ5LjAISLnNsQt2+Dx2LTfNIInUm3FwCY2pi0iVYKnSbf+cJrj38Vr33jzh9Pv/SwJ7HJHReH3Yv/7R8lvHiHCayJ1yFG3Jq4AmKLy1N/0Nxrm6OAUca0vY6zKjjGcRbTXyYATCf5KMUi5r9VKYVOhy2IpJ1EdTOsx9chUuQkhOwyx8pPVwssnZ2wlsLnjNmMCCE7xhMZusyh0EkIIe3mw/Pwxo0rQ4GT0bLt8MXrUgFI3SD79r3whswrQ4GjPRmasvTuI+9SXLtwVKQQF1zHTQLghaNw99HqvK4MxdbC4XDRTn04kOLK0FtZJy3c3r9Yb4f96ZvW5T596/3wxmItMOt9dGUYfQjmG9qCw7jY4CS6f6Hw7NXk80vq6gxzt2quDAUu5sttfvKAjVukXbRe6ASAl75+57lxb/s3FJxp/EXqow3fF42zpR3zg4cu7lnNFTt3hTi3JyFtog6uzbAYJcYSQsguMDWer2c2MIRCz10d33UoeBJCiMnAAsbuMhJSBnxBk5DqCEtv+jO3OpcOxwtH4d5D79JRZ6av/ZO78xWXYlifjo9C2qDN+d84kqF9YF44Cnu2WOtLE/CFL1NMu3AU3LnaKMSGOQiDfWgmIS6F70vPdNa2JyiuAr6YZ7oUTXFUE+zT8sNzDwcx5qYsGfWCaWuHA7FyTNOmiU3q6ozqZzWNI1QT1hcqIXVmJ87W37j2e8+NR1WvBSH5MZ8rpq4liWhSH51B6uDeDIuEELKLmGJmUMgMjje/20/QyNoRwEv7qFX3ERMXeOscyPACNyGErKHFzTqKnB8/9CL7BSOb2e8IXH2CiiIhZDNvvjvHlaHAs4uUrlnEJ01QKLO7Yi1N7HNPW2vpbU9HCntHYk0QGw7W12fPFtD9N0YRlrLWdFSm4YPHq40ue10oU7QM254gpjAXnJ9et2C61qc3PLA8vvBih8MIumWv70u87666bNOkrwV8ATwoeJucjr21/W53s59nJyO1tg/zTu9LSJ7shND5zCFefXtS9VrUH6vMji5JJK4LbPoL8lzsyK+XbEuVfXQ2rc9fQggh0QSdnCZaBKWLkxCyizgKaPKj9KPHLk7OVm/cpbV45uTwxmHngEInqR+yRi+P7Sp7XYSKfScjhZN355lddlEchzgSn96XeAfJ3rC5nuTtxBCCrsonD2Tmfh/ffbC6rsOBEJ+7uVyvp/cl7nbXU/Jqbl3fvD+zrNvJSOFklPxNpbC0tTeOJOZzhbvGum9KX3vzWK7s39FYxabEvZitn2+6f9Uwdy+w2e0ZPCYUOkmd2Qmp5N6JeGWbvjmbhGTq2cZjWQuxM+b/eT4HugHx6tNHAu4ceLzon9tzFR5TadoppCUu+wo+shSGHYGeAC4qXq9N6WAJIYQ0B9PZqctTK3oawHdsbuKt8/zWMU82rXvKrFOEkJZiPnY1UfSUFiDlajcpwXEcDh/Ok54FPDfMVvcHZ/muC2k2XnJNhhTE4UCKJw+i+8G8f+bh/pm3km52G+yELfxR/U1m6Ydym/VIQjdkt9x8Qka6OusiwgXT1mqeOFwVLjelr332qrUmJP/4vptKlN50XJnRgbSJnRA6L2bqlarXoQxc3si0hk3vF7nu+othLwzFyr+TK4XQ00w9YDRbfjeeA1ND/P8okNrZdYFzj392VXHcX71pOegq7BvZQgYdYKgbkC3gILQV1h93OlJ4UOGhDEv/qsVNHZkSlhBCmoMpYAYFzk3jCSFkF2ia6OlaAlIqeN66cBccx+H1Ybeil83Z7yuJg47OeqCdclFiJ+CLYnk4Oy9C+uwMI6obrLy6x7r7yEuVjjWOMB0wLnWt7n+0aoLH2+6K0DS6wOb0tbeuWyuuytFYYd5P3sjHbs/ILtF6ofPOm87tCdPWbiTOPUjKx1Ob09e6HmAZD1muXE9RMPWAIaB6EqLXX44/BjD1BIaAGgHi+f2wJazObuoAYZkagqLpyjoaDtM4PFfhdL3v8lpgRQqJ6zzRi77ZuBIxj6eGSebdrhuTqQfAhX+SL+JpeBaNUHoJr1dxLtI8IjM0EkIIIYSQOJogeurHlDB3YnAch9eHLTfft0qTpkKuc7+vpHro6KwPn3rKwqeesvD2PTdU8Lx/lpPQGdKm9uH5+vKiUurm5egEgLfvuZm26YsvxLeyvH0v/sR+6wMXL38yf6nj5rFcESPf+9hdc1pqwlLLOjO1lgJWsyl97Y0juVY3uGx97ETILh9PFHDk98EKAI676iQNc81qrgzFSp+nhNSd1pytd950bv/SNfVT/8dH4v+78zP2N/R49s9JmkjQ0bknBbo2MHOAi4XT0nMVLGNCLeiL7mLEQkAaAQIhwhJcYGRBwAWmCP9+RW21DBehwbALAEvRFMBK+bOLcnyK0po+9adm83Z0pcDMU5dxtqVz1vKgrI4Q7lyFxq1mXiRWIKZgmvChTU8XFntWuNtUx6DrNBgJIWTXGEBgDJU4EkIIWaXOvYrknYJ1lxAFuGWizpUsYnlZzr5OR5SyrKKXod1PZWxL1xaQVnEXhq5d3ra07fjnQdBdqAXP7/z5fGPK0iRcGQqcjJbzcWYKH557eHqR1vTDcy/U/aj7qLS7YmU9sjoxv/iCvbZN9888PPWEzNVdeeFEpwHWODO14pAMEwk/eOylTnEbPC/izpOotLVRJDkXgq7OIHp9wvpkvX/mwe4An7vpS0AfPPZW5hW3eHbRQZpGa4TOLz/TuT1R+OqrN4BXP1Jw5x6UJd+dTbxbz94EHswFnNnqxebhNPzC9PGoeXkUVfNWeac47Aj0B+vn21FHwe4u/2AtCVzrCnhzD6MZcNz3hTEA6HcEvn3XxcVC0JxNFZyZTmMLPBj4D1nXzdQ5wf9BN2M0CYqgpqBqlhfo8tRBuJCaNeaEducNAeVKiLTltMvTwua2AqfGlRCupwAJgbny18mIaI+KnCubxFL9fVL3KCGEtB0tXiaNhBBCmoPc1HcKqQVhAugmHeHf+UwHn94r9vh+4lDgaCjwwmHxOW/+888Xu4yjocBBp/jlAMC/99kORpPi7puGff+4F70tbTv+74/zmdfpSOHNB3PYXYEn9wSOhkIdDqQ47gncz0FBeuZYqpPRagq1dz50cbfrRYpnV4yMYk/uCdwNTPftHzm4dX3ZCPL+Y39em5yWL16X6vt3V9flvQduajfgex+7ayKiFiXffHfdsnrzWOL+hVoTbPW+DlvGuw9cjCcKg8XvYzzxBdThQFyKgUHSuF03ibFhbEpfGybYmpjrFxSwAX+f3H3kb6MTmE2co9NxsZZyty79oBISRmuEzpHrviKNV3usjgSAW+hLdARwq4twO1oo0T/ajxL8GZ2MPMCWgJMgIlpwTcpjR2G65c2R1bDr1NXhditsWcC1RClDffY6wHBDw9od2Q0AACAASURBVN1RTyK37jk6Ek/0V0e5AK4MBP5icaw9D4CxTo4LwFUY9UXyUz0LYSJoVHlTXTNaKcdHERBgV5a9GDYdp1qANZ2vIytheZN4uyVDww1reZvLWngNRpKdKEF0Tyn8hdc7L3dtCCGkOtI6OunsJITsChMX6FvLSEiZbHIK33/sQbnFNvgcDQUejxR+dFrs//6Lx6LwZbwAAMPil/PiscCPTzx1MSvumX1votTNgRRFb0vrjn9OibG0+OTMFO7OFO4+gghrzLqSom3S5HAgxXDgYTRe3SdxDkFTeHz2qhWafjVOUItbFzsgsJ6MVOo+M8PW58aRxHsfr6+T3RV49qqFJw7Vmgj6/buu+OIL/nXvuaetNWfr/TMPOEu8WomdvmFpa8PSv377R6t9N21KXwvEuzrN9QsTnTXBcwWId22Gpdyl0EnqTGuEzslc3O5GvGCSY5pxXNlwge4I4IodzMlooSOAuVqNedrSfvCRiz+9l31DX3xC4rPX2vlUFnfIHAVYnoK0RGzZn8fmP7acu+dY4+oQwKP4aTwXGG2xjKHl98UZFdEVGLoKIytZTEzUvVSSe6ygyIhAOTA8Ch7MKOdrkvI2blggUoANEWNXyzrlMDakHyaFcFHjrMCEEFIEaR2dFDkJIbvExF0tN0n4lJ1lOsiuACZzoL9oKWJ5c1nm3Ko2CTzDmedSlnPqX9/zgHsePNc/znlHAHjh0MaPThX+lx/PC13Ol291C12G5wJ/9TMdvACUsi1/cs8TP3hY3LH57BNSfOmGLHxb2nb8bxzl86yfRByzu9v1gfi5m53EqXBNp6bmZ251Qp2SWQgT2PLoM/PCUaEC6Es3/O3ZswWePJBrTso/uTvH52528PS+xOhgc9rbOGdjUkdnWNraZ47lWqa1oOsyyfGLc3Wa63c4kOLJBNuridtuQppGK4TOP3qgbnsqX0FzE1H/V1HroMcHY148mkWvU1LK3H/A9uublE1vOLpSXAqUUeW69Kcy7ErsSXXZT2cYY0dhb2EtvRQnE5R11OOjImbKF1LdhLEA1kRY/UWUKGkMZxZi8yBKBNXrFpLyd61u1HySCK0kN/aUwknVK0EIISVCRychhIQTFKKAVUGqKYInRc7s5SLQ51PwnNLDaYVPLRblHatYTlHLCC6ryfvMC7QHNHlbylpG1DZlZa+L2K6EnjyQ+NRT2y/05U92cDr21A8feCJMMItbzp4t8MUXbLz1/nylv08TO6ESFubqdGYKp2MvMo1sEt76YL1x68kDudb/aVDYG42Xy/7UUxauHYrIfQQA3ZgUfUkdnWHiYti2h6UNDqaIDSPK1Wn3VheRZHsB/9heOaRDk7SHVgidHeV+daSWF219/ZkrLByUy7hSbwuhrWxRcBMzB1BbXJuu9kWmbWrTPqyKtPvwuIvLfjpNxvo/caqwt8g1PzL+/zaVR9sKY12xmvPAGI4TVqO+D5t2kxibiAKF2Fg3bEIHrCnA5iHGzucK+eVUJho6Ogkhu8Rg0UaTNRJCSJsJE6JMQcqcLmx8HRACcLAU7maK5SRl3ZNJEUQ5O4PlOp5PpHryFu1Ieg4HUnzxBYkPzz2MLhTO5wrPPSETiX5RfUXGLevlT2ZvFE7jKo3rrzPCvRm5vZv6/kxDxLwul51mH21arxtHMjSFa9LtefaqFdon56a0sFHLDcPcXt336aa6eR4PQqqgFULnvan1/EF3OezNPTiWhO16mFsSchGDtEloO99yY4JveyWlTfswK2U7aQcDACFCp2bsAf050BPLN/nGtsDAUZAWMJ4Dg856mo7g+NQE3xIyhk0h8oES2IPKJMJmFmNTirBpy4ncsEkdsKYAu60Y2xWwK3TT2ALoJbzXPgzcT00jXmaL6jOzbOjoJITsIlkcnUcUOwkhO8Qm0bOu7k65eDFS32pbAOAquJZAdxEtV8HqCI5fjHdRbsq/YHpkTV3Fc0KIz9P7Eti/HOSNMSmdMFGVkDbSeKHzzpvO7WEXtyaugm0JOK6CbUk4rgIW0b6MYi3a7lIUTRPLIM1lyNkyt+q05NS/daNJztRnegI/3CBejS5tzP6wnClAABdSQEJhPAe8rvDHA/Aixmt6wj9H4qIWU4OiqtddHb+3WM6gsyqumvWixNe4/hpiSSjCZi2X7YZNWh7MlD4FKqObMEPJfuDfaD98skicgDAaFErN4TzEUjo6CSmeO286t7ep/wtPN/42N5JfvS6+UfYytXgJ0NFJCCFJSOr0rCuuFj8ZQyPgtwUY79zngn6usRM2OzXlfCKEEEIIKZLGtwD90jX1UyeL1I5OhujAf0MvbSwDJ2ScHZKC8iyHDiQPDyRcoHaCb1nvnDRK5E3QT+d0rDA8WJ4rU1sAjloRMJOUL+urzVHOFKZYRhgip/5+bIioWnS9kAJyri6n08qcJwUGIkKUnavL+cuZihRgzeWbYiywLswmiXueuhRl9xb7f2snLFCYEDsGsCuJJ4INAcHhKOHUFEi1GJpEFKWjk5DieSTl17PW/flD+e7Yw60816dmlK4gatEyratTSgG79LUtj5hubwgh5JI6pxmVlkCnUQ/E9UKGtNHkQfBFTiC5+EkIIYQQsms0XuiUlvy1yi1LJeKEiKyTyeYOi5POt26Cb5TYG+XQzRqzOnurEnz7fYGLQEfhM0Pw7toC04XrswcBOJ4vPobQgz+tGQGsjTNjsF4QPV7OFE6NacIEVR1POwI9Z/X7U2M+wXpmOUqAvRRd4YuTelgLpKYwawqxU2BFoIVYfL+Y18BRGC+W4XUFxnqdAteiNE7YqfIF0wuZToCV1rJOWPlKhalrNTNPoSvFZawTZmOBLoeJoo63KoReuPXaDkJIOHnfL1Qd6wDdnITkw9//3vwbWev+4o0a3ODlyKtPdl6teh12GUHxbCvK3H9h4idAAZSQsrjIwWhCCCGkGBovdN49FbdFv+q1WKdf4o2mu0NCL5DNubspZnX2ViX4PtFT+DjQeePUlug5Hqa2BBwPvbmA1cGlMAkgVKjUw8EYNm4a0qYSNi5qPlFoMRbG+l2OR7zoGhfNunGuVS18muO1I1TO1EoZWBVlL52mpuvUqKfnFRVN8XU8U5CIF2DDxg/E0g1rliUUpjVIsarFTVPsrLP4GYYt14XQfXeaNsMuIaRktDjYllgnwZMQsh0fdcQrWeoNpcKZsi7vJ5tOrwWXNMdrvtAkG98ytNtECaAAUObjYBnLKmt72rQcbkt+PDzd3mhCCCGkGBp9O3vnTee26Bf/L9eXgDZNJikDq+Wiebyls+h4IAtb3zIF312i35eQ0oVnHLfe4ulGx+lYwd4XkC7gWYB0gXNLQbqA1fH78bQ6vlAeFjcRFC91PXsOOEZMQpSQmkR03RR7AfE0lKi8c8b43syYp7Muvjozb8W5mkaM1XU0wX5Sw8RUc7wpvAadsT2lUGV/97OQFMt6nBkdr7p1XLwbEBoJIc3k7Y/cW3/5mbIS4JdHm0XOrgCsGh0y12VaWlJvkmQOqXtsE6bQ1LR7SCmBGmbUbQwyx+M9dYGp9DPI9Bp2HhHSZvZsgb2rNbpRJaRZtOyuj9SVRgudzxziVbcEQfHCS162JJBmnfLIeuq5Kpf5RJFW6DXLZQq+dSZvwbcv/ZRwFwEBb2SeCI6H3kIEl4snVx21C3hTTIs7XzwkL6LWuPRyo4RVLcSGjQ8jTV9fQUFXC7Bphdk0omvS6SIF2A3Ca9CZa7phgyJsHRydJlGuzirRDVNRMYymNWARsmt86pr1LtDqPjpbRVcALx5UvRbr/PCMYiepN1osbGpsK/oesin3iwMIOA1Z1zohhd8Wkzeni3ROU6rPhBBCCCGJabTQ+eJAfOnt8ebpspBGSDFJK7zmIdTOwjqyTEnceqQReoPlpJTQzWUuZBV9ixB8g/10zuZAZ75c0Kgn0XOArg2cQ2AfKve4CRl4OIsSVKME2GD9y/lsXHL0crUQ61m+EOtYAIxoCq5agE0b45yym2ISJ2wa0VU61b8yHyZqBmPTiBNBCSGEpGOmgI+mwH6NnkzO5xQ5CSHb0RSX50T4oh1Jj7QE+6QmhBBCCKkBNWpOSM8Px+qPXxx4uDe1nr/wVK5v7Tepf+mLLYWCfl9Ebm9WwTctZThz86BI0TdK7HU943x0PJwrAdcFnEDr20P4D1oAsN8Rl/M7Xzx4JYlJRU5N3sLp2BIYuNExq/AYRtR4IFxo1e7SJNFTADJGU3AN254ozO+luzyudgJBumjCRM06OTqzYEugoPdsCCFkJ7k39T+EkHgoqjSTOoueN49rtkKEEEIIIYSkpNFC59+6ZX81OO7lN14/+hvWb5843eW4g5D+jCwLkBGvLaZxF9ah3wS3QFW2SYJvkWwj+DrK72MjDNcDvIDI47r+x+T+mWf0W7V+UKQEOkMJ5QF9BUwX045cAK7CUAGTxbm6F9gWafw+HkPAspKJoWkE1E3z0rE7UTi3BaCFupDo76TNcR8KIzcfATZOFC2K4Pa6LlasrJ6rMDKmX39ZwD9PrnYU0CtuPbNCRychhBBCSHrGNXiJjWwH7yMJIYQQQtrFnTed29vU/4WnGy3TxfKf/b+//85br955p+jltG4PXpxNjidPADD6GDyZRz0Mpn9I1N3dyYR9UAspsJ9RJIsTYzWOo7bOM+O5ClNvu3mkFXxHGfuATIvnqTXRMA4tJp57QDfhOl4oBa9gIWzTIfY8/9wUEggzQ4wELk/3s+BpHxCa1NSfj1o8gAuJFaH0xJ8KQgDDqPW1BDxXXYqoUeVzS6Dr+OJm11GYLRTlvFyiQD4CrEmS88ncxiDnIf24KLV2GJDl+tR0pp5/LSkrEkIIIYQ0jX3mGK0lvL8khJB2sKcU/pvv5tBHWAzdDv/LSXKudYCrT9TzJuPTR95/+is3uv+k6vWoA5Yn7nzUEa9krT9u6YtwUwX8D5/76u//W7hzp+hltU7o/IOXfvfL37xf3PzVQntwEwt1aiEMZV5i7Ld5mKHOPODitKW/pi2oU/Y0keD/zHUVOjk0fOhlmcs0hdJLFHAWNZPFiakWLxkICfQcdek0VcbLB0IC0OLfIn7sAUIqPFwsKGs0xdqe6ztdo8ZpJ6zygIdyfT6mUzYRNXEqThtyA61dz0VE3ehkRoqkhBBCCGki5zW5xyRLzOw9vGckhBCSBLNd7LADdTpffcue4zgOAEQN2/T6ACaL+OOZeLri1WkNjqtgW6J1sSeA753PSzmRW3frvWs/MEV9cieQCX6pdTwXhFwKpqZIqMdHCbhJhN2sy48aZ5bD5pNK5KwByvM/vUhHe/0IEyTziFNv/aPHZ41R6agJIfXBavGnbCyZ/UMIyRc6OuvHqePfGz6Y+uUHU3/4dB79IYQQspvY8EVO0RE47PiWgrNF/kCO291xoiNwOocwh/W0Jv0axImxPp/uqg+D60iyYS8yA7Ytlknrmh/unYhXhFik8dyBj1IKUmCrD1D9dvAT/5GW2ChkeiEpUcnuogXdpjg6geJcnUHyElAJIYRsRgrAavGHkLI591TlL1s0/eWNIpi668NxH0IIIbuJA0B0BA6g1BmE0GKWKYBx3G6NO51DHEApfV5oV2fQ3WmKjFXGfmAcyQeH7fpb06rm2pffeP1oLpA5F/Kuwrft60+SY+TSYUYCeK5qlKOzLIoSUAkhhBBCioaOTkIIIaTZaJETWIpZetgUujhuN8YddnB5PpxBCJ2uNujoNEXGqh2dfWMcyYcqHJBlUOZLja3qo/Ov9f7O59WOtel7bHDfGZKkc/VcBdnSCyNJj7QEphVe5Z0dux4TQgghhBBCSJ0os32gjGWVtT1tWg63pX7LOQMuRa+zjhBa+NRRdJZiKMftxrjL88EYp8drTJGxDhHYvS4Ei0T3adk2XAA/u98ppYW4VV6+Zw7xatXrQEiVuOzvhRjUydFJdyQhhBBCCCGEEELIwtknV4UuPQws3X4c1+5xQZEzOA4A5ov+k+ogbgYdneyjMz/aKHJqvnc+L2XjGufofPmN14++dut3vieV+5OhZX3zfObc09996754RbSlk48UJHH6RSHldvVJefR6Aj2jP5drA8DqAFc6Aj94pPDRVMFTwNyw0SkP8BaDvX57L5gkHP8txGqFTlPIjBM7swiePenXSxPzIM95EULqz/MD4OZgffzdMfCTcfnrQwghVdARwEHIs/bYAyZ8cY0QQghJxUVHCDH348FcqYuOEHtzX/y0JLDHcTsz7qIjxNlcCNkJn7Yz9dDfWzZC1SFtLcA+OvNmqoBeS5vuy3J0Nk7o/NrNr/xmR+AWhHVronC7Y9uX3/3KJ9ZTJY5mHpyQDg5PptH7dzT1MEX8mXUyU6g6Ta7nbp6GFM9hR8DaILAfx1yp9jrAfmf9+yudZHmsJ3OFD84UPgLgzlWMq7OlV0sSivKa9RLDNMP1TNeJiz0r2j2qxcq0Yqku2yCElMVQKow8EVsm9WUH30MkpHUcWMBfOlwfz5c+CCGEkHQcQKmLRYrSCyM96RlWRTBvIYQezpW6kNHT7c19NyDrNrNuUPTU0YI/7XzPt/pV7eAMipzsozNf2ipyAnR0RiIt+WtR34X1Bzfshrf0Hw1j9u8w3+aYieer8llwXIWLCOFqNPXwziz7egHAvhT4xKFMJO7mxX5XwC5hUVe3uEJcq4mCkUT7sTsChwnWl/137hZC+mLnNERE3yXiBFTzu03iKFPtElIdQTGTgichhBBCCCGkqcSJW1mjxLpLkHWbVzcofgLA0PUAyFo4Oc20tQD76CTJoKMzgvFI3O7tVb0W6egI/5ME4XpQlkRPKUyFAKTAFXs5H5MfbSlyAsBTB8Ctochd3G06bj26NUzM9T2B6w+BE0vAcxXcEDHG8wDJw7wzPLFIVdyHwnnF69I0wsTRnrUUQQkh5REUMPXwFVfhxHhBy5pizWZdxnseO/4uSSIE9xEhsSRxrAevhXW79tRtfQghS546lDgouDuTYV/gcA68cFj8xaDoZRwOBYZ9Ucq2PH0oART3Jq0//+L3GY9/ej4+8zIJZXHzZN3m1w2LZxDCMjJVVtkfZ5ijk3105kdbmxvLTEjaKKHzjx6o254C5g0ToTRJHgDV4uI1Dbk2Brc7zMGahSbtz7IeovPat2Vy8wmJ51wFM0XtTAHO4ooykQIDo4+xh9PVnfm4iRu9AxxIwO4ajfyWwr5xfbjaw+W/4UFHrLl2H488nEemMyZJCabFBYB7Xo8aMiEFM/JEqLh5Yi3HD6XCmU1nJyGkeaQVOYdy9V6fEEI28TdvSbx4XPx14+ZA4BeuF9/E+Ld/rpxmzDKW8zc/JQEU399MGdvC45+Ov/t/OSvCWFAo63WEuEC48zM4rTn8BICHAbGNdZtV1/z+oiPENQC6Sc8UGqtwc9LRSbJggalrQzmUuP1RxQ322whteQuKZzMBbPlmnlOBcFynfdhUgvuw25fAha/AOJaA7So4lkDXVegufuV7UHhqf3kT/fx+cK6rM516wCjgGvZc4DSkc1pH6fMxnLO5gtfiY3e0IRfzE73wjX8mJL3ywcaUy/Hfe02zIzeUPaVwUvVKELIDaHFzKJcxSvykyFk/Bin+koadZd/JdWDqASO+KEQKJq3IOfIE9mv0OyGE1J8/eLP4P7Pf+pkOfnSq8L+9W6xv4x9/ycZ/+cdOocv4t29ZeOFQFL7f/vGXbPy3353jR6fFPb+/cCjwt3+uU/g+4/FPz1O+21Z0V0dfDs894FiujTenD62rAByHT8O6Dakb+B5zD+j3BWQHGKIahhHlngBudj06OslGmLo2hJHrvuJGGHnLsvfWTWiTWz7oHgxk6m3a1lVZt31YFXkLvrIjMJv7yvVsMVEwu/HUQeSPJdi42JNAL6Rn6auZ3iJPXidMYK2C45Bt78qsBy263sxTl+mqZwWowarMHAE7xAVzMRJSOFHiphn198Cq+5Pkz0ABY5E8An4M+TtdwxLAzQFK6cM9KY4C3j5vXncGpFnoa5Y9A9BZDh/NFR4bDwvBdN2EkHbSs4DnMrZk/+As33UhhBSHZQFy0fVUMAJ+Vi9djpourJ4maR3WrV/dsHOl2wWOD6p5003fjs7VavkSj42OZDN0dIbwcC5vRzkYw97psS0Bx1W5Rtv14Fgyc8yTPFKNhvXluAkKlT51E3wHHWzsi/F8Buwv0tf2pC8q6jgtrmuI1Ay76+tXB5dHEUIk4PfNq7XdbZZhCrEzT20hzOaDLfx0ukm43ov/Pur8DBsf1sdm3tDRSUg5xImcQWGTImexmOLlpmiKnoAvZG7io2k9/us1+v8lyboTkgXzOva4I2KHzZc6qkpdy98CIfViYAFjdxkJIc1Ci1rBGBS+giJY2HBYveC8N42PWx6Xm3y5aY5t2DRh0/WgKu0PPShy6nJHoMhuhkmLoKMzwL/8YPafnKTcJc7iIpFndCCALWKeKA8QObiKXKA0wbcMdtXdm4Sxo7C/6O9xpc/BMns8Di4vZLhnLxsYg7GO1KVhNiiSFiXMFoG9YR9Gfb+WgTkExzh3plHllI0DdHQSUg5JRE46OsuhSEcnAJwWm4mMkFqS9GWO4PWOENJeHJUsw4EWNylyEtI85Ia3h/T35nTBOmHzCKuXZLlJlsflJl9uHEnrrk5Xzf2fKWqawyuxkjUjJJzGnI/fui9//aevV70W9eEip3Txe51yBd8y2EV3LwDYA4mzM4V+nLjlKmBPYGQJDF11GVHmw5GF1eUFh7FIsWt+n5YEYuo2w3V2w5J1TJHULIeJpGGi6GU0zlM6Ogkph02N/Gz8LwftzEwbgfq8DERIXUl6feN1jhTFTz6YYzzm+ZWVwUDg+Rv5Nq1FJe+qU4p3E6tbXUN83ogyXwIvEKvE7eDxJ6Q4wtLZlkFQ1DTR4/t8zgsl+PJ1VBaqsCxVbaRMyaExQueLT1jPTRrkTspK0otEDllrd4q2u3s1hzYwm/rliRQ4EArO4opiW4Dj+ulrpaUwAjCEL3ZipoDuUvwMDucZ165wIVe8tTpZ2LScNMMBMfbSDZttzbKTwg2rRdg6oNPoxsWq0rAFCRNFTUHUuRQ/BT7hTZMYSgkhWxDW+B8UPot2PD2YAWchL5hNanKNLQvt0jQdm5vKA1Wf/yJC6kjYdazM6xshAOBOFCZjnToPkIvnC5aTlbslPsaEtQPVQfx0Z+25NqmWOGTdEreDx5+Q4qjK0bmSnhbhZTo6wwkKmXEipzm93qdto8z3RxpxPr709TvPzZS61ZR+Qfpy2fiVtpy00ex8piByeHOijo10fCMkO04Hl+pb31Mr7tYzJdCHwthR2LME0BUYzdRSdJ354idcX+Q0h3ONSXAVRot1gIXCxdc04mwuImxWUrhhy+inMgk6fe6m6CS8FtnSnzYuFomevy2B02IXRQjB0sEU5mqK+i7vtyEnXj3vl6rAdGomKQN0dBISR9h1LKpMkZOUgbRYzlKukqpfgi/TOVgGbXH0lXVcePwJaSdRaWvNcpX9htYZU+DU5SuuwsnC4HE0V3hs7DyzDWPTPn1hCFzrFbbqmfju4/j2kjLF20YInX/w0u9++a4DuDVsZLLk+npdeNnLiZeZg4hxUELDU1qhF2BjoiaL4Gt3JR6fRZwcnsIYwMQB9vrwhcMFk45A37zyRHy3Mp0WIvMqB+OiHCXGlhpN3Hg3bFmCq78uq4Nh9eypB1T8lsgmN2dXJl8/LYhuioSQdhDnatrkeOKDV75ol+a+BOD5DVvnnj+sozleC550dBISTVZHZ92ub3VbH5IO1xKQFNJrwyTwjNe3/HFmTEPhv0/jf76Ma0HRy9COvjK2RXQEOqKg357nzx8oeFt4/AlpJVEip/kdHZ3hmPfuWsA8sZZOTi1yHiuFR0Lk/qJ23SjzetqI8/Fb9+WvP3Ol6rUIxzFuQstKGeJ6+QidE6t48TiN0BsmGsdRQDeXuVOGuzeI6AiokNclBhIYe4CaK1xIiYGjMLYFBo5CHwoXUmDPW4+mABolhuZSDsZAOUpwLU2INctVCbCbMEXYYFrbCknm6qz/H7t2jeryuNrVIWQnCDb463HA4sEg5Lu2PyhUhU5F67oAhB8HWIqawfF6ejo6CYnGdGnGiZ3BaQnJk44A5OJa7Xl+mTF5FAW14GlhUwufpthpfp9W+CTx0NG325R1/NvmhCXtJvg3R0fnZi5dnNb6y9qAf1//KCSN7SZ+NPI/TWJKR+cq/8z5h//u68Pf+fxPRvNX9bh7J+KVX7wB9f1zeVsszoWqG1LKTBkyyik/dxHrnFXwTSu61tHhG6QId68+ZuczBdddit792Xpl/dDVtf0X7Y4AyI4v1o27AtKIEr7jcyAULmx/nCeAsS53xaU4mmS4t6hrCqpZ4qXouhAwzTKA8oRYs1xjARazhdPU9V2xTkdUkc5/hc39czYD0y1K5ygh5RFM4ajjI7Ge3pF92BXHQK26OF136fCMKxNCoglzqGuiUtnu8+UBUiBa8GRMF4sgytkZLAenC6JKvGaUsayil6EdfWXttzbsszKX07bjT0gToaMzHn1vb4qcOnWt6fTUQmjb6dHRucp3vvDa41/Fa98A8A1z/J03ndt2V93OMs8kD4idhCebSHnA8hBk3fn28zgo6MdUdR8RZZA0DVsSMdaLEHrON9Q9O12d4LAjcACFM/gRAAYDoGc45aa2QG9xgLQ4KRdimS6fdgR6i3FmWc4UpouYZFgLntPAd5tiUIAdA5BQuFgIqQPXF2LTiK56eKqw8l0eImx/vup+Ld0Ja5ZDhFh7ripPXbvJ0UlI3Xjp63ee+8qnfu8/zGNeP7vfUd87n4s2lf/WLfureeybpOiHANhLJ+cU606ongvAoqOzSFbcm/DL557v4IwaTwiJJujW7DrAzF5e92b2+rWuqiwYEw946yx8PGk+MkVXEqQaTEHTLJuiJ9keOjp3Gx5/d4SpnwAAIABJREFUUkfcmqY0pKNzneDL17psipz6u7BxbYR9dCbkl66pn/r2aba6m0QkAECBjfCut33q1W2fRc4VYM2a9UMK9gWVR6yaMDE0yblhWQKuuzx+PcfDDEAPCjM9bi4w7RjH2PEbhy9ZHP8eBBztCF1M04MAHO+yrEXSKRRgy8uy3ZULoXK13JstF2MKrJvKpnAKLAVYOVM4NaYJiqyndrwoOzbmk1Z8jRRjF9snsXS/DhwFD9mcsHnHPU9hmvZNjAqZev6LIMFISNn8xrXfe+5Pz+Xfy1r/5w/lu8MubgHAvTnwdN+/3WpRuVShcygVZotrwcgTgLUUBPRbkgB8hydUqx8SqibMsalj0O054GEgJBUjT1y+0GFe9/R3VV/b5go4p5hCyBoHULm+gWB2lZEEipz5org/dxoef1JH+p6H2vRNZUBHZzzBzC27moWKfXQm5Fv35a/LftVrkY1tRM68UrYqT+G8ga4HLU7mFasm67nQ6aynhJvaEj3Hu4wzBxgk+JVPQ3KbmuOmUKsi6UIABbAUSAPllXmu1E1QNpkpX2iFQs/BmuAKx0MPq4KpHud1fQG3h6VzdQq1ItzqOpvKpvvVdMMGnbBTXS+DE3Zb4TU4fNEVuDL30IT+L02XtBY3dYwSQKuOhCTBcRVsS7QmVo12cwK+62kI38k5tZauzkcNesGjSWghc4DVtLRa+BwYfXbuW0xbS0hStIvTvL4FifuOkG2RHUBagOcC/Q4wmfsRYDlJ+aJTzI1HWFcZScVPkh06+nYbHn9CktOXwJzPfGtsEjV3TfSko5OUgmB6mkYjQ27AeounIR1P50AnwQUla7+qZaFF0zDB1fzeH2dMa4ibWhSFLTF1vPTiq+F+XnHA6vGbnLAZY1BQDUYgXHjV45vk6ASi00Lr8XWIFDlJGrQ42JZYBVoEAJauTZOptVoOfk/yY6WfTsPBub/4PszheVTpGhNSf/T1zbyWmde94HeEFIUW70yxk+X4ctgzeZGEiZ9APQRQCkS7DY9/OqwS9tf7Dz08fpRchfrZqxI/fdNSVkeI//H/mW2uAODpoVCvvtBB0XUA4N//nJ/24lvvuOLd04iLYUSdHz7wxBsfJNsXWeoAwNGxhWeeqMHFuEQmHoWlMMLEy6CwuUtiJx2dCXj5jdePZF+8UvV6VEXDtAtSAJ2OAIzG3FGYNdQDpAt4G26i6tyvalIRdmpJ9OLszvqpMOrpMAVhDtiV7xai6oowmzVG9b85M8RbY3xvtnS1Aqt9tDaNOrg2w2LSPnoJIfmg+6s7Vmolba0WAbSL81gpTC2wj84CMMVN1/UjEJK2djF+LHzxk+lrCUmHvrbp65se1s51wP/dEZI33cVf50z55b7RUsRydLlbk1uOHB5xCSEtQd+be67CaJT8Zty+IfCJQyl6EsnrDbPVudEXYtgXqdZP1/n6fK5GI5Xo6qvrvP/IS7wsXeedh8nrAMDB4e49+NDRGU6UoKm/AxA6rq3Q0ZmAv9b7O5+veh2qQhXYdyhpFlICnk7FO1fY7wgMXQ8jS17Gx65AN0Lo7EFh5ArsQ8GzgJErMLTqdX4lFWHl3IOT0zJt4S/XjHpdguNCKetJM2Q5WlwF/NDkNGd1cG+GRUJI+QzlatrGoNNpiOX3x6pe/2NtQYuWWsTUjSh62BQ9zbL/X8XX/AkJwxQwgdVrmzlsTlOX7jdIu3AXmRu6roJrCViMsdEkz3+4qQtM2VUGIWQLTLeoSHEdcWYK7kwBfZGqXpY6WdYPWLbJpKk39fyXeNLWSbucQrFlqa64pJQpYDUJU8QMK0eNq+MxzgM6OhPwzCFefTCpei2azTb9hJJ6IC0BbyF8O47CI0fhEQDAw6kEMPcwhsCRKyCNBzJ94zNauP3OIWAB2IfCGOKycTJJ3IfC2BKx5aGlLkXUTVG6/vrochSbXKrb4CjfCetYq8OwlsKrjkkcs3kwNZyZrrva99nIcJi689V6n+gpoKKUk3V2ChNCmoMpBJjloNNJR6Z4LAYrwk22ImouWBmuQz49QmpK2LUtbNhkX5bbYEA204t6qTTm8nea1xuaOSAtgY5urewIv5GIMT4CUHMF0Vl9zs4DfW5M6ZIhhGyJlSIdoL2wqE+9dPXKrOMuMpmlqVdWnV1jrhaOzqpXpIYEHZ06A5Xp4DS/1+PaCh2dBi+/8frRd77w2uPg+Hsn4hXZr2KNqkelfBOFtJdOB5iHPCRbErjM4uoAFx2sXFk8B4juolX5fY3of6s5MPCWb/lqgU3HxxBARFmLp+euX3eUMJp1gOj+C4LC68BVvmibsqyF1n34w1qk1d+HDQfHA75AG4cb8bB6YbiPBh4wgv87XyXbP0NvrjDvZaqaG5vSwRLSNLpnHmYHPHnLItgHpybodAqKAhQC8kX/h5n/u3p88H9aT5OGQ7te/wlTr15CBGk3weuXOdz3gEmNfhtkneeG6evw+tJ8BG80CCEZkdENcvktwxKZ+hF2Zyp1vbLqAECvI4RMmYmuK7L1qZymjrREKce1LnQEHZ1xmI5NWApDLIfNLnna3j8nQEfnCl+79Tvfm733X93q9uW7Urk/GVrWN0eu+8rbY/nKJNCAckL7EGk5x4FOQHqWgtsRsDpADxJXe0DPBv7iROG7H/lKpztXmM2MBkdPwfWA4b6MFDu9wG/rAsj2DzYHTgBsEun2BDCWvsg3lsDpoo4eHsyW348ADLEcPjXiI72suV8eeOpyfFQZUHBmwFgqnC6+uwtASIWJ549/7AHqUnlUl9u0vm3bX4Mutp7DKtOKH8DD0r/qhmwdmRKWNA2KnGQXMcVMU+yMKwNInLr2Wi95v9xl4CgKEaQeUORsBwMLGLvLWCeEBBTPs8zwJXRCSBtwZu1tU0+6acG2UBINHZ3RjDyBK67CzF4VPLXA+cgTl8Jn20VOgI7OS15+4/WjnlK3OgMJALcgrFsThduesPDZgYK7ptKEt46cx1yokoijE9f/JKUJgivd9+m4kqLlrW/5nzgObIFezCyvJPxlWhENh9f3PFgPl67O2XT9nJw7Ct1uPU6ECwXAXYh8xm9ND6/EqPEhv1FzfFQ59DsAKma+TaIOjs4gUw/+frWWMU1jclR6sLXpYlykeUR78yoQQkgs17vAYch//ukceDArf33iCGZRSFIGkDh17d1x+L6oilM+tRNCckSLm3UTOTXs1oZsg9UVi4f6dmDVpJ2kKfD41xeZ4tpuG9udpl7ZdYpclpTAKQQOFyJU1vXbBejojGYoFWZyfdzUKO8SdHQu+NrNr/zmVIhQk1TU+DD6xskV3Ln7hfRdl22ecwVcbHA2Oa7Cu6eZZr+CZQHXBvX8804jKqZh3yrPKVBWl4hRz8k9GzjsSTwaR59QaVPKkWZStaMzEisQU5C0vxo9XVjsWeFuUx2DrtNgJGQT7CYyHwYQGEMljk1irvxrykHIWxN1drqnFjsTOjqnLvCA9yZkB2nzdW5XcFS9HOlpkbLx73ZWChvCl/3ZtQHFH0NqePzrhxS+2zzN9clsPixatNRiYpo69+fAk510y8pSZzJXly9fplm/XXT3dwQdnUnQbk7dT6fus9Mc12bo6FwgLflrwf9L3WYvXA8qw2uHdX/bYG/TJkmBK1e3f4qqq/ZRNGWZbYPLKfvB1+pIHHddPBpHT+POFbKK8qQ51NHRWQc2iaX6+6TuUUJIMehG/aSxSUJA0+7FwvrkDPbPGTbe7u7gUz8hCTGvWUkjqSfm81/TRE8hsvXjRnwGfKZulaNP8LeQGh7/9NSxK8dBwM2YhF5HiLR1tuEiQ3rCLHWyUsfjWiR111iqpuvgMn3tFLhMVzu1/LIe13P9cW2Fjs4FnrCeD14rL39EUubRHV5j2fYk4cXIp6wfWxXZjA8T5NWczxU6TWtpJamoraOzIUQJontK4S+83nm5a0PI7pHW6UQhoDjW+t7EurAZNo2c8cUqQqJIc31r0oscu06TRU+SngmPMR19Ow6Pf3rK0N72pMBpSuVtMlewbAGZol7aOr2OEFmWAwDu4lRLUy9pHWn5fXS6c6VOO/7EaZazJ0Vhx7Wu9xJ0dIajHZuw/ZNPC56a4HAakbMjEpjkSmSmVt3gVVNbofPlN14/mni4JV0PniWhY1ugUJkP3I/R7PUFbFvAiVFZvRpdjEgx0NFZDGW+FUjILtNmR2fTsCygB4WRKzC01uMU6+MtC5jyeBASiukC0+WkkTSHJoieN4/b085C1imjGU07+kpZVsHL0I6+spof27DPePzrSxa3vqvS1yujjjtXCh2/ISZpvbR19DSTRWMxsx3E0+Y29W3Qjk2NFjW1wBkcTsNL+0C/ZuflW2fAeU1eEqqt0Pm1m1/5zQsAriXhuAp2Sf8yZZ0rvBj4UKjMh7D9OLSAo67ARzFCJ/vpbD90dBbDnlI4qXolCGk5WRv0KQQUx7qIuYyuC0wtf9gUP5m6lpBosjg6j3iNayz6sayugidpJz/7pIWrBb/4ut8BPlOSYP7LzxbbaveZY4n9TvHLAYCfe0ri01eKuyAc9Px5F70tPP7p+UlJNrik72erQLNhlve6k9QxlzOZq8TL0VqQFiCT1Numjobvt8dDR2c4ZipasxwUNfVwmtS170+B6ynF0aKZ1MhEVVuhU1ry1xxDxXLcchQtJzBsW2IhtOYfbdeDY8nUsQwo+JZLEYKv7Ekcdlx8FFPPnSt4SuxcHvldgo7OYqCjk9SZ44i7uzO3ef+7WUQACp3F0IPC1BKhzs2hpQBT4FxMN3AVnJlCeXeWhDSLtE5OXt/aQRXdmpDd5dkh1CcOi33iP1jce5YhdhW9jP2Ovz1lbMtz+wLTYXGHprfYhKK3hcc/PT95UPwfQZrmY9284c6VmqTscyJNHXM5Vid5o4rZVt9NqGQE2/eLqqNpiws4DU1rWygLU7QMEzCDwqYuJ9EGTh3/Uzfq4rGprdB5b2o9b1vV/2K0wFpEdCCADLEMwn4zdRJ7yxJ9my74XhkI4HH8NJ6rIOtyRSK54ilAVHhsbbF8uNrE9YUYO415Eyjqu6h+NIuEjk4SxVBWf+/ylw7Dx//pKfCoYa9cZhEBmLq2GExhE0Y/nDqaLs6BqzC2BMaWwHGX9xiExMEXOZrPxPXTiOlISJ34F++4wvVcWBJwPeQeAeC3fs7Gnz3y8K/e8x/MiliOJYGv/byN//5PnEKX8cvPWvjMsSx8OV/7eRv/7Mcu3j3zClvGrQOJ3/rLVqHbwuOfLT5/o/jm+IkEpJWir03jfYik9eZGg0+RdUxGloS00lvIsiwr3f5LPfvGM5DAuOqVaAh9b3mOpOmTsw3MFVK+PpGdWgqdL339znOuULdsJLe/9nfwglI2dRJ7yxJ9m+7wHXSwsZ9OxwE6tbwSkG2RAphUvA7dhC8P23I1hrGfYrmO8d8RFEjN4awiKR2du8FQKow8sRbjvtPfk3ygo7M+mP1wTiF8h6cRzfFjazlezkp8siGkgaR9oUNKUevUp4Ow5woPGNcorVURTNzVMoVPUid0s0FRsazllb0Mbkt9l9OmbSmKvgdcZLxfyPK+fBl13LlSSPk+f1l1+i2/zwmj7fd2ebKLQrimTP9NLeWNP3jpd7/8obsUOftyc7lO+YCrhIJvsTTO4dsVG/vphKcgKNq0ll1NXWsKpkHxNEow1eLoNBhDxFA6OncDLVqaMShoBqN2dNbVKF/X9Ypim7SOdRYCklCn9Q+Kmmmixz46CdkpPjVcHzdxgR9flL8uZWGKmlrwNEVOCp6kzbhsi9tpePzrjcpwG+7OlVIyffraMuoAvlicZbuS1hm5EEMLatoRUDXI1lRn6OgMJ8uL2sxKlQ+1FDqlJX/NNax0F97mchLKfHNmW5KIu2FlCr4+FHyXHHYQ30+nB8zndHW2lWkNVI2Zp9CVIjbWwe0TdJUGBVFTCJ16Ap/wpmlMpqSBaFHziqtwYi0FTT0c/N50fBLSVtKKnft0dBJCWk5Q3AyKnsFxFD1Jm2hSOxvJHx7/+jK1sh+fLPWKrqMFyDT1TNHSyiBaplm/XUtHCtDRGYUWLdNGsj21lDbuTa3nUcBBbtKbRknE3ayib1NuRLKKvQAFX01fAoMBYJ3Fn/+uq9CpgSBG8qcP4LzC5fsiZnQEVtPbOp4vNOpYF8z12u/4IujprOq1IkWjRUtTxBxKhROsOjyDw4S0kd7i3lyLmMHvosbT0UkI2SVM0ROIdnkSQkihJOxChrSXIYBRyjqmmJgES5ZTpwmEJLRoPXR0hkNHZ3XUTuh8+Y3Xjy6m7q1atXCXRFkpypoi+BYp9gLNEXyBZOJucNj1/H3izv0OtF0v/KLZpP1A0qPmCqg4de1mNyfgeKtipxmrxpbJ+hAl7UOLljpV7RXXFzVXnJxY76fzen/R4TohLSIoZAbdm1FlQshuEfZMW3Wf8VUSFDwnGfuHJ4SQVNQkaxKpFmllOwey1CurjmUVv6zR4seTdf8VQR29KXG9pO0ydHRWR+2Ezu984bXH/xFeEy+/8frR67d+5/MAIOfOT33rvvz1rsArgH+hsQJvQloWsN+t4a8+BbxA+FDwXUeLuKOphzNj/Bn8hsbJeOGSW6R8Pl+0sHuuWttOqyPQ6/s7mSJn+0nVe3pBxLk6TbGzroQJrhQ824+ZhlYLnkFRM0zkHEqFBxPgeAf7xiXtJs61GSWCEkJ2E0f5z3Q6knCHJyGEFEWdBBpSf0QF7RujDEp8WXWQ8n+6iv1XB2wBzKteiRpCR2d11E7o1HznC689/lW89o3F4Df+5Qcz/PM/84XOtHQEYA/ir2uWJXCYcG/YGQRVywLsHASH3g5cPKsUfCcuMHfTr4AWGjfhusDpBpuP66hLwTItnqs2np+2TYFzl6jDG/RxTs66i5xR1MVtSorFdHQmSUlb9z462d5RT5okAgRdm8CqqGlGPV6yj05Cdg59XWvS9a0sKHKSOHoW8FzG/Ic/ONs8DdkdPJf3X7uOtAREim5VurYfR4BIKtxpE1KWOkB2gTBLvaLr7OLLBTRsrTNYXHezRrIdtRU6g/zdu//of3rF+sp/l6WuAjCbbvr1qUblle72+APQKA9wWnZ1DTqWk7P5vNjFP99dpjdXmFfsLNvUP2cTxU6bfRHsDEG3pklwvC4zdS1JyqMZcBrS6O3WsCE8SuAMDgfHewlfELzeq5db3vGAB9Oq14KQZkJHJyHFM7CAsbuMkRTdV6P5LFdGv5Bl9T3Z8OWszLbIbeHxrzUy4711lnql1HEz1HMBz0peZ+QCQyvDcnYQOjrDyeLmpNCZD40ROr/zhdce//Xv/fY3H515mVydbWOzcEt2kVtPWRha8eeG3eU/9S7RsYCjCpf/aJO7zQMAge818A1kOet/4ev351+vej3CePXJzqtVr0MbCIqcOoYJn+Y0TF1LkjJTaMzTYVjK2rDhLClrexZwYNdLEHEUcDoHpjUUnQkpk4H0f6NBzuZAVCKcXXN0mpk+6vTCBmkmSV8Q0OJmrMhZBi0RhKpAO+iKwFPFzv8SHv9aU3QfneZ0RdYBlgKktARkwgxbuo4t0u8LL2NfoLtEyzxHuUE3Z3U0RugEgKeuqG+eXmRLX0tI27FsgWev8MmarDKbK3Qr7KczaYbXUQNvkGZCADNx2xw3cf2+l6osk+IIEzjDhFA6OglZkiR17dQF7tbQJk+RkxRJ1q4kpCg3FfmhDdzor49/Z+SLnUnZlbZCip4kD6Iaj2v58kADs/PsBF5JqWt5/FvDeYbzJWuK5JELZMzSXRq1vN7WDDo6w8nq6OQ5tz2NEjpff/vOH/6NwZ2/V/V6EFJHrg79J2ktbDEydjviUuSsej3aFgGECsjdTvVlkh9a0Ixzc5pls4/OCt8viKWu60XaS9LUtRQVCSFtg6InyZswAbTyhlE6+uoJU7zuPJYFWCnury1r6YBM+jKWtETqOssFZnvpK+12XdZLsayRJeAolapO9i7ImgsdneHQ0VkdjWoaffvVf/AGvvVVWscJCWFvuCq+MDKaVL0ebYtJhFBzuqjhIqch2xPWP+em8lAqPJgA10PcLyQdWd1Ol/U3/By6Ijwd49RdpJQlubBXlqOAEEJqDEVPUhSVNzS3yNFHzS4DPP61RqT4v/G7nxCp6pniXuo6brr1A3wBElCp6mWpo8lSZ5ego5PUjUYJnQAwOvf+1cGx9ctVrwchdeOojP4XCCEAkguhm4aT1Ek7DckP06kJ+GLmsVKYAqEpbDUUOZtBVDrGDybAR7Py16etXLSx1YgQQrbASdq3A9lJJgGnUt9adlORpbuKwv+Gzf72SvjLb5tRsfH7jMe/tvR7Av1e8g3xANxTAKbAk3vJ62WuM8+2nDTbpevAknhyL91y0JF4snGqSblU/qLNDjKQfjtGXZh6wIlT9VosadxP9voN6+/PZqDQSYjBkS0ochBSAtJT8KS4jJqgmzLKURscjhMq00wTtg5ke4KOzqFUeOQJDKFWhM3gdHR0ErKEjk5CSNuZekCPrg+SM1rY1MKnKXaa36cVPgkhhJA86EvgvOqV2DE+tV/1GqwzVcC4Jtbexgmdfzj9r//Nlzu/XfVqEFIrjgZVrwEhu4EWN4NiZ5TAuEl8DPtuk2gaNV+KnPmjxUzt4jTHdR3gxPIdnrBWnZ9Vi5xvnYWPn9BBUjgHHeC54fr4sznwzqj89ckTW/hv7epojosrE0LILqDFziaKnj/5YI7xmBftrAwGAs/fyL9pLcrZGSwHpyOEEELKgO0L5fP2eb0cnUB9RE6ggULnd77w2uO//r3f/uZU4ZWq14WQunAwkJAt6huBkCahf3ta/DTpdsSKCzQ4XVg5WCcYzfmS4hl54lLINNPVzmxgiKXD06RqR+c5G7xIAWjh0hQ7k5SZupaQ/KmLgStsPb5/mnzatjD1wmNTcCcKk7GC5wJycaBYTl7ulvQ3ZwqaZtkUPQkhhJCyoKOzfMYeMJ5unm5XaZzQCQBPXVHfvPtYUOgkBP4DVrcj0LDnaUJag5nC1gs06PcEMDVcoJvq2JbAVC1jmIO0J7DyHSkW081plsO+7zq+AFq1o5OQIrAXlxzTqRnm8gyOZ+paQvJF8OdUO6YtEZmkxXKWct7oPlzthM5gipyEEELKxhZ0dJL60bCkKj6vv33nD4UE+OGHH+CK8Qqp7o+bkZGxHnGqfMenOU4P62iW9fRThbVpTJFz07JJfkyt9fLIE+g669/P7IUDlFRCN5AyxdrwiWNT3bzmea0L/Mzh+udaN/tyiiAsLa0WPs0YHM/0tYTkx4C/J1IQriUgJfjJ+HGtYu79HG/9QwghhNQBR/mOTkLqRCMdnW+/+g/e6P6b34fTrXpNCKme455YETfqIO4wMjIacdH4IT0FWGLFpQkshMuFg1NPP100ZuppHFetTKOH45ZN8qHn+mKmdmsCCxen0S+C+Z1ObUvKZ7a4q6UYUAymkBl0cgZFTrM8Yf/BhOTCWP/Pux7anQiWlE1H+IKd5zFmiZZb3o1HlNiZ1P1ZKG25LLVlO8qmLfutLdtBWoddw0eqEv/+CElEHW6HMuFKfFNIAX742fXP3qJh11n8wzAyMtYz2oYw6bhLl6fua1NH/Z0ZbVMsBS6Ho5ZH8kM7Nmf2+jiN/q7n0tFZB8Y8BIVhPmCHOTrDvuvPeV0i5P9v725iJDnv+47/nqqZ6XnbXZGhVruUKC7HUrySLSxkEogDKZFf4MggkEugm5GD4+gUn4IY0IZJvIARmDnn4EPeDr744KMhvwDKaGMFSAzKDmFYMROZIkOJXGoprnbntbunqnLofmaqa6p66qnpqnqq+vsBBk91TT9T1c/MdFc9/+f/PPPYwRlly2E4/xb+xvr5r6cuMUB40NkeA7gKAsoqpfFgQA/ZnothAklMBby0+P0Dbmqa0KDTwuASX6a/X03pZEanJP3ss+G9P/sw3m37PIA2bQdnAQ9KSkr/Sxu0zD6+qG5RvaI6WAybzSmdZW3afdlSmpSbIqjTNjI665XO1izz+CDgfQkospGcDc4oWw6C+R0GT+cENfdOpCfjy59vFpcd/RHwXl1JHCe6oiSRFrMY9TCShoE0jBlk0IaEhPmlxu8fcMM4e/ims4HOr7397/7iH135jbZPA2jV9ho3pECXZAORZQOTVevhcrLrb6b3ZcssDwb394YNBJQtw5IdFEUdiE12LPpwDq6y0yZd9HgrXlj/L9A7Nnjp8j43JHML8EYQGO3JmOsL/Jl2UMJwQZllpsFriiaOVfcxNqVEkmmq3frQZk0ep2+/f6DL6JaCbxoJdN7evXfr3//0b95a9M/90bHefv84fn7RPxfoio+sXvwcAAC6zDXbaT+WPtbc6QFAJdns87JT1/o8EALdFKxIgZ3BwkjHJ9L6tKeI7Yu3g86mDyDPIaOzlhq/f6A8Mjrhm0Yuyf75ziu/+uNI/0aSRieJ1lbMQkoZ6dkrwem+9M9nm+1l2I4l7Y0X9z+1tmJO1w0EAMAHrhmd29MgwGVGmNYxOtX1ZzJCFui/bEZnme1rDZ/jw+HkC/1HkNNte5Scbfvsq59Z0c5W/cfZ2Qr0yzfrH4nx2y810+hNHOerP9nMPKlNvBZ+/25+5y0iROg+7lfhm8Yvy2ygZlEl22yzvfj/KZYlAABpQ0ZHSkqXWLwq69ftx9JPrzd3jliMne3z0962aZxIb+63fRboO5ulaQdrlN322aDgRmJR03CiHnZFlHTgju3i7VFy1ma++8PvRVqreSaolz8Z6sGx9Oc/rPcf/as/Geo/vFHvMX7meqgb69LX/1/9r+WPvh/rnYP63tif2zL65U8EtbcZv/8KmJ4BPUBGJ3zTWKCzriyxYTL52ZSUy1iOo4S1+gCgJjZ4WbYk4LlY2ekaXTw8lp7eXuz5YHmM+TdGg85lpMeTtYb348ljW6oja3Pe2jy/b+9Eeveo+XNBeVHOPWXXlRVQAAAgAElEQVQYJYpCQ5lThitGso9b+H25+P5xIh3Xe4xhLD08jvXmXv0foHUf47mtWE+tBY28lncOkl60Gb9/d58c1PrjgUasGYnLu8Xx/XriMpoa79hIoPPO9kry/kk9P9sGUCkpl7GsI8jZ5zdWAHDhmtFJkHPxqkzrWDYwOownne95+9E8siexjGxwM4om711RJG2ouATqEIRGKyc5H54rZtJhRDm3DBh43C90iCw3fv9AaSO6P+CZxjI6+awAAABdQkZn+7JTNtosp3nTOh4Z6aMlpq59Mp58AUBbbHAz/d5mg59FjwHAJ0nPBohtSokkotcl8fsHlhcZnfnWTqTRinuJy2usGVf4mAAAAB2xUfH+tmo95EtP5Sid7/hPT/GYDgAwdS2Arjgy0kYkbYeToOdphqcm728b0exjjWMxjBiLZAIpYbm4ysySt519/X1ZcjAKuJh3we8fWF4sv57PBi1dS1wegU4AM/JmLQKAZeUydS0ZnYuXnsrRPs52/EfTAIENdm4k5TI66zCMpDf22jk2gG6ygc1o2lsURWcZ6nZ75jkbPelNhldC/qxQURBIUY/mLxwy24cTfv/A8mLYXfM2V/wbWPJo1PYZnCHQCWAGgU4AmLDBS0nOJRYjPWXj6dSN0fmpHNPZTvsxGZ0AusG+Z9nBGtvTjgv79mX3pae3vdba2aKvgoCsjMvYCpb72i+OpXDN9GaxtsFq22fQLfz+geXFtUM+OwOVa1nGxzekVc8uO4axdHjS9llMkBwLYMaKIdgJANJs0JKMznakg5hFUzlKqelrp/vayugEgLLC8CwjXTof0NxIzqazDafPYY1O1MEYo4C0jMqOPetwbFrfMvrCmDUaXfD7B5YXlw75bNDStQxLvPP84Ei66lE0bxhPZrW66Nyjhj4mGmsa36LNAPIR5ASAictkdHbhumej4CrwyJPReFYYzmY17cez2ZvZ9eyiqDsZnQ+Hky8Ay82+l4WZzE5pmsGuVMCTNToBeKRvGX2s0eiG3z+wvMjozFdnRucwkh7S8IUaC3SWiUoDaB/TTAPARN+nod3ZzN//V0+aPY95jsxZlqZdvy69Vl16v31uSP8/gI6wQUybqZnO8LSBzdPpu0PW6EQ9Pv4Uf1N9FjTw67UZfU0cq+5j2Iy+Jl6L1I824/cPLJ/QSGv97i6pxF7TVy1xOY0FOvnjB7rhuO0TAABgymY1pdfo3NbZGp3p79mMJ4mpawF0w3amIzXMTFUrzQY/y2R0vrG38NMEgLnI6KsmrnlwXt0/3+L3D9TPxwSynvzbL5zNzkxnal60TaBzMRijAgAAAO+EqawmGwxIl/vx2XaUmb7lIaN2AHjOvm+lA5v78fmsdBv8PK2zyi08AAAA2rXGTEqF0oHLMttYDO6SAAAAltCqyf/yiQ0A2KCmnbJWmg1w2hGRNiBARicA32WzN9ODOrL7w3MZnQAAAEB7RqwVmctmaaav3+2MVHn7CXguTmNT1wIAAMAf42QS2MyWPrJBzbyO/+z3w3CS0fn0djvnCgBlpYOdZbZZoxPov8H0+mbg+K/+ZLz4cwHQXd9770TvPygfifq7z4X6hz+1qtVA+o2vl5seZ+eZIPmnL60Z1zr/+GfWzCCQ/uUflZ+G5zf/wboGgfQfXxslb34Ql7prtXX+9Hsn+sM3Tkod51/93ECb68apjiR97EaoF24uV5iFjM58YShtTAdob+jiEouzXP+BAAAAkHQW1MyWPkp38tuO//34bN267PfJ6ATQFdn3Nun8YI70yO/BOJFYQgzopUEo3dqsVpdAJ4As4zBgYnXNaBi716tSJxol0rqpVMflWLbOKHE7P0mV6iwbMjrz7ceTAKbN4tyPz7I85z3G5RHoBDDD545uAMDidCGjc2a6Rs0GBPKmeLQlGZ0AuqAowJkOcmYHcgx9e6POeGOv7TMAlsNGKB1FZ2URY5p7z2jiWE29niaOs2rqPY79uOhTm/XptdQtCI3Ckq8jSiZRFhsYLFPvMnWkSYC07Pml60hu9ZqqE4Td/5txEer8uvKYsEHLdBAzneUpcz7rE4tBoBMAAGAJdSGjM9vBnzd9bd73yejsnoGHN8pDRimjZmEoDZToMDLaDBMNQzPz+HS/zh6T0emfvPevi6YdJfsORcoOPLPBzXlBTgAoIx3QG5ZcCvyydaqIRu5pb1XrrHGpNVekadAOuY7MJJC5obPZqOy2DXCmMzq3yR5eCAKdAGr33R9H+j8/6k8e/tMbRj/7rHuP7P94N9KHR/1ph7/9t4w+9RG3dvhglOjP3unX1dDLP8FHaR+FHbvQ3AikZwuGAv7Nwfy6Xc3ozJvukYzObhqE0sc3/Pr7GyfSD44IdqJ+6aBmGGqmzO7fDBMNVzv2AbUEqkw1SqAT84wLbhl9+pwE4L+gwkDCaJQ412uqjlWlnmudUVLtOMuCjM5iNphp+yvSWZs24ClNgpvp9TpxefTOAmgEc9tP0A60AZZD0zPXDELpSsFV3UXnks3o9GnWnbxMznRGZzoIkH4OGZ3dMoykvbHkU/xmHBPkRP0GmmRx2oxNSadZnen96XKbjM7OyU4xukHHICrKC4AS/ASQp6/9LoMVYyT/Egjqbm8fLx0IzhU7MtK2zrI2LTsWez/O/z4uh0AnAKARYezfxSgAv9nAZXoKx/QUj0OZ3OeQ0dk9D4dtnwHQPDtQYyNKdBSamQEcttR0IIct402iGl2TnWKUqUaxSEXZnwAQOARQxqkpXl3qNVEnjqUnMro6DXCWqVeljiT98ES6vuJ2fsuKjM7zbJvYbM3t6WM7Pa0t0+t4pgds43IaCXQ+ifXNq3O+/yffj36uifOAFD2O/n54LfxvvpZlX8fHBvrSiOl+nH1kw+jTT/NJjXZEgZGPI+8A+M128OdN5Vi0lh0ZnQC6wGZxHqWyOKX8abttGYzI6AT67DgTCF8PJ/vSpYsms7qaOFZTr6dPx+G1+HucurkE6lbXzGlg0KVeE3WCQDo+SXTVIQBZpU7ez0AxMjrz2cClzeq0Ac90adftTGdzRolfs2t1USOBzl/6qPmmpG8Wff+f/cnxNxKZX2jiXJbZ1rZRuBEoOEm+FG8E0ij5kjx7fHSYaDycHwgJQum96Gw7jijLls9tG+08dfEnNdPfXIw2mqAdaAOgTtnpacNQp1mcNsiZ3kdGJ4CuGGgyMCO9XaaM1+h18006o47rQiyKDWzawGc62Jn+vmvgE8By2AqMngTlP5RmMzrdP8yaqHNgjHO9KnWeyL3OVoXXX1bo6aUfWYj57LI622H+/mzW5+mARq4hL42pa5dEegHlOJ79nk+P4+jiIGccnd+mLFdiMYKKnz6TemQ0AkBZ6bXpspmb6QxOO+0jGZ0AumQwvS4sCmpmnzuU0WYbJ4rSCHpiUYoyO7Pb2ecBQBWra0bHJ4lChw+vwYoxVetoxf1D8oIu44XVQTlkdOZLBy/tti3T29myjKur0rZn0bx3j9o+gzPeNE3AKID61Ti6ZFGC0ChJij+FbHbi2fMbOCkAC7NJoBeAoyiShuH54Gb68b6MNsUanQC6JRvQzAY5s4HPyX7/7+kwQdATi5QOaKa300FPAEir0mcaJe71qtSRytdZM1J0kiTH04ugMvWaqrPMyOjMlw1y2n15wc7088t4ZuDXNeU4kQahNPTkOsSbQCdQBh8yl/O4xXdD5rafoB1oAywHnz6uis7lrUO357fFBjal8zcBRevYkdEJoEuywcx5AVDW6OwmG/T0qXMKfhpPZ7xaLXnPdFGQ85PrUlhzz98gkK6uBXphu/6ezhdqHsh2dS3QIKj/OJL07KDeGZ8mP7/+18Lv392w3h9/ylT4zDk+SZzrudaxwcSydcaZx2XqNVVnqcUXP2UZzcveTH8/vV02O/bN/Ulg0Se+BDklAp3AUrk2JpsOADBxeNL2GcxXNH1jGWR0AuiCogCnfd/L2xev0evWZdyOoaxxTgdy2eBn2i9+ItTO1uXP5yIf35BefKr+3td/8hPN9PA2cZxf/kQzI4CbeC38/t38zlv1fxiEgfuyS9FJkoQrxrjUq1LHKlsnTM0/ezIIFOS9Qc6pU/ZYVeqc1q3539nH9Rt9C7j5Ipu9WbSvalanT4FF33gT6GSURP06MHOtJP4W6nQyCFr5cByof2tT+niR0bSBDO0g/haAJuWtYZe3ph0ZnQC6Ipu5Kc1OaZu3D/45jiZTiAJ1KurbnxcA/S/frb9H9Fc/Fert/Vj/9UG971G/dSfUv3693tfzCzeMnt8Oam+337oT6j//TaTv7dd3jBe2JwG7utuM37+7Z6/1Y5qrk4H766hSJ0p1umxG5dIIowodNbZOdJIwfUYJBNyK5WVzZgOceQFPXI43gU40gykj0ZbSc1L0HO1AG6DfNuiDXqii9ermPUcioxNA9+QFN9PsPqau9U926tCuBT2/+72xfrzHBUxVH7li9KkXVls9hxLJTQCWzDB0C54cp7JjytQzmfeduuqk60UnSXIYBiYML37TS9eRZKoEkpyy7Dr0ub8Io0S6QlQpV1E252aY6DAyM39XBDkXiz9JeCUMpZgRIfBc1Q8hPryAbtlIpCNTvpTOynk2C66+fJ9Ktg152Zp5+7OZnWR0AuiavOBm3uAOpq7103pYHPDsQuAz8Pz8lsUwkp60fRIAemEQSXsV6pXNZkxSiTR11snWKztFbLrOcKX8LHOHkcxmqMSljjRp72WyZsjoLBKGk+v6w8hoM5z0T4Th5LreBjvT5VCm9BqdmM+bQGeVubzRT/wtwGcJM1gASyMbvJxXpoOe0vwpjW9t5u9/o8qdaAld/ljNZjflldn16wCgL/Le17ZirkV9lA1q2qBnF4KcEjM/+YSOYwCLEIbV142sUq/uOjYA6VKvSp2q57eMiQ2s0ZnPBjltcDOdzTkMJ/vPBTu73GnjEW8CnaifCbowZSSdlHVr63PI9OTm2Uw7lqq2Y1/awarSDrQBuqJKRicWq8x6nNlpbQdKmLoWQG8dBHzg+Cob3MwGPX3N7pysScZ9eFVV1oEDgEY4XjOcBgZL1guDZuqcU6VeU3WWCANz8mUzN5UKbJ72Z4Tm/PNwaQQ6AQAAcrhMV2sfL+NIzjpdlNGZ91xJTF0LoLfI6OyGdNBTOp/l6ZMVQ0ZnVXE8aT+fNdlP38Sxmno9fToOr8Xf49TN5XWspZYa9j2OWPexDqcXWn35O6gLGZ35okhSao3ObPamzfbciBIdhWeZn7g8Ap0AAAAZG8n57aIy/bw211bYW9I1PvPWsCOjE0BfkdHZTXkBT58E/F1VQoAYgK+C0MgE7lliUSBTdhYuG8ipUudQ5etYh5IJQ7dZwmzQ0ulY089plzrLuAwbGZ35ZjI3dZbJmS4l6Sg0M7NT4fK8CXT6PgquL3yfMjIMJBO3fRb9FQYtrNXm+d9cVUt4DTPD9oUseztItEGf2YzNdPbmRdvbLb3nDSPp3aN2jt22vOxOMjq758lR7N18PVc36PlHvVaNNE7KlxIZnV3nY5ATAABrWKFPtkqdSqp8hvK5WxsyOvNdtPyODWpmt3F5XgQ6b3xsJXy0R3QLE0lPA2NLi3/tXvKvOxpYPJuxaaemzdveDiZZnHZ/mxmdrp7dyN/fh4ApGZ3dEiXS7acDc8WLO5OJvRPpnUMGs8Avq4aMTgAAUE7VrPMq9ZzrRM2dXxyWr3MYSZthteMsGzI6z0tnaOYFOfOeY7dxeV50Jzx4/yQabPLuUbdOvEEHhghK3wTSgPfr3gkMv1f0XzqoGYbSfjwJbNpS8WyQ0z6vK3wKKi0aGZ3dEhrprz/07wKQjE7UKZ2t6bL/I2R0emc8Hdi52oX77RzBihR06PrFN0GPr6cAdJvLdKr7qWuLsvWOU58dddaRzgKQQWgUlEyomAlaOo5ejMPlnI7WBRmdF8sGOfMyOPOW4kE1XlySkdEJ9FgsHa0aBYY3balfi5kfZXvgShgE/WoD9NtGouki8ZOA5obOl/Z50jTgyahGoJKb68Y8kdFVD27y7Hk8ScjoRL3SQc2y22R0+muc6tLoatATALDkHO5nN21w1LFOOgDZhApdV5XqLBsyOvNlsziz2+nnYLG8CHSS0QkAAHxjA5c2Y9NOVbsf5z+2gVG0K+CGq5OeyCg00pOk/V4Fex4EOdGEdEdamW3W6OyGcWYct++BzzX+pHrLNPi7beJYTb2ePh2H1+LvceoUhlLoEoAMp9cXoRSW/Mw6ss9zqJNWto5JPc/1dbkeS5IOQyMpcaqzjP0AZHTmy8vinLdmJxbHi0DnjY+thE8OyOismzHG+0yqwEjy/Cawy5bxgxcAqrLrb9rSrsG5HWb262xqWzI6FyudyTROZvcVfT8u8Vn30UF+p/OPRoxMbYsNKtoymmZTNl1mzwdoQnaq2nmPyejsJt+nt41406uMW2wAvjIOnzlDm2npUG8jlnMd6zA0MoFbkMcGIF2OVaWORonGiftrWjbcNxfLy9y8qMTleRHofPD+SbS5zbsH0FdbcdKLEXFW1YBxGPZjZKBVNaOgT22Afouis9Jmb0bRNKCp2fU50wFP1CcvsJn3/Qt/TpC/RumPRpc/RyxGNvDZVAm0IW89zqLHZHR2WzbT0wdBaLRyQiebq+QkkVkxrOEGwEvrA6P1gdv70/uJpBPp+lb5epXrDKXrW+VjAbaOy+uydRQGur7lcBxJWjG67kXUxF9kdBbLTlWb3neZzM5nN/L7MdoyTqQfHPkT9PaiacjobAYjUQAAKM9maW4H0rYmwcwwnGxLZ99Lizy5wOuDoo7+dLAz+/28/T776EB6eu38/g9H0sNh8+cDAKhmGE/WosfyMCsduuAAAGDBfAlu+SgvcElGZ/28CHRKkvdzqvZEJzKp+FvonckUW7xx9w1Tp6HPbGamzeIMw2lWp1LBz9T37D4yOhcrHbhMZ3Jm92W/DwBAU2yQs6vBThNISQfP2xc+DyhPwkASPdEAgMUjo7OcRa7F+e7RQn5Mb/kT6AQ0vUngOhwA4IFsgPN0zc6w+HsEOhcrnZ2Zzugs+30AAJowjGfLLgkCKfQ4WOe7gLYDACwhMjpnffGmkjcen99PxmZzvAl0khhUv660cVfOE258HunaJNqBNkB3hOFZYFPKZG8WfA/1yE5JO+8xGZ0AgCY9Gbd9BgAwi3tuAHUjo3PWt96T+ehm22ex3LwJdAIAAPjEBi9tmZ7Odt7zLvLG3uLPte+K1uvMPu7aGp0AALTNGKOAzsrKNuT3hUeTAa8mjtXU6+nTcXgt/h4H6DIyOuEbAp1Lhg9rAADKSWdvSpNApt2fDmra6WvDkGlJ2sYanQAAuLm6Iu0FRoNIGoaidCzXFtzHYjNkXNd7JbMYANAkMjrhG28CnQTg6mf8HmgoabK+RQeXNemMIGznjyAMuvH314Q+tUPVtXz61Abot6KpabPb6ecMW3qfxRkyOgEAKO/KlUBX2j4JSJp0Gt+qOO0dgU4AQJPI6Jxl1+i0g69dS1we4UUAgLOED2EsAZu5mbct5Qc/yehsHzcJAACg7zbC2bJtSdVRsJ5p9HWs1nysun9+Cr9/YPmQ0TnrW+95Ppf9EvAmoxPN8D2Tyhgj0UkMeM/39xJgUeYFNfO+N+TatnVkdAIAgK4qu974UTRbAgDQJDI6Z33xppI3n8zuS2dtzttPH8ZieBPoDBg0gyn+FvrJBP151w4qvpYgML2ZpjuJqw9I6NPfAvovG9SUigOeUrcyOvdO2j6DekQ9fV0AAGA5FM1OQUdo/QZJIjFwcWnx+wfKI6Nz1rfek3l2azaA6bqNy/Em0In6dSHAYgIpYUQI4D2ClVgWm2Giw8jklsPQaKDZ/V1ao/Pdo7bPoB4hV7eddHVVGnh0rTqMWe8MAOCXvAAonaOLNWTqoqXG7x8oj4zOfOlsTZdtXJ43XUGGD5NG+B+cSPhbqFHIaBssyCSjk/9V9FsUScPQKAw1E8wMw8kUtfb70uQxa3QC1T0z8KuzdpwQ6AQA+O+iztEmulfsQKUmjlX3MWxGXxOvZdXU+3rsdVXtbcbvH1hKZHTOSk9dm76vdd1Gdd4EOgGgK1bjWJL7J/qkXj8+vUxgtH5CsBP9thkmGspoI0p0FBoNZc7tGyiRUoFQ1ugEqvnBkXTVozuTJ0yBDACo0XcO8iOU/3tfOo6ldcdZDo7j8/uiWHpmo/7pEv744eS11H2s330nqf0YbxxJbxzVf5zffSfRflRvm+1HzbQZv3+gXo89HXxJRucsO3WtlZ2Slulq6+dNd0IXplVF/QIjxfwtwHNRWO2PNAoD6aQ/2V7HK3wqo/+iSKcBzcPISOFkXzwtbZDTZsyT0dm+MtO+/Gg0+criZq09w0h6SPsDAJZEUcf140x5WU1kpg3jZo61H9V/jCZfS1PH6VOb9eW1AH1ARuesdEandD6QSQZn/bwJdKJ+XfigNkaijxgA4AObwWkDnHbKWpu5aYObdspam9WJdpW5aSCgCQAAAABANYu4p769e+/WSONnFMTXT3fGwQ/XtPrBX//8vbcuf4TmZDM60TwCnQAaUTEJsndoB9oA3TFQMhPYtI+z308/D+2LOjTl6MPh5AsAAAAAgK6omtF5e/ferVEw/Oxg787ndSANCp73mT/4PQ2vvP4Xa/HgO10IemYzOuviWyatT4PIvQl0Bh3INuw6ExjvAwxxIAX0EwMAOoo1OtsXenN1CwAAAABA/7gGuF587dVrHx48/orZu32jKLiZNdi783lJn3/h/t0HT29d+/1vv/S1Rc3svnBNZHTubPs17e04kX5w5E+wk64gAICzKG77DIB62bU201mbZWwbRusAAAAAAID+csks3Nl95aXDB7e+vO54jOMrrz9Y37tzQ5IOH9z69Z3dV/74zZ//t685/phGNJHR+cFQ2vYsmudLkFPyKNDZhfUj0Qz+FuoTepbeju7yPTsc9bq9e+9Wdl8XphJxlQ1w5gU8s9PXKiHQiW5Zlv9nAAAAAMBilA1w7dy/+3Ki+KY0CVza/et7d26kH+exz0k9987O/bvX3/zSb3/9EqdeiyYyOp+MJ1/I502gE/XrQmDCGJXMmQEANOXF11699ujg8RcS6eb63p0bOjj/nM/8we/p+MrrD0wcvO7rCDtX2SCmDXLm7bPP92kakWU15kLiQrd3790aBsNfvOj/eRAPvkHQEwAAAACQViajcxLk1GmQMydwKel8QNOW9jnp5ybSzZ37d1/2LdjZ1BqdKOZNoNN0IAiHZvC3AAB+sAHOwwe3Pl9mDYXplCI3PvMHv/fl4dZfejuliIv0FLbSbFZn3vfI6GwfweZi6XVRLpo2aPr//Csv3L87d5QtAAAAAGC5XJTRmQ5yWnmBy7z9F2V6JtLN27v3bvk0KLeJjE7MR0gJAACcM11D4deni787Ob7y+oPBwee+/ML9u7/24muvXqvj/JoylJkps1PZZr9HkK190UnbZ+Cnnd1XXvrw4PFX0qNny9xI2jVRAAAAAACQ5md03t69dysb5Fy0YTD8RZ/6m754k0kq2+ZNoNMYvur+6oq226nPXwBQxs79uy8PDj735aJgSF6Z3k4HUj48ePyVvDUAuyKbtXnR95g2tX2hN/OV+OP27r1bSRDfkWb/b/OmBsr7avfsASy727v3bvnUkQUAALDs5mV0joLhZ11/npHeu6jMfj06ePyFKudeh2+9J3reW0ZXEAAAOPXC/bu/lo7V5a2fkLc/L0vM7puOtPv9b7/0tceNvZCaZNflzCKjE76x63HmfW9eRmfe/z0ANMFOnX86q8SBdHjQz/XAffDnfzXSwX7sVOfv/Z2LJkA/70//57HT87e2A/3MT6051dnbi/W/vjNyqvPssyv6iefcusZoM9qMNqPNXNBmtFmVNvPVo2GsYSw9PDE6TsyNnd1XXlrT6gdbV9Yfffulrz3e2X3lJSk+DVBWYaT3EulmNthp96W/58sUtqzR2T5vAp0moGcQ/B3ULaB9AcwxXUNB0mzgw3U7b9+HB4+/0odg57wgp0RGZx/Nm5LnonVJ2jZdkzM3yHkRgpx+ePG1V68d7B0/ZR/bDoQ2zwmo03Tq/C8XrQ1u1wN/4f7dO4N48A0fOra6LvBmnq9Zvp6X5O+5+Xpekr/n5ut5Sf6em6/nJfl7br6el+Tvufl6XpLf51aXKJEeHsd693DS4TFOpFWTSKuSgvj6SMPro4OhdnZf+aGC+Lp0PiiZDk6mt9OP0/vyAqXZwKd0mj36Vj2vvDzW6GyfN4FOAPVbjWNJc3psASytnd1XXkoU17qGwnRaka/XeYy2kdHZvkUHm6+uSE/nDCb+cCQ99DzQ+Whv79MmOH8DaddLSW/DLzYT9/DBrRvptxWb1Tbc+ss/JqMNfTMZcDW5FslOr5197nTfr+zsvsL/wiX52mHb1HkFFXrFaLMKdWgz9zq0mXsd2sy9Dm3mXsfTNqvLwTjRm8fSeJRIq4E0jrW6FmgYSyvK3BBPg5xS8RS02e28x6XFwQ8VxNdffO3Va20PBiWjs30EOuGVMJAit+x/OIjCJfs0BlDKNOvrzqJ/bnb0njQJqPa5Q5KMzvYRbJ6YTv04M5q2zE0m2rdz/+7LZu/25+dNcDU4+NyXX7h/987TW9c6nykPSKezSpwGOfPKAndu7977gMzO6oLAaHWt/g9P12NUnY2oiddCm7mjzdzRZu5oM3e0mbum2swHj4axvjc00jjW+nqg4+Oz8tp6oIMyA3+nwcjcx3a7TClpZjv1eDoDTqv3RGR0ts+bQCfxF1j8LQDAYtmpD0caPyNJ6fUTpEnWl4Lzgcl5pXR+upE82TozF7g9RJCtfdFJ22fgB/t/LRHM7BK7TnJRYCe7NvKHer0X04Jjud3evXdrqGHl7PLpOsT/aYGnBAAAsLwC6WCY6Psjo/VQp5316+tn5XE2yOkYnJzZTlX3ZTsAAAb0SURBVO+b9zhbb/p42tf1Vh1NURYZne3zJtAJAAAW6/buvVujYPjZRwePpdQgkqGGd0YHw/d27t/VWjz4zigYXi+aVqSoLAp45gU/s4/7ntWJdoVc3c7K3nDCW9MpxOc+J29t5A8PHn9FBHnQYaNg+Nm8WSBc7Oy+8tJTV678XzuwKzuoC8WCUDINDDZ2PUZQccUV9+O4j1KjzWgz2qyJ49BmtFkTx/G3zdoURdL7J/OTkdZDTTI600HMdHAyuy8brCx6fpXHmvR/tTnDBxmd7aMrCACAnplOWfmFkYan+9IByJyF250VTX1ZKnOsI8GWVTOZitalBHzw4muvXnu0t3f+xjHnhjBX3sha1Go6MOW6VG3tVDuA5PbuvVuSRIAHXZR3nVJaEF9/dPD4uh3YlR7U9dTWtf/O/8N8oan/IqaJYwShaeQ4Em1WBW3mjjZzR5u5o83cNXWcVgTSk6NYx7HReiBplEhr5nwplZu61lrwfeWaVj9Iz1o20viZ27v3WrsPIqOzfQQ6AQDomUcHj7+Q3VfL1JV5aysUbWeCJj4sFg/00TTI+elL/RDXwCguzd6kVw70BPH1nft3X7YDXIYHw5s79+++pzj4IRn06Iyia4kK0v9Djw4ef+H27r3vLOAMe2nNSMcVM2dcuGbnVF3+rGoWkAvazB1t5o42c0ebuaPN3DXVZq2JpWEsrQfSphIdrplzpSsbiHQpL/qZ2efZ7bbW6ySjs30EOgEA6CuXQGTRdtE6C1L+2gpF25l9B3vHH6jlxeLnsdmZRdma2f328XafR3Z2xDhp+wzac3v33q1He3szo1rbPieUtOBM9/S6yEwXDu/lTWGW3U6bd52R/l9KPa46g8UyiEKjIKj/wzNwnOYvqjCdYJXjVJl+kDZzPwZt5n4M2sz9GLSZ+zFoM/djNNVmTRglUpR5Kdmm31SiQ02CnPax9SOdBRjnHicVlMwGM4vK9HPygp/zjtvGNLZkdLaPQCcAAH1TNviY3U7XtdvZReDTdcp0TBas0TDS+JkXX3vV66kVbSAzHdQs2rblUP244emyZZ1C+MXXXr12sHesvFGt8xTdQKZH1NZ31pCm09bG44uzZ4sGn2QDPNl9QXy97TVrgIWad51RYg0nzFox7h3DVbgeY6VqNo/jcbYC9wPRZrQZbVb/cWgz2qyJ4/jcZnWJEun4JNHeyfy+i3RAc7NEP8e8TEyXIGXec8o8P23ryvqjpmcRayKjcxBKVz2K5g1j6cm47bM441HTAACAhXIJRJb5GWXrlPRob+/Tt3fvfeBr53s6YHbRti0HWtIom0eik7bPoB3TKXqcFd1AZkfUoh63d+/dKh1MLgroFAwoSZfTY7y1kJMGFuyiwRZFpa3LgIzLCyp09Pp4jL4dp0+vpanj9Om1NHWcPr2Wpo7Tp9fS1HH69FqaPM6ijRLpYBRrGEuDQIWllL9vnkXdN25dWX90sHf8VLqUJve7RfsWcdzLaiKj8+Mb/g3sHsbS0GWt1hoR6AQAAKUVdS5etLbCvNF9vmcaZaeuzZvK1iKjs33hkl7dZm/wsjeCNhCa3ka78rJwL1I2+JPn9u69W9LZ34rPGfVYTvMGWxSV2e0iBEOLBSvNrGnmeoyg4ud5I6+FNnM/Bm3mfgzazP0YtJn7MWgz92M01GZ1OBjFks4Cl0Vl0b4iRcHIqoFIWyddd94+HzSR0fmDI/8yOn0JckoEOgEA6K1sp/ciFoCfF7zMW2vBPr5onYWmpxVxkbc+ZxEyOtGGF1979Vp2X/ZG0NcbwmVWJQjtEvyZd9yRxs/c3r33wdaVda+nEMdyKBqokd6+7KANstPna2KJ8SaOEYbNHEeizaqgzdzRZu5oM3e0mbumjrNIR5MYp8KVQNFJ7FzaulYYJ7qzvZKkr9Pm3YP2XRMZncNIeuhRYNE3BDoBAOiZMgu2V+0svyiD4qK1F4rqTzsrO9/ZTkYn2vDtl772OC/YWbQfflhEELpo1HTZgGlf3nvRL/NG71f5f/FpWjMfBaFpKJvHrVfY9flV64UV1lmjzaodgzZzPwZt5n4M2sz9GLSZ+zG6mNEZR8lpoDJdrhpJ03J1dbI/W66vBYqyXR2h0f5o/L5ULaBp71XnlZLfg+LTmsjoxHwEOgEA6CkfsheKOuDzpjHpygXsPGR0tm+8pLHmov+dsv9TeTeTp/u++y8WeaqYyvvd5N3kz/sZRUEgl86GPrz3ottc//4u6hTL/t8Q5AQAAF2VJNJWkuidyLz9XJg8/95xktxcN8b18YfGvL0yij8ZrhgzNObta6Gefxzp7TBOnj8x5u2tYPJ4kCTPD42ZKY9Sj58Lk+fficzbK+P4kzKXWw7D1i0qs9s+ayKjE/P9f/Sj5klq7HcBAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_1", "w": 1850, "h": 1624, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABzoAAAZYCAYAAADubMTAAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAgAElEQVR4nOzBAQEAAACAkP6v7ggKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA2YMDAQAAAAAg/9dGUFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVSbs+U0AACAASURBVFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVWHvfnLaWLM+AJ8yycfgioFbvQ/bYtKjlrIRC3YQVgI7IGIjkXp0J5ZhH61bg4gBjaj3G1DOJQSwDfW/nkeK6sa3UnUwNn/qV+e8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPRe1nYBAAAAdMs6T/Mi4iQibo6n2WXb9QAAAMBLPrVdAAAAAN0ziZgVbRcBAAAAb5i0XQAAAADdNImYtV0DAAAAvEbQCQAAwC+KiEXbNQAAAMA2gk4AAAAAAACgdwSdAAAAAAAAQO8IOgEAAAAAAIDe+dR2AQAAAHTXOk/zxTS7brsOgLFZ52nedg2wiyJiMYlY+3kBgDYIOgEAAHhu1nYBAGNXhkfLtuuAbSYRZ0JOANpidC0AAAAAAADQO4JOAAAAAAAAoHcEnQAAAAAAAEDvCDoBAAAAAACA3vnUdgEAAAB0y32K2WH2+N9FxCIirlstCGAg1nma77pvUWchUK3zdZ627vTjIeLoIM4W08zPFQBURtAJAADALzYhJwDVWkyz61WeTu9TLLd9rTWGjaG4SxGHWZz9+58CTgCq52cmAAAAAGjI8TS7/Nc/si9FxNXd9iY46K0fDxERcfavf2RfdHECUBdBJwAAAAA0TODJUJWv57N//1PACUD9BJ0AAAAA0BKBJ0OhgxOANlijEwAAgJ/WeZo/e2jWSiEAI3M8zS4j4nKVp9N442vv7UPMjg4iioibePwTETGbRMy2PXb7EMvn//Ydj109qaXSxyYRy4j3P/bjIeKPg+oee89zPJTn/aX9In6+TgGgMwSdAAAAANAR24KkMghdRsTNZt8n4ei2x2YffWwSsd5061Xx2NOPd52n5fPnYJ/H/jj4u97yxp0PPfbO57i3z/sO//Y0AKBjjK4FAAAAAAAAekdHJwAAALHO07yIWBSP3Rs/3aeYrfJ0ERE3T7s6AAAAoG2CTgAAgBFb5en0PsVmZN1vDrOIeByjN4uI5SpPN5OIbwJPAAAA2mZ0LQAAwAit8zT/86/0fRKxLMPMnUweA89z63QBAADQNkEnAADAyKzzNI+I830Czucmj92dwk4AAABaI+gEAAAYkU3IWcWxyrDzoopjAQAAwL4EnQAAACOxztP8LlUTcm5MImZleAoAAACNEnQCAACMRBFx8pFxtW+oNDwFAACAXXxquwAAAADqV3Zdzuo6/ipPp8fT7LKu4wMA7XtpisNiml23UQsARETUcy8vAAAAnbLK0+kkYlnX8e9SxL/+kX2p6/gAY7PO07yIOHn++O1DzI4OWigIXncm7ASgLTo6AQAARqDOkDMi4jDT1QlQpSJiMXmhE1/ICQDwN2t0AgAADNwqT6cNnaq20bgAAADwnKATAABg+BoJIF/qPAIAAIC6CDoBAAAG7j41F0Cu8zRv6lwAAACMm6ATAABg4A6z5s5VRCyaOxsAAABjJugEAAAYsBY6LI2vBQAAoBGCToAn1nmaG7cG7fD+A6iHDksAAACGStAJAABAZZpcDxQAAIBxE3QCPKPrAdrj/QfQf02uBwoAAMC4CToBnhCyQHu8/wBqo8MSAACAQRJ0AvzOxUBoj/cfQIXWeZpPWvjausrTadPnBAAAYHwEnQC/ErJAe7z/ACp2l+K8jfNOIpbrPM3bODcAAADjIegEeOb2QdgCbfH+A6jOKk8Xba6XeZfiXNgJAABAnQSdAM8cHbRdAYyX9x9ANVZ5umhjZO1Th1lEEXHSZg0AAAAMm6AT4Im2LwjCmHn/AVSjrXU5XzKJmK3ydNF2HQAAAAyToBPgBcasQXu8/wDeb52neVvrcr6mDDtP264DAACA4RF0ApSEK9Ae7z+AahQRJ22uy/maScTS13oAAACqJugEAAAYgC6sy7nFubATAACAKn1quwCALioiFhFx3XYdMEbefwDvczzNvr70eBsB6GKafWnyfAAAAIyTjk6AUhmuAC3w/gMAAAAA9iXoBHhZl8e+wdB5/wEAAAAAWxldC/C3X8KVdZ7mi2lmfCY0w/sPYEB8HQcAGD7rrw+bn+fpC0EnMHqrPJ3ep1g+bXEv17E6X+Xp6niaXbZVGwyd9x8AAAD0UxFx0vRa8DRnnaczYSd9YHQtMGqrPF1MIpaH2cv/fxKx/POv9N0dalA97z8AAAAA4CMEncBolSHL1rvODrOIuxTnwhaojvcfAAAAAPBRgk5glHYNWTbKjjNhC1TA+w8AAAAAqIKgExidfUOWp4qIk4rLgVHx/gMAAAAAqiLoBEZllafTjyySPomYrfJ0WmVNMBbefwAAAABAlQSdwKjcp1h+9BiTiKURmrA/7z+A8VhMs+u2awAAAGD4BJ3AaKzydFGu9fdhPx7ivJojwTh4/wEAAAAAVRN0AqOwztP8IyMznzs6eBzDWdXxYMi8/wAAAACAOgg6gVEoIk6qPmYVYzhhDLz/AAAAAIA6CDqBwVvl6bTKbrKNw0xXGWzj/QcAAAAA1EXQCYxB5SHLhq4y2Mr7DwAAAACohaATGLS6usk2dJXB6xp6/13UdXwAAAAAoNsEncCgTaL+ji9dZfCyJt5/dQapAAAAAEC3CTqBwWqq01JXJ/yuyfeErk4AAAAAGCdBJzBkjXV63T7o6oRnGnv/3SddnQAAAAAwRoJOYJDWeZo3OdLy6ODxnE2dr8lzMQxNvz6bfP/pqgYAAACAcRJ0AoNURCxaOOdJg+dq/OODXbX0+tTVCQAAAAAjI+gEBmkSzY+SNT6TLisiFk11dbbx/ptEzHQ6AwAAAMC4CDqBwWkr7DjMmjl3eQ6hKp3UZtio0xkAAAAAxkXQCQxOm2GHoIUOmzXx+mz5PeAGAAAAAAAYEUEnMERthh2NnNuYXDqstdfmRNAJAAAAAKMi6AQGp82wo4lzFxGLw6zuszA0tw/NvC/aDhut0wkAAAAA4yHoBAalCyFHF2qA544OIqLmELILr33jowF+tc7TvKmbXZ6ft+lzAgAAMD6CToD+mUW4gAgAvG2Vp9OIOC9vdmna+SpPF62cGQAAgNEQdAKD0oVuri7UAE9tQvG6x8p25LVvnU5g9NZ5mv/nv+n7JGLZZh2TiNmff6Xvbs4CAACgLoJOYGi6EHLUWsNm/FxHQiUAoEPKLsq2ujh/U64rrrsTAACAWgg6AXqmKxcu6aehd9XU3bUK0FXrPM3//Ct97+rXQd2dAAAA1EHQCdAT6zzNy7W2IiLiPsXSxUK2WedpXkScbP5eRJzU+Lrp5MV1gKHbdHGW3ZOd9aS783TLrgAAALCTT20XALCvt0KaoslC3vBWjYtpdv2e492lXy9gPrlYeHM8zb6+o0wGrrzwPXt6V1PZ6XO+ytPV8TS73PeYY3z/AXTV5ueDvt29OolYrvI0m0R883UZAACAjxB0Ar1TRJy8NpatCxf6NkHSS/+viLiJiL1CybLrYflal8YkYrbK04Wwk6dWebp4a3xheZE59g07u/7+K734/it9aawKgBpt+/mg6zY/L63zdCbsBAAA4L06dE0SgOfWeZpPIpbb9tuEnU3URPdtCzk3yrDT+ECAnim/zm/9+aAnzv0MAwAAwHsJOgE67C692Zn2izLsFFqN3CpPp7uEnBuTsNYrQF+s8zT/86/0fZ+v832wuWHL9yMAAAD2JegE6KhVni72HUcntBq3dZ7m92n/Dp8i4qSGcgCoUPn9/byvo2q3mUTM7lKc+zkGAACAfQg6ATqoHFn7rm4NodV4FREn77kArhsYoNvKr9E7T3noq/J7mLATAACAnX1quwCAfU0ivhURi6eP3T7E8uigrYpeVkTcxOOfnyYR613+7Y+HOH/vx7MJrY6n2eX7jkAflReF3z3KsOwE3fqaeen9FxGzro1R/PEQ8cdBXLVdB8BH7bru8sCcr/J0czzNvrZdCAAAAN0m6AR6ZzHNriPi+uljqzzNonsXAW/eEzZW1MXQteeCmhURJx8Z03CYPXYMbXvNvvL+O42Oveb+OHjf+w+gS0YackbE3+t2CjsBAAB4i9G1AB1zlz4+ms4o0nH5yKjjp96zvicA9RhzyLmxCTvbrgMAAIDuEnQCdMgqT6fvWWPxJZMQWo3Fj4dq1m0ruzpdUAZo0TpP8//8N30fe8i5IewEAADgLYJOgG6p9KKmrs7hW+dpXuX6tPfJhXWAtqzzNL9L71+ne6iEnQAAALxG0AnQEVWNH31GaDVwRcRJlcfbrNVZ5TEB2G4TclY12WFohJ0AAAC8RNAJ0BFVB1YRjxcF13maV31cuqGmcDxCQA7QKCHnboSdAAAAPCfoBOiIutbiKiIWdRyX9tX1uRWQAzSriDgRcu5G2AkAAMBTgk6ADqg5VNKdN1y1fW4F5ADNWOXpoq6bnYZK2AkAAMCGoBOgA+oMlVw8Ha77VOvn1usGoGZCzvcTdgIAABAh6AToilovchpDOjzrPM2NOQToLyHnx5Vh52nbdQAAANAeQSdAB9TcmWcMKXtz8R2gPqs8nfo6W41JxNINXQAAAOMl6AToAJ157KuJ8NqFY4DqrfM0n0Qs265jYM59zwIAABgnQSdAyxq6MKdrBAA64C7Feds1DNFdEnYCAACMkaATAACgAas8XZjiUI/DLKKIOGm7DgAAAJr1qe0CAMbkpU6DImLRxF0nL517Mc2uGzg1H/TK66b2Lt0i4mSdp2/PH/e6AdifdTnrN4mYrfJ0ejzNLtuuBQAAgGYIOgEaVIaav6zL1UTIWV5YfWlU3pcGTs8HFREnzy+Ot/W6KSKuIkLQCbCH8oYV63I2YBKxXOdp7aYcAACAcTC6FgAAoEY/HqzL2STPNwAAwHgIOgEAAGqyytPp0UHbVYzL0cHj8952HQAAANRP0AkAAFCDdZ7mz0fW04xyhO1va1wDAAAwLNboBGjQ8TS7jIjLzd9Xebp4vvZizc6sWdU/x9Ps69O/r/P0valzFxE3z88PwG7KNZZpSRFxEhG+hwEAAAyY37sBAAAqVnZzNnkzE89MImZG2AIAAAyboBMAAKBidynO266BiPtkdDAAAMCQCToBAAAqtMrT6WHWdhVERBxmj5+PtusAAACgHoJOAABgdNZ5mm/+VH3sSegi7JL7FMs6Ps91vX4AAADYnaATAAAYnSLiJCLOi4hFlcfVPdg9h1lE1Z/n0nn5BwAAgJYIOgEAACpy+6Cbs4uqXqtTJycAAEA3CDoBAAAqsM7T/Oig7Sp4ibU6AQAAhknQCQAAUIFyHC4dVVe3re5OAACA9gg6AQCA0bl9iFmVx1vnaT6Jao9JtY4OhJIAAABDI+gEAABG58mI2UrCySJiUcVxqJeuWwAAgGERdALUqGtdA0XEYltN6zzNd6l71312OV5V++xbV5M1NXm+qr11zq69xgHaUtdYVKo1iZj53gUAADAcn9ouAGCoJhHLiFiu8nRzPM2+Pv1/qzydlv+/lZr+/CvFYRZni2l2/ayuiyg7W9Z5iiLi6niaXT7b52ftr+2zztP8LsX5s3/32/PwfL///DfF0cGvda3zNP/x8Pc+b9U++bX2rc/7Kk83k4hv7z3ftufq+cdX1XPQpPI5PV/l6cXXQkQs3TUFjJ3grF/K7tvrrTtuOYbvfwAAAO3zuxlAzSYRszIQioifa3i12vVxmEVExPnTC7NPg8KNScTy2T6/BbSTxzD3l48vIs7Lczzdb1aGg6/ud3QQ8TwgLSJOnowXfK320xdq/+18L9Q+e2GE3fkO53vxudr2PLxU013a/zlowy6vBYCxMra2dypdS9XnHwAAoD2CToAGPA2EurQ21ObCXBkCvnjR72nodp9eDrZ2/fienuO1/Q6zn52Cb9b19N+/Frjtcr6nI+yeBpWvne+tmmLP8xURi+eBcMTuz0HTdnktAIxUJ75Os5uufF8FAADg4wSdAA3ZhFtduri2S0feJohb52n+Uii3sevHt9nvPr253yzi7Q6Jzb/fZV3LbXU9Oc+r+2ypN56f46Pnix2eg6bt+loA6Lqn3ztuHz7+fblL39vZzVs3NwEAANAfgk6AhnQpsHpuW227rD2268e32W/HoOzVC8ebf7/tvFU977ueb52necVrtXXq4rl16ICh+ehocF8XAQAAoD2CTgAAgHfq8o1MvO72wQh2AACAIRB0AgAAvF+nOu/ZzUc7ecPnHQAAoBMEnQAAAO9kfc7+qnDssNcAAABASwSdAAAA72B9zn4zdhgAAKD/BJ0AAMCoPA+4BJYAAADQT4JOAACAd9AR2HtGzgIAAPTcp7YLABiTLnaMrPM0L7bss7mQu+3umF0/vl3226WuXfZp+nz7PFdVfXxN2vXjA4Cu+8j6qtZmBQAA6AZBJ0BDJhHLePzTNefbQqvJDnXv+vHtsl958XBrXbvss+P5lhGxrPB8O9X01rH2eA4atcvHBzAiwi7i9sHrAAAAoC1du34KAAAAvXF00HYFAAAA4/VpnafvbRfBy4qIq+Npdtl2HQAAMDA68IiIxzHxi2l23XYdAADA8JXLVC3armNojK4FAABGYZWn04iYvbC+4vkqTzcRceNGQ96yeQ09f/zPv9L3z1lcTSLWglMAAOAlRcTJC7+P8kGCTqB3Vnm68A0BeMskYvbS1IrFNPvSRj1Au1Z5Or1PO62NPPvzr7Q8zOJMWMVT215Dh1lElGtwr/JkMg8AAEBDrNEJAAAM1ipPp5OIZRlEbVXud1527sHmJrudX0OTx7Dzot6qAAAAiBB0AgAAA7UJqN7zb4VVRLx/ksgkYub1AwAAUD9BJwAAMDhlJ+eHRt2XY7DnVdVEv3x0uQRhJwAAQP0EnQAAwKCs8zR/byfnC84rOg4d9NparFUE5RE/w05jkAEAAGoi6AT66KZ49ucutV0S0CV3KeL514ki4qbtuoBmFBEnVR7vja48X1cG6j5VFpRXeiwAAAB+9antAgD2dTzNLp8/Vl6A/PBd98AwfM7i5niafW27DqB55ajZSn8mqKKzj+758fDy42U3Z2UOs8djvvQzLAAAAB+joxMAABiMImJRx3FfGj86iVjXcS6a8cfByx25dXRg3j7o6gQAAKiDoBMAABiMCtfm/IWgapB+CzpXeTo9zKo/0dHBz25jAAAAKiToBAAABuGlrsuqvBRULabZ9WvjT+mt2sYUV712LAAAAIJOAABgOGpdS/OlsbivjT+l+14aPVzneqz3yVqvAAAAVRN0AgAAg9BAkPTb8ScR32o+JzW4S48duU8fq3u07GFmfC0AAEDVPrVdAAAAwEc1ESA97fYrx+TOinD3aB99zuJmM+p4ErFeTLPrImJR9+ey7Aq+3rojAAAAOxF0AgAA7Gidp3nZCTirc8wp9So/d7OIiOLxoevw+QQAAOgdNx8DAAC999L6mX0+D81raA1NYSoAAECFBJ0AAMAQCJD4kMOs/nPoAgYAAKiWoBMAAGB3gqoBamKN1zbOBQAAMHSCTgAAoPea6pS7fRB0AgAAQFcIOgEAAHZ0dNB2BdTB2qsAAAD9JOgEAAB6zShQ+kSoCgAAUB1BJwAAwB4EqwAAANANgk4AAADGztqrAAAAPSToBAAAAAAAAHpH0AkAAAAAAAD0jqATAAAAmmNMLgAAQEU+tV1A1xQRV8fT7PKjx1nl6XQSsXzlf58tptn1R88BAABjtM7T/Onfi4hFk3dwFhGLBk/H8Ny0XQAAANC8ScS3XX6fvH2I5dFBExUNg6ATAADolSLiZPKkK67pMTXPb2jc9WbJMqA9r60wooi4mUR822G/n6+hN25QrcUkYrnO0y/nXEyzL03WAAAANK9sgNvaBLfK0yxMgtmZoBMAAIDB2GV6zipPTZQCAABAzazRCQAAAAAAAPSOoBMAAAAAAADoHUEnAAAAAAAA0DvW6AQAAAAAAIAarfM0LyJOtu13n2J2mDVQ0EAIOgEAAAAAAKBmk4jZtn2EnPsxuhYAAAAAAADoHUEnAAAAAAAA0DtG1wIAAL0yifhWRCyePDTbZfxPVYqIm3j8s6ln3dS52epm+y6P+xVP/tLkGjh3KeJztnOdAADAQCym2XVEfNm23ypPF03+jtt3gk4AAKBXyl8Orzd/X+XpNJr9JfDmeJpdNng+Kvb887fK00U09Br6nMXN8TT72sS5AAAAhs7oWgAAAAAAAKB3BJ0AAAAAAABA7wg6AQAAAAAAgN6xRicAAAAAAADUbJ2n+bZ9iiYKGRBBJwAAAAAAANSoDDnPt+1nFOt+PF8AAAAAAABA7wg6AQAAAAAAgN4xuhYAAAAAAABqVkTcbNvnPsXsMGuimmEQdAIAAAAAAECNFtPsOiK+bttvlaeLiJjVX9EwGF0LAAAAAAAA9I6gEwAAAAAAAOgdQScAAAAAAADQO4JOAAAABmESsW67BgAAAJrzqe0CAAAAAAAAYMjWeZoXEYtt+90+xOzooImKhkHQ+cztQyxXeZpVcJxXX4hFxMkqT1uPMYn4tphm1x+tBQAAAAAAgHZNIpbb9hFy7kfQ+Uz5Avpw0PnWC3Gy4/HLZF/QCQAAAAAAAM8IOqlE2XJ90nYdjNd9itlh1nYVQFfcp5it8nTRdh2Ml8kcAAAAAPUTdFKJImKxa6cq1EHICTxVfk3wfYnWmMwBAAAAPLWYZtfrPJ1t26+IOJG37E7QCQAAAAAAADXbZfrTKk9NlDIYk7YLAAAAAAAAANiXoBMAAAAAAADoHUEnAAAAAAAA0DuCTgAAAAAAAKB3PrVdAAAAAAAAAJ1yvs5T2zXAVjo6AQAAAAAAgN4RdAIAAAAAAAC9I+gEAAAAAAAAescanextnad5EbF49vCslWIAALpptsrT6dMHJhHrxTS7bqsgAAAAoHVn264NrPJ0MZG57EzQyd6KiBNvMgCA15U/K/3y81LxuBF0AgAAAFTE6FoAAAAAAACgdwSdAAAAAAAAQO8IOgEAAAAAAIDesUYnAAAAAAAA1KyIWKzztG0f9iDoZG/H0+zr88dWeTqdRCzbqAcAoGuKiKvjaXbZdh0AAABAd5Q5yptZilGs+/F8AQAAAAAAAL0j6AQAAAAAAAB6x+haAAAAAAAAqFkRcbVtn9uHWB4dNFHNMAg6AQAAAAAAoGaTiPViml2/tc8qT7OImDVUUu8ZXQsAAAAAAAD0jqATAAAAAAAA6B2jawEAAPYwiViu87Rsuw5edL7OU9s1AAAA0BAdnQAAAAAAAEDv6OgEAAAAAACAmhURJ6stU2juU8wOs4YKGgBBJwAAAAAAANRsEjHbto+Qcz9G1wIAAAAAAAC9I+gEAAAAAAAAesfoWgAAAAAAAKjf2WKaXb+1wypPF7uMuOWRjk4AAAAAAACgdwSdAAAAAAAAQO8IOgEAAAAAAIDeEXQCAAAAAAAAvfOp7QIAAAAAAABg6IqIxTpP2/ZhD4JOAAAAAAAAqNkkYhmPf97ahz14vgAAAAAAAIDeEXQCAAAAAAAAvWN0LQAAAAAAANSsiLjZts99itlh1kQ1wyDoBAAAAAAAgJpNIr4tptn1W/us8nQREbOGSuo9o2sBAAAAAACA3hF0AgAAAAAAAL0j6AQAAAAAAAB6R9AJAAAAAAAA9M6ntgsAAIChuX2I5SpPs7brGIvbh5gdHbRdBezm9iFmqzydtl0H0At+lgCAgSkiFqs8Ld7ax++4+xF0AgBAxcpfSFycbIhfAOmT8vW6bLkMAACgBZMdfhfwO+5+jK4FAAAAAAAAekfQCQAAAAAAAPSO0bUAAAAAAABQsyLiahKx3rLPycRyODsTdAIAAAAAAEDNJhHrxTS7fmufVZ6aKmcQjK4FAAAAAAAAekfQCQAAAAAAAPSOoBMAAAAAAADoHUEnAAAAAAAA0Duf2i4AAAAAAAAARuB8nae2axgUHZ0AAAAAAABA7wg6AQAAAAAAgN4xuhYAAAAAAABq9uMh4o+DuHlrn/sUs8OsqYr6T9AJAAAAAAAANfvjIK6Op9nlW/us8nQREbOGSuo9o2sBAAAAAACA3hF0AgAAAAAAAL0j6AQAAAAAAAB6R9AJAAAAAAAA9M6ntgsAAAAAAACAqhURN5OIbxUcZzGJWL7yv892Pc4kItZ5mm85F3sQdAIAAAAAADBIi2l2/dFjrPMU8UrQuevxy4DzfNt+RrHux/MFAAAAAAAA9I6gEwAAAAAAAOgdo2tHapWn04oPOav4eAAAAAAAAINRRFxt2+f2IZZHB01UMwyCzpF6Y9FcAAAAAAAAKlSu5bl1Pc9VnmahuWxnRtcCAAAAAAAAvSPoBAAAAAAAAHpH0AkAAAAAAAD0jqATAAAAAAAA6J1PbRcAAAAAAAAAQ7bO07yIONm2332K2WHWQEEDIegEAAAAAACAmk0iZtv2EXLux+jakVpMsy9FxFURcdN2LQAAAAAAALAvHZ0jdjzNLiN+tksvbh9ieXTQdlUAAAAAAACwnaCTWEyz64i4jojLTeh5n2KpPRoAAAAAAODjyizmy7b9Vnm62GXELY8EnfzihdDzxBsKAAAAAACArhF08qoy9Pwa8XO8rdATAAAAAACAThB0spNN6LkZbRsRM6EnAAAAAAAAbRF0spcno203XZ6L24dYHh20XBgAAAAAAACjIujk3Z6u57nK0+kkYtl2TQAAAAAAAF20ztN82z5FE4UMiKATAAAAAAAAalSGnOfb9ps0UMuQeL4AAAAAAACA3hF0AgAAAAAAAL1jdC0AAAAAAADUrIi42bbPfYrZYdZENcMg6AQAAAAAAIAaLabZdUR83bbfKk8XETGrv6JhMLoWAAAAAAAA6B0dnQD0Wjnu4WYSsX7y2CIiZsY8AAAAAAAMl6ATgF55GmyW4x5e8vPxdZ7mzT3wbgAAHbVJREFUm+BzYuQDAAAAAMBgCDoB6LziMdj89kaw+ary3/wWfN4+xPLooNIyAQAAAABokKATgE76SLj5lifB5+Um9LxPsTTiFgAAAACoy5PJc2+6fYiZBo3dCToB6IynY2mPKw44X/JC6HlivC0AAAAAUIdJxHLbPkLO/Qg6AWjdpnuziXDzNWXo+VWXJwAAAABAPwg6AWhNEXHVVPfmrp52ea7ydCrwBAAAAID+Wudp/tFjFBGLSRXFUDlBJwCN62LA+ZLjaXYZAk8AAAAA6KVymarzCo7zqnWevu96nM110S37WF5rD4JOABrThRG17/E08Nxljj4AAAAAwHOTiPViy7XRVZ6aKmcQBJ0A1O7HQ8TRQZz1LeB87niaXa7ztHZXFQAAAABA+wSdANSqiLj69z+zy7brqEp5x9XXdZ7mPx7i/Oig7YoAAAAAAMbJ2qkA1KKIuImIs3Ls6+Asptn1v/+ZfSkirtquBQAAAABgjHR0AlC5IuJqqAHnc5txtncpzg+ztqsBAAAAABgPQScAlblLEYdZ/9fi3Fc5zvbLKk8X1u4EAAAAAF5xvs5T2zUMitG1AFSiiLj51z+yL4uRhZxPHU+zr0bZAgAAAAA0Q9AJwIcVETfH0+xr23V0QTmy9+zOjVkAAAAAALUSdALwUWdCzl8tptn1YSbsBAAAAACokzU6AXi3IuJqbOtx7moxza7XeTorIk6s2wkAAAAARMTZtqW/Vnm6cD1xdzo6AXivs3JMK69YTLPrct3Om7ZrAQAAAAAYGkEnAHspx7FuvfOIvwk7AQAAAACqJ+gEYC+HmZDzPYSdAAAAAADVEnQCsLMi4krI+X7H0+xr2zUAAAAAAAzFp7YLAKA3zo6FnFU4u0txfpi1XQYAAAAADNtdivicVTNlbRIxq+D4i3We3tyh2LewkRN0ArBVEXEj5KzGYppdr/N0FhHnbdcCAAAAAEP2OYubKqasrfM0jxeu5+1z/CfHWL61n1Gs+/F8AfCmMuQ0crVCi2l2XURctV0HAAAAAECfCToBeNVdsq5kXY6n2aWwEwAAAADg/YyuBeBVh1mctV3DkB1Ps8tVnmYvzfcHAAAAAIZll8aH24dYHh00Uc0wCDoBeFERcWVdzvpNIr6F9ToBAAAAYNAWj9dat15vXeVpFhojdmZ0LQC/KdflvGy7jjGwXicAAAAAwPsIOgH4TdllSEPK9Tpv2q4DAAAAAKBPBJ0A/KKIuFoYWds44TIAAAAAwH4EnQD89OPhsbuw7TrGyAhbAAAAAID9fGq7AAC64+ggztquYcyOp9nlf/6blkcHbVcCAAAAAFRpnad5EXGybb/7FLPDrIGCBkLQCUBERBQRN8dG1rauDJvP264DAAAAAKjWJGK2bR8h536MrgUgIqwR2RXlCNubtusAAAAAAOg6QScAUURcLXRzdobQGQAAAABgO6NrAYhJxLrtGvjbYppdr/J0s8soCwAAAACg+8pGky/b9lvl6cJ1wd3p6AQYOd2c3aSrEwAAAADgbYJOgJHTzdlNi2l2/eOh7SoAAAAAALpL0AkwYkXEjW7O7jo6iLO2awAAAAAA6CpBJ8CIGY/abYtpdn2X2q4CAAAAAKCbBJ0AI/Xj4ecC2HTY5yyu2q4BAAAAAKCLPrVdAADt+ONAgNYH5Rqqy7brAAAAAADeb52neUSct13H0OjoBBip42l22XYNbLeYZtdFxE3bdQAAAAAAdI2gE2CEBGf9Yi1VAAAAAIDfCToBRkhw1i/WUgUAAAAA+J01OgFGSHDWP0XEzSRi1nYdAAAAAMD+ymuyX7btt8rTheuAu9PRCTAyxtb2ky5cAAAAAIBfCToBRkZg1k+6cAEAAAAAfiXoBBgZgVl/6cYFAAAAAPiboBNgRARlvefzBwAAAABQ+tR2AQA0SlDWY5OIdUQs264DAAAAANjfOk/zbfsUTRQyIIJOgBEpgzJ6ajHNrv/8K8Vh1nYlAAAAAMA+ypDzfNt+RrHux/MFMCLW5+y/z5muXAAAAACACEEnwGhYn3MwfB4BAAAAAMLoWoAxEZANgHU6AQAAAGA3tw8xW+Xp9KPHKeLlzsF9jl+uvXkTW67T3j7E8uhgzwJHTNAJAD2ymGbX6zy1XQYAAAAAdF4ZGNbWNPCO459tW15sladZRMw+UNaoGF0LMBJlJyAD8OOh7QoAAAAAANon6AQYiW13CtEffxwYQwwAAAAAIOgEGIHC+pxD4/MJAAAAAIyeoBMAesYYYgAAAACAiE9tFwBAI3QAAgAAAAC0qIg4WeXpzX3uU8wOs4YKGgBBJwD0zGKaXa+3/EAEAAAAAHTLJGK2bR8h536MrgUYAaNOAQAAAAAYGh2dANBDRcTNLneAAQAAAADdUOywxJjRtfsRdAKMwGKaXbddAwAAAADAmE0ivm27VrvK00VocNiZ0bUAAAAAAABA7wg6AaCfto65AAAAAAAYMkEnwMDtMvcdAAAAAAD6RtAJAAAAAAAA9M6ntgsAAAAAAACAqt2liM9ZNRPvJhGz54/9eIj44yCu9jhGrPM0f2uf4h21jZmgEwB6aBKxjohl23UAAAAAQFd9zuLmeJp9/ehxynDy/PnjfxzEzfE0u/zIMZ4zinU/ni8AAAAAAACgdwSdAAAAAAAAQO8YXQsAAAAAAAA1K2L7ep63D7E8OmiimmEQdAJADxURC2MZAAAAAKAfFtPsOiKut+23ytMsImb1VzQMrpECAAAAAAAAvSPoBBi+m7YLoHqTiPVdarsKAAAAAID2CDoBBqyIuDqeZpdt10H1FtPs+jCLs7brAAAAAABoi6ATYKCKiBsh57Atptn1LguYAwAAAAAM0ae2CwCgencp4jCLb23XQf2Op9nlKk+ziQXKAQAAAKCz1nmaFxEn2/a7TzE7zBooaCAEnQADUgaccZjF2WKaXbddD804nmZf//Pf9P3ooO1KAAAAAIDX7NKsIOTcj9G1AANymD2uyynkHJ+jgzi7S21XAQAAAADQHEEnwIBYl3O8FtPs+jCLs7brAAAAAABoitG1AD23GVd7lyI+Z3HTdj20ZzHNrte5tk4AAAAA6JpyCt+Xbfut8nSxy4hbHunoBOi5TchpdjsAAAAAAGMi6KRSm/XhbG1tm91uwk7Y6MLr0tbW1tbW1tbW1tbW1nb4WwBoU7bO0/e2i+BlRcRVX9baW+XpdBKxbLsOGKu79Bh29unrBvVY5+n75vUAAAAADTgrxzEyMsZr0gdFxM3xNPv60eOs8zSPiPO6jv+U99Z+rNFJZdzFBe26SxGfhVuj585aAAAAmuRGWwDaJOgEAAAAAACAmpWdoW8qmihkQASdAAAAAAAAUKPXxt8+N2mgliHxfAEAAAAAAAC9I+gEAAAAAAAAesfoWgAAAAAAAKhZEXGzbZ/7FLPDrIlqhkHQCQAAAAAAADVaTLPriPi6bb9Vni4iYlZ/RcNgdC0AAAAAAADQO4JOAAAAAAAAoHcEnQAAAAAAwLvcJVvb7m77rO3nri9bQScAAAAAAPAuh5mtbXe3fdb2c9eX7acAAAAAAADY0xC65hi2zxUGni+93vc9/ipPp9v2uX2I2f9pU9yZoBMAAAAAAABqtM7T/C7F8q19DrOI/5s8bt1IsBuZMAAAAAAAALTsLv0dcg5h/G4TBJ0AAAAAAADQAZtOTh2duzG6FgAAAAAAgMG5fYjZKk8XHz1OUW6fj5Td5/hFRHzO4moSsd6y38l9itl7ax0bQScAAAAAAACDc3QQcVdBaLgZI/u8y3Lf4x9m8W0xza7f2meVa+Xch6ATeu5zFjcREe7wACAi4n9FxB8HcVX+deb7AwAAADBWVY1/fe04xsu2T9AJPfK/IuL/Jo/h5iR+v/Njnae5tvbx+l8R8fmg7SqAtmy+N/zrn7/fFbjK0+ntQyz/z+rsAAAAAAyIoBN65jCLs9da28vHv67zNP/xEOcuaI+LzzeM0/+KiKOD1783REQcT7PLiLhc5en0PsWywfIAAAAAoDYui0PH/a9c5fhzFjf//mf2Zdv87ojHwPPf/8y+bMbaMg6b1wowfO/53hDxGHgeZnFWZ20AAAAA0BQdndBxm1G1x9Ps677/9niafV3l6cIo23HQ0Qnj8ZHvDYtpdr3K05XOTgAAAIBm3aU4//Ovtxf2vLfu515cFqdymy4T249vN3/ecyF743iafd10dnbhY7KtfwttvwZt699W8L3h8nMWV5u/d+FjsrW1tbW1tbW1tbXt5xYA2pSt8/S97SJ4WRFxVa6p1XnW/KrPW2ty7mqdp/ldivOqaqK7Pmf9+bpBPf78y/f1Majie0NEhK5/AADgo6r6/YT+8Tsl0AU6OqlU23eQDW37OYurKn5QXEyza50749lCRPuvQ9v/b+8OcuO2sgWA3irJigGhPyB0etDIImQhkz9qIBsxkh0kK4l3kEaW8ScBMuqJIHsRRgaJuxr/w4CsWFV/oLyYfiZZpEQW+ahzAOGWSBb5inxPZfPykuPFJ6t4NdRJhHXEP6f6HKIoiqIoiqIoLiMCwJRUdM7YtrCKzre3KjqHcrK++8fiX46GvSLu59+M96U7PSrn7wbjMM6X7x+fr74acn2+wwEAgIcY+vwV5VDRCf11qYI3tvpR0cmgTtbiQ2N6fXo0TDVn1enRh6rOOXxWcfgIydR9URwnnh7dPXN5SOuIq6k/lyiKoiiKoiiK5UYAmNLqarP76d0u4rNVhDivqKLz8TpZj/d8g3/9e/eTW4ssl4pOVHQu21hXSl9udi/e3rpSEAAA6E9F5+Ol6gz6U9E5vHXEXVJNnF/k8UlXwg35/LXck9Xw1UAAjKd6lfRY3w3rP57VCQAAAAAlOX63m7oJNHki4fnoVCotR0tG/nEy+/ux1g/AsNJ3Q/X240O7OFu9/Ne/d6HiHwAAAICSHE/dAOBT64irsdZ9cbZ6+fNvrnAAKMnJetzvhoi7iv+bcFsUAAAAoHzVi7lPj4YpLKreTva+699GXFxt2s/Puw69H4lOmKGxn2twehSvPIsNoBw324j//nz0Z968ColOAAAAerrZ3l2gK4pziun16VG8+vJs9e1D+/nVZvcsKndKvM/6rza7Z+928f3NNp5XH1VkTD0stuxKYApDXV2yh+d0AhTiZB3xn/fjb2fsilEAAACWKSVsRHFOcc66tnHqfVhKVNEJ8yMJCcCfbrYRX3zmuwEAAID5qd6+E5ZuiP7edR3GVncF5LbhcTlERY2qHYDijJ7oHPu26QAAACzT1NVcotgW5+TibPXy9Ch+3PeTlp9635USVXTCI3Rxtnr582/tDzwGAAAAANgnVZ6J4hzj3Hx5tvph3zKXm93529s4n3rflRJnmNMGAGAKB3pONAAAAAAMQqITAAAAAAAAKI5EJwAAAAAAAFAciU4AAAAAAACgOMdTNwCAYVzvIk6nbgQwOGMbAAAAoHxXm92zbcTX+5Z7exvnB2jOYkh0AizE09XULQDGYGwDAAAALENKYl7v7s75NEW6c+vambrZTt0CoDTXu6lbAIzB2AYAAADob+xzKg9Zf0pmNkW6k+icqRNHBujJlyAsk7ENAAAA0N/Y51Scs5kHt66dqZttxJOjqVsBlMRz/GCZjG0AAACA/sY+p9J3/Rdnq5cR8dW+5S43uxee09ndOuLDbVLFecQIFZ1Af64ggmUytgEAAAD6U9H5OKynTuqJn8b0A9CH5/jBMhnbAAAAAP3N+RmdDGcd8aF6UJxHBLgPVxDBMhnbAAAAAP2p6Hwc3Lp2hrH0hGe6ikHsFudm6v0h3j/OtU9xeHPoj+Jw3wtzGNtT7xNRFEVRFEVRFOcbAeZq7L9T/g7Ow+rn33Y/Td0I6p0exY9fnq1+mLodXVxudt+8vY3nU7ejJP95H/HFZ/Eqn76O+OcfDyUe1eVm903d9Dfv47krUcpV0t8NxuF7vWynR/Eqova74eoQ3w1Xm92zuun/dxvfj71tAACgTH85iu8O8f8V5udys3vx9jbOp24HtDk9ildfnq2+feh6rja7Z3XnR4Zaf5Wx1U/htYPMzdRXkJUQ088Xn8WPX56tvs1/DvUPwy/PVj/kP+uIq5TknMO+Evv3LYiYR38U+8f/vbuzw6u6v8+H+m64OFu9rPtJ86feR6IoiqIoiqIozi8CzNXYf6f6rv9qs3v282+7n/b9vHkvydmHis4ZK6ky63Kz++bNexWdXTxd3f0B/Ovx/I7v1Wb37Nf3qnZKNsd+xWH9z6++10s21zGsXwEAAE3+dqyi87FSdUYJ5lTR2bQOHkZFJxxQSnICAAAAAADj+ePuWaNxrn8eJDrhgK53d8lOAAAAAABgPP81cgbMuf55kOiEA7veRdy40gMAAAAAWIipnxUrinVx7IrOvuu/OFu9/Mfnq6/2/ZwexauI6fdfKVGiEyZw4koPAAAAAGAhUmWbKM4pjl3ReaiKUbE9HgcAAAAAAEBPnlHInF3fRpweDbi+rL/f7IZdf9u2aKaiEwAAAAAAgEV5OlISMnHnxnmQ6Jwp2XoAAAAAAID7uZFneRTcunamnroSAAAAAAAA4F7mWHF5tdk927fM9hANWRCJzpm63kWcTt0IRuNKEsagX0HZjGEAAACA4Yx9rqXv+q82u2e/vo/vx2nN4+XWtTOlonPZ5nglCeXTr6BsxjAAAADAcMY+1+JczjxIdM6UZ3Qum6odxqBfQdmMYQAAAIDhzK2ik3EcR9wl1Z6uxLlFlsuVHoxBv4KyGcMAAAAAwzlZRbx+F+eXm92Lh65rG3eJzer5m8r6v+m6jr8ex6uIeNW23Ot38fzp0QMa+8gcR3xIqonziZ7RuWzXt+EJuQxOv4KyGcMAAAAAw3p6FPHL73G+3Uas1xH3jRH1F6k/PYp48z6ed63u/OJJfHdxtnrZtszlZnf+5n2c9/2sj9WxW6TC4bkagzHoV1A2YxgAAABgeCeriEjnXe4bu2yDSXhGJ0zg+nbqFrBE+hWUzRgGAAAAgH4kOmECqnYYg34FZTOGAQAAAKAfiU6YgKodxqBfQdmMYQAAAADo53jqBsBjpGqHMehXUDZjGAAAgFLd7O6eUZiiaaZNOS2P17d3510OHSM+fh0RsY34+nKzizav38W580TdSXTCBK5vw+hjcPoVlM0YBgAAoFQp4ZRiNbljmmmHnpbHNP/QMX99s4v45fc4325jr2oil3ZuXQsTcDUGY9CvoGzGMAAAACW6qRSnpceyVKvZEtNMO9S0ptdj29eGauJ130+1WpV26gZmqsTOW2Kbp7LdxmxHn+NYrjn3Kw7HGC7XnMewfgUAAECT6q1B89t1Vi/qNc20sac1vc6XHSvmlZz59C5VmnW35KXdTE+nofMu21otNSPQr6BsxjAAAAAlutk1JzirtxXNpzU9x9BylrvPcnWvmxKRY8S6dubtPVlF/O04vrs4W72MFpeb3Ys37+O8bRk+cEptpg5ZTs3hdbkHN/SlX0HZjGEAAABKlCo6Iz5O7NT9vi9BZTnL3Xe5ulj33n3vuW/Mk575vPvcLUtBXDcSnTNVHXgsj6odxqBfQdmMYQAAAEpzs/u0ojPi02RPWxLIcpYbarku72ta9r6/J9Xitbp5J6t+yc40tvzs/3FKbaZUdC6bqh3GoF9B2YxhAAAASpYSnnkVW1MSNE1PF/42JUfz+dZnfW3rS9Pa1ts1Md9lfl412pSIZTwSnTOl4y+bqh3GoF9B2YxhAAAASpQq1VKs3qYzXdSbkj5pejVxlJapJkXT/5FTJVyePM2XqSaZ6pZva5P2Lat96X1t7Wxqc76upNr2PJmfV4w2JWIZz/HUDaDe9W04Ogumaocx6FdQNmMYAACAEqUkZ0oc5bGaPErLrdcfEkIfJaZa3p9+T8ulmCfEui5f9z7tK799XdoZUd9X03tTv87bmT7HvvXc7D5erjptG3FxtWm/h61TRP1Ipc2Uis5lU7XDGPQrKJsxDAAAQKludnf/r82TQ9UYEX8mkf6sysuSS3W6zL9ZN2+3qR35equ/a1+57ev8OfZs8yHrqb5/va4sHxGv38XziHjedB4oXQjvPFF3x30efsrhbLchDb1gqnYYg34FZTOGAQAAKFGvpNBI2ipKD9kO7ZtH++Yc9+XkJDj7s8tmSmdeNseXMehXUDZjGAAAgBJ9VLGWJcG6JssOOX3q7e9r19TtKLV9bdOnakeub5J4u71L8ro4vp2awZnScZfN8WUM+hWUzRgGAACgRNWKzojmhFg+bYrXU29/7vun5Pa1LTOHfZYucP/7k/gx9nj97u7Wtul20BGfVrWu15/eIvcx/r5eS3TOlqqOZXN8GYN+BWUzhgEAAChRXtHZVN22L0E19vS5tmXfvjt0W+d6/Pa1b1+icQ7HdL2OWEdcXZytXkaLy83u/Jff4/zPxF58SHh+lPhs+IyPLTqlNlOqOpZru3V8GZ5+BWUzhgEAAChVU0Vn/roa8+mH+H3Kbbe1JX+d7FtXdbmUbH7o9Ihux6+UfTeXdu17LmeTdJyq1YxN4616TB/T738kj5kjVR3LlUrOYUj6FZTNGAYAAKBUHyUcKq8jPk72VGM1SVO33JjzD7mtPm3ZlzhrSyan5Ff+npNVv+WbEmh1x3DqfdmUjK3re1Me07S/73PeJ39fNcFX3UY1EZra9Rh+//P2tf13LYegqmO5VO0wBv0KymYMAwAAUKpqwiEl1vIEWzUBWpdIe8j8/HVdJV11/pht6Ts/X6ZLW5vW2/aetuWr20v7K68kTPv0k0q6Cfdl3s40r5oQq/s8Q7UjTzjWbSvfj32lz1X9TGls1SW089vbNo29pcxP+0iic6ZUdSyXqh3GoF9B2YxhAAAASvVnVVVDoqKafMorCfMEaVpfl/l5ErP6f+u8yq1amVhNzO5rS3X9EXfzhnx/W0K4ab+l9+RJtHx+dd117ajbd/n2qhWF1YTiGPuia7+oHve8nXnFY9Pnyfddl3Y2HYf8GFRj3X7so5oczZN/+eeta0O1nWkfLmV+NXl83L4bmYKKjmVzfBmDfgVlM4YBAAAo3ckq4jo+JIlSrKvOispydYmauvUkefIyr5h7etRSmViz3fR7an9d9WL6vamyMal+vrS+vC118+oSs9fZe/L9l9aRr6utGi5to267dcek8bju2U+j7MvK/LYkX1MVYN6XWvtfTfVoUxK/+nnz/Vq3j7cRX19u2h/a+fpdnOfJ50+2V9Pmts+f3nMdLf1pxvMj6sdwhETnLKnoWLb7lqlDG/0KymYMAwAAULI8oRTx8e0lIz5OTqTboVarQdP/i+sSVNVbeOaJqU8ShJXESH6uPU+cVNvb1La2efn/5/NE3L59le+bfFvVdebbqVu+LlnXpu6YNLW1z35Kyzbt74fsyzwp27Tuakzv27fturZX+1CXY5Da1VTF+fpdnH869VPVbTUlu+vm16n7LGkb+f6Y4/ymZdM0ic4ZcqJz2RxfxqBfQdmMYQAAoET+L0OuLimX5MmJOnnyKk9ytGmqymtrZ/66LrHSNq+pmi5ftm1enlzL5QnPqqbP2ZTEq0vq7Tsmddvuu5+6vO66L6uVkqlt+455l203batt/9dNr0vM5+vtqq4P1W23LYHYFNP79n3mQ8yvfra6/d12fCIkOmdp3x/sufIPm2VwHKFsxjBj0K8AAADYJ0+83Cd51FaR17TNpsRhl0Rf0vT7vvdV21yXZNvXnnzf1e2X6v7qmqjc1+auya+u+61rcqptetd9WX1f277o0/Z9x2bfvql+9rZ+2yVh3/RZ8wrpts+Qt73LeMzb1ZRsHnp+RHtSu8sYlOicIScTAQAAAACYuz7nsvNl25JG91ln/t4uVW1dY5uuy3Zdf59Eb77epvfn8/Z9lq7be8ixa9v+fdc35DFv2qdd2l23jhS/eBLfXZytXrZ9jsvN7kW6xe2Qbe/Tr/bd+nio+XXHuksfrMYOuWMOrUtGHwAAAAAA5ixPSNRNe2js8nrIbYyxjqqhPv++5R7S1qGP4ZyO01jH/iGmaPu+9x9iftf9eTzUjgYAAAAAAAA4FLWDAAAAAAAAQHEkOgEAAAAAAIDiHE/dAAAAAAAAAFi6bcTF1Wa3bxl6kOgEAAAAAACAkb1+F88j4vnU7VgSt64FAAAAAAAAiiPRCQAAAAAAABTn/wEAZ5AQn+5ryAAAAABJRU5ErkJggg==", "e": 1}, {"id": "comp_0", "nm": "City", "fr": 30, "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "Flag", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1211.6, 1192.8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.25, 18.15, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.6, -17.9], [0.6, -17.9], [0.6, 17.9], [-0.6, 17.9]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.400000029919, 0.831372608858, 0.960784373564, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.85, 18.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Green", "parent": 2, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.45, 21.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [80.5, 94, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 7, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 161, "h": 188, "ip": 68, "op": 180.5, "st": 68, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Flag 2", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [78.056, 88.361, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [78.056, 88.361, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [78.056, 76.753, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [78.056, 80.325, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [78.056, 94.611, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [78.056, 112.468, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [78.056, 116.932, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [78.056, 104.432, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [78.056, 90.146, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [14.673, 12.229, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [500, 1428.571, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [4.981, 4.981], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [0, 0], "ix": 4}, "e": {"a": 0, "k": [100, 0], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.412799999761, 0.651110540652, 0.860000011968, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [14.673, 12.229], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "White", "parent": 2, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.45, 14, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [80.5, 94, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 7, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 161, "h": 188, "ip": 68, "op": 180.5, "st": 68, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "Orange", "parent": 2, "refId": "comp_3", "sr": 1.01869158878505, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.325, 6.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [80.5, 94, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20, 7, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 161, "h": 188, "ip": 68, "op": 182.602803738318, "st": 68, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 15, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [372.519, 733.965, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [580.469, 431.118, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.7, 0.6], [-2.8, -0.5], [-2.3, -1.5], [-1.4, -2.2], [0, 0], [18.6, 3.5], [0, 0], [-15.5, 11]], "o": [[0, 0], [2.1, -1.6], [2.7, -0.6], [2.8, 0.5], [2.3, 1.5], [0, 0], [10.7, 15.8], [0, 0], [-18.5, -3.4], [0, 0]], "v": [[-657.9, 286.531], [1.8, -177.169], [9.2, -180.469], [17.6, -180.569], [25.4, -177.469], [31.1, -171.769], [486.7, 500.132], [465.3, 533.632], [-649.7, 325.531], [-657.8, 286.531]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-219.156, -20.547], "ix": 5}, "e": {"a": 0, "k": [-196.141, 12.07], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.45, 328.221], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-3, 0.6], [-3, -0.5], [-2.6, -1.7], [-1.4, -2.4], [0, 0], [19.9, 3.8], [0, 0], [-16.8, 11.9]], "o": [[0, 0], [2.3, -1.7], [3, -0.7], [3, 0.6], [2.4, 1.7], [0, 0], [11.6, 17.1], [0, 0], [-20.1, -3.7], [0, 0]], "v": [[-660.5, 215.077], [-1.8, -250.424], [6.2, -254.024], [15.3, -254.224], [23.8, -250.824], [29.6, -244.624], [483.2, 428.176], [460.2, 464.376], [-651.7, 256.977], [-660.5, 214.977]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-277.312, -56.141], "ix": 5}, "e": {"a": 0, "k": [-248.992, -16.297], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.45, 254.577], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Lense flare", "sr": 1, "ks": {"o": {"a": 0, "k": 35, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 180, "s": [74]}], "ix": 10}, "p": {"a": 0, "k": [25.444, 466.425, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, 8.8], [-8.9, 0.2], [-0.2, -8.8], [8.9, -0.2]], "o": [[-0.2, -8.8], [8.9, -0.2], [0.2, 8.8], [-8.9, 0.2]], "v": [[-16.15, 0.3], [-0.35, -15.9], [16.15, -0.3], [0.35, 15.9]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [16.278, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.506, 296.475], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.6, 25.4], [-25.8, 0.5], [-0.6, -25.4], [25.8, -0.5]], "o": [[-0.6, -25.4], [25.9, -0.5], [0.6, 25.4], [-25.9, 0.5]], "v": [[-46.8, 0.95], [-1.1, -46.05], [46.8, -0.95], [1.1, 46.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [65.205, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [276.856, 257.325], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[14.4, -11.9], [-12, -14.2], [-14.4, 11.9], [12, 14.2]], "o": [[-14.4, 11.8], [12.1, 14.2], [14.4, -11.8], [-12.1, -14.2]], "v": [[-21.85, -25.65], [-26.05, 21.45], [21.85, 25.65], [26.05, -21.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [34.404, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [454.106, 252.125], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.1, -2.2], [-2.2, -3], [-3.1, 2.2], [2.2, 3]], "o": [[-3.1, 2.2], [2.2, 3], [3.1, -2.2], [-2.2, -3]], "v": [[-4.05, -5.45], [-5.55, 3.95], [4.05, 5.45], [5.55, -3.95]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [6.989, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [546.906, 385.525], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.7, -4], [-4.3, 11.3], [11.7, 4], [4.1, -11.3]], "o": [[-4.2, 11.3], [11.7, 4], [4.2, -11.3], [-11.7, -4], [0, 0]], "v": [[-21.2, -7.3], [-7.6, 20.5], [21.2, 7.3], [7.6, -20.5], [-21.1, -7.3]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0.151, 0.052], "ix": 5}, "e": {"a": 0, "k": [52.628, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [393.956, 297.875], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-7.2, -0.7], [-0.7, 7.1], [7.2, 0.7], [0.7, -7.1]], "o": [[7.3, 0.6], [0.6, -7.1], [-7.3, -0.6], [-0.6, 7.1]], "v": [[-1.15, 12.95], [13.15, 1.15], [1.15, -12.95], [-13.15, -1.15]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [17.867, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [464.406, 334.725], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6.4, -7.4], [7.6, 6.3], [-6.4, 7.4], [-7.6, -6.3]], "o": [[-6.4, 7.4], [-7.6, -6.3], [6.4, -7.4], [7.6, 6.3]], "v": [[13.7, 11.45], [-11.6, 13.45], [-13.7, -11.45], [11.6, -13.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [34.822, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [488.056, 406.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.4, -18], [18.4, -0.4], [0.4, 18], [-18.4, 0.4]], "o": [[0.4, 18.1], [-18.3, 0.4], [-0.4, -18.1], [18.3, -0.4]], "v": [[33.25, -0.7], [0.75, 32.7], [-33.25, 0.7], [-0.75, -32.7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [70.346, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [186.406, 177.975], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.8, 20.9], [21.4, 3.9], [3.8, -20.9], [-21.3, -3.9]], "o": [[21.3, 3.9], [3.8, -21], [-21.3, -3.9], [-3.8, 21], [0, 0]], "v": [[-6.9, 37.95], [38.6, 7.05], [6.9, -37.95], [-38.6, -7.05], [-7, 37.95]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [454.555, 385.025], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.5, -21.6], [21.9, -0.4], [0.5, 21.6], [-21.9, 0.4]], "o": [[0.5, 21.6], [-22, 0.4], [-0.5, -21.6], [22, -0.4]], "v": [[39.75, -0.8], [0.951, 39.1], [-39.75, 0.8], [-0.95, -39.1]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [71.863, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [394.605, 296.875], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-39.2, -3.3], [-3.3, 38.5], [39.2, 3.3], [3.3, -38.5]], "o": [[39.1, 3.4], [3.3, -38.5], [-39.1, -3.4], [-3.3, 38.5]], "v": [[-6, 69.7], [70.9, 6.1], [6, -69.7], [-70.9, -6.1]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [114.361, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.156, 69.975], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.8, -24.5], [24.9, 1.9], [-1.8, 24.5], [-24.9, -1.9]], "o": [[-1.7, 24.5], [-24.9, -1.9], [1.7, -24.5], [24.9, 1.9]], "v": [[45, 3.4], [-3.2, 44.3], [-45, -3.4], [3.2, -44.3]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 0], "ix": 5}, "e": {"a": 0, "k": [100, 0], "ix": 6}, "t": 2, "h": {"a": 0, "k": 0, "ix": 7}, "a": {"a": 0, "k": 0, "ix": 8}, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [488.356, 405.075], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Smaller Rays 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [329.9, 589.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [110.55, -73.6]], "c": false}]}, {"t": 179, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 149, "op": 180, "st": 149, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Smaller Rays 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.9, 639.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 145, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 115, "op": 146, "st": 115, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Smaller <PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.9, 639.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 125, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 95, "op": 126, "st": 95, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Smaller Rays 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.9, 639.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 125, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 95, "op": 126, "st": 95, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Smaller Rays 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [219.9, 559.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 110, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 80, "op": 111, "st": 80, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Smaller Rays 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [329.9, 609.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 72, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 42, "op": 73, "st": 42, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Smaller Rays 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [369.9, 649.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 42, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1.5, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12, "op": 43, "st": 12, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Smaller <PERSON>s", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [339.9, 499.95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [399.3, 234.95, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[137.2, -96.4], [117.8, -79.6]], "c": false}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-136.8, 96.1], [-137.2, 96.4]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 2, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0.5, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [-135.732, 95.262], "ix": 4}, "e": {"a": 0, "k": [138.146, -97.551], "ix": 5}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202, 159.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 31, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Foreground 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [452.398, 930.102, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 148, "s": [-42.398, 930.102, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.898, 68.398, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20.396, 84.655, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [312.797, 312.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.533599973192, 0.739510450176, 0.920000023935, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.898, 68.398], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Foreground 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [442.398, 915.102, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 145, "s": [-42.398, 915.102, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.898, 68.398, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [20.715, 84.655, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [312.797, 312.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.533599973192, 0.739510450176, 0.920000023935, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.898, 68.398], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Foreground 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [452.398, 922.602, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [-32.398, 922.602, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.898, 68.398, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [8.567, 84.655, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [312.797, 312.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.533599973192, 0.739510450176, 0.920000023935, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.898, 68.398], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Foreground 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [412.398, 922.602, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [-72.398, 922.602, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.898, 68.398, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [13.682, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [312.797, 312.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.533599973192, 0.739510450176, 0.920000023935, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.898, 68.398], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Foreground", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [427.398, 932.602, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 54, "s": [-49.398, 932.602, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [41.898, 68.398, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [30.306, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [312.797, 312.797], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.533599973192, 0.739510450176, 0.920000023935, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.898, 68.398], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Bull body", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 120, "s": [-8]}], "ix": 10}, "p": {"a": 0, "k": [612.372, 1404.756, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [20.936, 59.647, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.056, -3.234], [3.3, -2.6], [0, 0], [0, 0], [1.3, -1], [0.6, -0.9], [0, 0], [0, 0], [-1.6, -2], [-4.1, -4.4], [1.5, -1.2], [-0.8, -1], [0, 0], [0, 0], [0, 0], [5.2, 6.5], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.3, 2.6], [-1.1, 1.2], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.584, 0]], "o": [[-9.1, 7.3], [0, 0], [0, 0], [0, 0], [-0.8, 0.7], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, 1.2], [0.8, 0.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.2, -1.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.579, -0.633], [-3.78, 0]], "v": [[2.4, -25.399], [-8.3, -7.199], [-8.3, -7.299], [-17.4, -0.099], [-20.8, -0.099], [-22.9, 2.301], [-25.1, 1.701], [-22.2, 3.101], [-17.7, 1.501], [-10.3, 13.201], [-9.5, 16.801], [-9.5, 21.401], [-4.4, 29.601], [-2.5, 28.1], [-4.8, 20.401], [-3.4, 8.801], [11.1, 1.801], [19.9, 2.201], [19.5, 3.901], [16.7, 3.501], [15.4, 5.501], [20.1, 5.801], [22.5, 3.701], [24.5, -0.599], [22.2, -2.999], [19.8, -3.4], [20.2, -5.399], [22.1, -14.499], [23.8, -28.099], [14.371, -29.601]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.271, 0.573, 0.953, 0.5, 0.396, 0.712, 0.973, 1, 0.522, 0.851, 0.992], "ix": 9}}, "s": {"a": 0, "k": [-0.099, -29.171], "ix": 5}, "e": {"a": 0, "k": [-0.004, 30.074], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.35, 30.068], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [5.9, 1.1], [0, 0], [0, 0], [-2.4, -1.7], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.5, 1.7], [0, 0], [0, 0]], "v": [[10.55, 4.15], [4.95, 1.45], [6.05, -2.65], [1.15, -6.15], [-10.65, -0.75], [-0.65, 0.35], [0.55, 3.85], [7.95, 6.15], [10.65, 4.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.352941176471, 0.662745098039, 0.964705942191, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.9, 39.419], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.6, -7.7], [0, 0], [3.7, -0.6]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.85, -7.05], [7.45, 4.95], [4.85, 7.05], [-7.45, 0.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.352941176471, 0.662745098039, 0.964705942191, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [52, 24.119], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Bull Head", "parent": 23, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"t": 126, "s": [-55]}], "ix": 10}, "p": {"a": 0, "k": [47.342, 9, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.805, 7.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.271, -2.845], [-0.547, 6.215], [7.369, 2.719]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.992, -1.024], [-4.382, 3.387], [6.653, -3.387], [-3.894, -1.126]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.352941176471, 0.662745098039, 0.964705942191, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.922, 2.812], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.103, -0.345], [-1.5, 0.4], [2.5, 3.1], [0.2, 2.199], [0, 0], [4.9, 0.9], [0.8, 0.201]], "o": [[1.966, -0.38], [1.6, 0.5], [1.5, -0.4], [0, 0], [0, 0], [-0.2, -2.301], [-0.7, -0.1], [0, 0]], "v": [[-7.603, 5.744], [-1.197, 5.15], [3.703, 7.15], [5.103, 2.05], [1.903, -1.949], [1.803, -1.949], [-5.197, -7.05], [-7.397, -7.55]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.271, 0.573, 0.953, 0.5, 0.396, 0.712, 0.973, 1, 0.522, 0.851, 0.992], "ix": 9}}, "s": {"a": 0, "k": [-0.575, -7.526], "ix": 5}, "e": {"a": 0, "k": [-0.562, 8.009], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.507, 7.949], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.512, -0.118], [-0.841, -3.645], [-3.644, 0.841], [0.841, 3.644], [3.084, 0]], "o": [[-3.645, 0.841], [0.842, 3.644], [3.645, -0.842], [-0.723, -3.132], [-0.504, 0]], "v": [[-1.523, -6.932], [-6.599, 1.191], [1.523, 6.266], [6.599, -1.856], [0.005, -7.107]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.271, 0.573, 0.953, 0.5, 0.396, 0.712, 0.973, 1, 0.522, 0.851, 0.992], "ix": 9}}, "s": {"a": 0, "k": [0.113, -7.003], "ix": 5}, "e": {"a": 0, "k": [0.062, 6.532], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.69, 7.357], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Arrow", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [687.6, 1397.7, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [79.35, 53.8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.6, -2.1], [0, 0], [1.8, 0.6], [0, 0], [3.4, -2.5], [0, 0], [0, 0], [0, 0], [-1.7, -0.6], [0, 0], [-3.4, 2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.6, 1.1], [0, 0], [-4, -1.4], [0, 0], [0, 0], [0, 0], [1.4, -1.1], [0, 0], [4, 1.3], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[60.8, -49.05], [24.9, -22.95], [19.5, -22.15], [9, -25.95], [-2.8, -24.25], [-79.1, 37.55], [-67.1, 53.55], [6.9, -6.75], [11.9, -7.55], [23.1, -3.85], [35, -5.65], [72.9, -32.95], [76.3, -28.45], [79.1, -49.95], [79, -50.05], [57.4, -53.55]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0, 0.843, 0.463, 0.5, 0.5, 0.922, 0.731, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [79.189, -49.342], "ix": 5}, "e": {"a": 0, "k": [-73.102, 45.408], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [79.35, 53.8], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[13.3, -9.9], [-13.3, 9.9]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [110.65, 17.65], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4, 1.4], [0, 0], [1.4, -1.1], [0, 0]], "o": [[-3.4, 2.5], [0, 0], [-1.7, -0.6], [0, 0], [0, 0]], "v": [[27.35, -8.1], [15.45, -6.3], [4.25, -10], [-0.75, -9.2], [-27.35, 10.6]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [88.4, 65.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Clouds", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [470.85, 446.95, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [366.849, 446.95, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [454.4, 74.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.9, -8.1], [0.9, -1], [-0.4, -2.3], [0, 0], [0, 0], [1.943, 0.618], [2.2, -1.4], [6.6, 0]], "o": [[-1, 0.4], [-1.8, 2.1], [0, 0], [0, 0], [-1.594, -2.053], [-3, -0.9], [-2.7, -6.3], [-7.8, 0]], "v": [[417.4, 105.9], [414.4, 108.1], [412.1, 115.2], [462.5, 115.2], [462.5, 105.981], [457, 101.6], [448.8, 102.4], [433.7, 91.8]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [437.341, 91.891], "ix": 5}, "e": {"a": 0, "k": [437.355, 115.458], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [446.05, 33.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.6, -6.9], [0.8, -0.9], [-0.3, -1.9], [0, 0], [0, 0], [0, 0.4], [3.7, 1.2], [1.9, -1.2], [5.7, 0]], "o": [[-0.9, 0.4], [-1.6, 1.8], [0, 0], [0, 0], [0.2, -0.3], [-0.1, -3.3], [-2.6, -0.8], [-2.3, -5.4], [-6.6, 0]], "v": [[-18.3, 2.05], [-20.9, 3.95], [-22.9, 10.05], [23, 10.05], [23, 9.85], [23.2, 8.85], [15.5, -1.65], [8.5, -0.95], [-4.4, -10.05]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0.132, -9.888], "ix": 5}, "e": {"a": 0, "k": [0.153, 10.074], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [707.35, 88.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.8, -7.6], [0.9, -1], [-0.3, -2.1], [0, 0], [0, 0], [0, 0.4], [4.1, 1.3], [2.1, -1.3], [6.3, 0]], "o": [[-1.1, 0.4], [-1.8, 2], [0, 0], [0, 0], [0.3, -0.3], [-0.1, -3.6], [-2.8, -0.9], [-2.5, -5.9], [-7.3, 0]], "v": [[80.9, 27.7], [78, 29.8], [75.8, 36.5], [126.3, 36.5], [126.3, 36.4], [126.6, 35.3], [118.1, 23.7], [110.4, 24.4], [96.2, 14.4]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [101.259, 14.507], "ix": 5}, "e": {"a": 0, "k": [101.205, 36.722], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [446.05, 33.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.6, -6.9], [0.8, -0.9], [-0.3, -1.9], [0, 0], [0, 0], [0, 0.4], [3.7, 1.2], [1.9, -1.2], [5.7, 0]], "o": [[-0.9, 0.4], [-1.6, 1.8], [0, 0], [0, 0], [0.2, -0.3], [-0.1, -3.3], [-2.6, -0.8], [-2.3, -5.4], [-6.7, 0]], "v": [[-18.3, 2.05], [-20.9, 3.95], [-22.9, 10.05], [23, 10.05], [23, 9.85], [23.2, 8.85], [15.5, -1.65], [8.5, -0.95], [-4.4, -10.05]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0.382, -9.885], "ix": 5}, "e": {"a": 0, "k": [0.546, 10.424], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [336.85, 118.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.9, -7.9], [0.9, -1], [-0.3, -2.2], [0, 0], [0, 0.4], [4.2, 1.4], [2.1, -1.3], [6.5, 0]], "o": [[-1.1, 0.5], [-1.8, 2], [0, 0], [0.3, -0.4], [-0.1, -3.8], [-3, -0.9], [-2.6, -6.2], [-7.6, 0]], "v": [[-440.3, 35.5], [-443.3, 37.7], [-445.5, 44.6], [-393.2, 44.6], [-392.9, 43.4], [-401.7, 31.4], [-409.7, 32.2], [-424.4, 21.8]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-419.112, 21.912], "ix": 5}, "e": {"a": 0, "k": [-419.139, 44.678], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [446.05, 33.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, -9.1], [4.3, -1.4], [0.2, -5.6], [6.1, 0], [0.3, -5.1], [0, 0], [0, 0], [3.1, 3.5], [1.6, 0.7], [11.3, 0]], "o": [[-3.2, -1.9], [-6.4, 2], [-1.6, -0.8], [-6.1, 0], [0, 0], [0, 0], [1.2, -3.5], [-1.3, -1.5], [-2.699, -11.7], [-9.6, 0]], "v": [[346.5, -17.9], [334.7, -19], [323.8, -5.9], [318.4, -7.1], [307.1, 2.1], [399.4, 2.1], [399.4, 2], [396.4, -9.7], [392, -12.9], [368.4, -33.3]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [354.34, -32.246], "ix": 5}, "e": {"a": 0, "k": [354.281, 3.234], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [446.05, 33.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.9, -9.1], [4.3, -1.4], [0.2, -5.6], [6.1, 0], [0.3, -5.1], [0, 0], [0, 0], [3.1, 3.5], [1.6, 0.7], [11.3, 0]], "o": [[-3.1, -1.9], [-6.4, 2], [-1.6, -0.8], [-6.1, 0], [0, 0], [0, 0], [1.2, -3.5], [-1.3, -1.5], [-2.701, -11.7], [-9.6, 0]], "v": [[-205.1, -14.7], [-216.9, -15.8], [-227.8, -2.7], [-233.2, -3.9], [-244.5, 5.3], [-152.2, 5.3], [-152.2, 5.2], [-155.2, -6.5], [-159.6, -9.7], [-183.2, -30.1]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-197.586, -28.797], "ix": 5}, "e": {"a": 0, "k": [-197.761, 5.655], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [446.05, 33.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2, -8.6], [1, -1.1], [-0.4, -2.4], [0, 0], [0, 0.5], [4.6, 1.5], [2.3, -1.5], [7.1, 0]], "o": [[-1.1, 0.5], [-2, 2.2], [0, 0], [0.3, -0.399], [-0.1, -4.1], [-3.2, -1.1], [-2.9, -6.7], [-8.3, 0]], "v": [[-22.8, 2.55], [-26, 4.95], [-28.5, 12.45], [28.6, 12.45], [28.9, 11.15], [19.3, -1.95], [10.6, -1.15], [-5.5, -12.45]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [0.506, -12.165], "ix": 5}, "e": {"a": 0, "k": [1.172, 14.25], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [183.35, 136.3], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 3, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 2, "nm": "Layer 1 new", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [462.5, 406, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-33.5, 406.02, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [925, 812, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [50, 50, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Electric pole", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [636.35, 785.1, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [385.685, 785.1, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [542.75, 32.85, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-141.8, 34.7]], "o": [[0, 0], [0, 0]], "v": [[-180.7, -17.7], [180.7, -17]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [904.3, 21.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-188.9, 28.7]], "o": [[0, 0], [0, 0]], "v": [[-174.6, -11.75], [174.6, -14.35]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [909.6, 18.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [21.7, 15.1]], "o": [[0, 0], [0, 0]], "v": [[60.25, -7.55], [-60.25, -7.55]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [674.45, 11.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.3, 0], [0, 0], [0, -0.3], [0, 0], [-0.3, 0], [0, 0], [0, 0.3], [0, 0]], "o": [[0, 0], [-0.3, 0], [0, 0], [0, 0.3], [0, 0], [0.3, 0], [0, 0], [0, -0.3]], "v": [[0.45, -1.15], [-0.45, -1.15], [-1.05, -0.55], [-1.05, 0.55], [-0.45, 1.15], [0.45, 1.15], [1.05, 0.55], [1.05, -0.55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [734.35, 4.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [21.7, 13.4]], "o": [[0, 0], [0, 0]], "v": [[61.35, -6.5], [-61.35, -6.7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [662.45, 10.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.3, 0], [0, 0], [0, -0.3], [0, 0], [-0.3, 0], [0, 0], [0, 0.3], [0, 0]], "o": [[0, 0], [-0.3, 0], [0, 0], [0, 0.3], [0, 0], [0.3, 0], [0, 0], [0, -0.3]], "v": [[0.45, -1.15], [-0.45, -1.15], [-1.05, -0.55], [-1.05, 0.55], [-0.45, 1.15], [0.45, 1.15], [1.05, 0.55], [1.05, -0.55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [723.95, 4.4], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0.1], [0, 0], [0.2, 0], [0, 0], [0, -0.1], [0, 0], [-0.2, 0], [0, 0]], "o": [[0, 0], [0, -0.1], [0, 0], [-0.1, 0], [0, 0], [0, 0.1], [0, 0], [0.1, 0]], "v": [[7.55, 0.9], [7.55, -0.9], [7.25, -1.1], [-7.25, -1.1], [-7.55, -0.9], [-7.55, 0.9], [-7.25, 1.1], [7.25, 1.1]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [728.55, 5.65], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [141.8, 34.7]], "o": [[0, 0], [0, 0]], "v": [[180.7, -17.7], [-180.7, -17]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [181.2, 21.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [188.9, 28.7]], "o": [[0, 0], [0, 0]], "v": [[174.6, -11.75], [-174.6, -14.35]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [175.9, 18.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-21.7, 15.1]], "o": [[0, 0], [0, 0]], "v": [[-60.25, -7.55], [60.25, -7.55]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [411.05, 11.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0]], "o": [[0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0], [0, -0.3]], "v": [[-0.45, -1.15], [0.45, -1.15], [1.05, -0.55], [1.05, 0.55], [0.45, 1.15], [-0.45, 1.15], [-1.05, 0.55], [-1.05, -0.55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [351.15, 4.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-21.7, 13.4]], "o": [[0, 0], [0, 0]], "v": [[-61.35, -6.5], [61.35, -6.7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.2, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [423.05, 10.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.3, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0]], "o": [[0, 0], [0.3, 0], [0, 0], [0, 0.3], [0, 0], [-0.3, 0], [0, 0], [0, -0.3]], "v": [[-0.45, -1.15], [0.45, -1.15], [1.05, -0.55], [1.05, 0.55], [0.45, 1.15], [-0.45, 1.15], [-1.05, 0.55], [-1.05, -0.55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [361.55, 4.4], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 4, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0.1], [0, 0], [-0.2, 0], [0, 0], [0, -0.1], [0, 0], [0.2, 0], [0, 0]], "o": [[0, 0], [0, -0.1], [0, 0], [0.1, 0], [0, 0], [0, 0.1], [0, 0], [-0.1, 0]], "v": [[-7.55, 0.9], [-7.55, -0.9], [-7.25, -1.1], [7.25, -1.1], [7.55, -0.9], [7.55, 0.9], [7.25, 1.1], [-7.25, 1.1]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [356.95, 5.65], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 4, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.3], [0, 0], [0, 0], [0, 0], [-0.3, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.3], [0, 0], [0.3, 0]], "v": [[1.2, -29.25], [1.2, 35.45], [-1.2, 35.45], [-1.2, -29.25], [-0.7, -29.75], [0.7, -29.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.705882352941, 0.862745157878, 0.980392216701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [356.6, 30], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 4, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 2, "nm": "Layer 2 new new", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 75, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [462.5, 406, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [271.163, 406.007, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [925, 812, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [50, 50, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [462.5, -0.02, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-2.5, -408.02, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100.108, 99.992, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [924, 812], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.749, 0.929, 0.992, 0.5, 0.875, 0.965, 0.996, 1, 1, 1, 1], "ix": 9}}, "s": {"a": 0, "k": [5.744, -400.25], "ix": 5}, "e": {"a": 0, "k": [5.368, 410.188], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-2.5, -2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_1", "nm": "Green", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Move", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [197, 57, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "Null 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [407, 1.375, 0], "to": [0, 1.233, 0], "ti": [0, 0.357, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 1.066, "s": [407, 0.514, 0], "to": [0, -0.222, 0], "ti": [0, 0.4, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 2.134, "s": [407, -0.394, 0], "to": [0, -0.308, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 3.2, "s": [407, -1.318, 0], "to": [0, -0.349, 0], "ti": [0, 0.308, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 4.267, "s": [407, -2.388, 0], "to": [0, -0.824, 0], "ti": [0, -0.586, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 5.333, "s": [407, -3.338, 0], "to": [0, 0.827, 0], "ti": [0, 0.926, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 6.4, "s": [407, -4.471, 0], "to": [0, -0.374, 0], "ti": [0, 0.466, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 7.467, "s": [407, -5.626, 0], "to": [0, -0.419, 0], "ti": [0, 0.437, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 8.533, "s": [407, -6.738, 0], "to": [0, -0.465, 0], "ti": [0, 0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 9.6, "s": [407, -7.976, 0], "to": [0, -0.558, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 10.667, "s": [407, -9.212, 0], "to": [0, -0.487, 0], "ti": [0, -0.415, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 11.733, "s": [407, -10.656, 0], "to": [0, 0.668, 0], "ti": [0, 0.941, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 12.8, "s": [407, -11.879, 0], "to": [0, -0.387, 0], "ti": [0, 0.454, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 13.866, "s": [407, -13.413, 0], "to": [0, -0.416, 0], "ti": [0, 0.38, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 14.934, "s": [407, -14.621, 0], "to": [0, -0.473, 0], "ti": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 16, "s": [407, -15.767, 0], "to": [0, -0.594, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.066, "s": [407, -16.859, 0], "to": [0, 0.602, 0], "ti": [0, 0.753, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 18.134, "s": [407, -17.951, 0], "to": [0, -0.304, 0], "ti": [0, 0.356, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 19.2, "s": [407, -18.957, 0], "to": [0, -0.335, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 20.267, "s": [407, -19.897, 0], "to": [0, -0.378, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 21.333, "s": [407, -20.796, 0], "to": [0, -0.657, 0], "ti": [0, -0.636, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 22.4, "s": [407, -21.613, 0], "to": [0, 0.523, 0], "ti": [0, 0.676, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 23.467, "s": [407, -22.434, 0], "to": [0, -0.285, 0], "ti": [0, 0.341, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 24.533, "s": [407, -23.204, 0], "to": [0, -0.325, 0], "ti": [0, 0.321, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 25.6, "s": [407, -23.942, 0], "to": [0, -0.375, 0], "ti": [0, 0.292, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 26.667, "s": [407, -24.603, 0], "to": [0, -0.676, 0], "ti": [0, -1.05, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27.733, "s": [407, -25.178, 0], "to": [0, 0.429, 0], "ti": [0, 0.135, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 28.8, "s": [407, -25.694, 0], "to": [0, -0.08, 0], "ti": [0, 0.11, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 29.866, "s": [407, -26.235, 0], "to": [0, -0.082, 0], "ti": [0, 0.067, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 30.934, "s": [407, -26.645, 0], "to": [0, -0.074, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32, "s": [407, -26.987, 0], "to": [0, -0.052, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 33.066, "s": [407, -27.337, 0], "to": [0, -0.003, 0], "ti": [0, 0.024, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 34.134, "s": [407, -27.5, 0], "to": [0, -0.084, 0], "ti": [0, 0.063, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 35.2, "s": [407, -27.783, 0], "to": [0, -0.123, 0], "ti": [0, 0.031, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 36.267, "s": [407, -27.952, 0], "to": [0, -0.052, 0], "ti": [0, -0.185, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 37.333, "s": [407, -27.959, 0], "to": [0, 0.054, 0], "ti": [0, -0.133, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 38.4, "s": [407, -28.011, 0], "to": [0, 0.084, 0], "ti": [0, -0.06, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 39.467, "s": [407, -27.972, 0], "to": [0, 0.128, 0], "ti": [0, -0.146, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 40.533, "s": [407, -27.932, 0], "to": [0, 0.153, 0], "ti": [0, -0.176, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 41.6, "s": [407, -27.806, 0], "to": [0, 0.188, 0], "ti": [0, -0.214, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 42.667, "s": [407, -27.69, 0], "to": [0, 0.249, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 43.733, "s": [407, -27.465, 0], "to": [0, 0.508, 0], "ti": [0, 0.14, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 44.8, "s": [407, -27.168, 0], "to": [0, -0.61, 0], "ti": [0, -0.711, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 45.866, "s": [407, -26.678, 0], "to": [0, 0.301, 0], "ti": [0, -0.394, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 46.934, "s": [407, -26.177, 0], "to": [0, 0.37, 0], "ti": [0, -0.426, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 48, "s": [407, -25.584, 0], "to": [0, 0.46, 0], "ti": [0, -0.497, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 49.066, "s": [407, -24.975, 0], "to": [0, 1.076, 0], "ti": [0, 0.213, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 50.134, "s": [407, -24.18, 0], "to": [0, -0.161, 0], "ti": [0, -1.082, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51.2, "s": [407, -23.3, 0], "to": [0, 0.521, 0], "ti": [0, -0.68, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 52.267, "s": [407, -22.217, 0], "to": [0, 0.628, 0], "ti": [0, -0.71, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 53.333, "s": [407, -21.177, 0], "to": [0, 0.759, 0], "ti": [0, -0.814, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 54.4, "s": [407, -19.881, 0], "to": [0, 1.001, 0], "ti": [0, -0.939, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 55.467, "s": [407, -18.482, 0], "to": [0, 1.017, 0], "ti": [0, 0.225, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 56.533, "s": [407, -16.99, 0], "to": [0, -0.272, 0], "ti": [0, -1.662, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 57.6, "s": [407, -15.101, 0], "to": [0, 0.805, 0], "ti": [0, -1.002, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58.667, "s": [407, -13.023, 0], "to": [0, 0.949, 0], "ti": [0, -1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 59.733, "s": [407, -11.104, 0], "to": [0, 1.175, 0], "ti": [0, -1.1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 60.8, "s": [407, -9.283, 0], "to": [0, 1.697, 0], "ti": [0, 0.385, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 61.866, "s": [407, -7.489, 0], "to": [0, -0.418, 0], "ti": [0, -1.407, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62.934, "s": [407, -5.666, 0], "to": [0, 0.625, 0], "ti": [0, -0.76, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 64, "s": [407, -3.992, 0], "to": [0, 0.717, 0], "ti": [0, -0.749, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65.066, "s": [407, -2.445, 0], "to": [0, 0.853, 0], "ti": [0, -0.802, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 66.134, "s": [407, -0.949, 0], "to": [0, 1.624, 0], "ti": [0, 0.483, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 67.2, "s": [407, 0.46, 0], "to": [0, -0.451, 0], "ti": [0, -1.106, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 68.267, "s": [407, 1.779, 0], "to": [0, 0.471, 0], "ti": [0, -0.58, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 69.333, "s": [407, 2.944, 0], "to": [0, 0.544, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 70.4, "s": [407, 4.282, 0], "to": [0, 0.626, 0], "ti": [0, -0.59, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 71.467, "s": [407, 5.272, 0], "to": [0, 1.484, 0], "ti": [0, 0.539, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 72.533, "s": [407, 6.27, 0], "to": [0, -0.442, 0], "ti": [0, -0.818, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 73.6, "s": [407, 7.287, 0], "to": [0, 0.335, 0], "ti": [0, -0.412, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 74.667, "s": [407, 8.236, 0], "to": [0, 0.377, 0], "ti": [0, -0.397, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 75.733, "s": [407, 8.974, 0], "to": [0, 0.437, 0], "ti": [0, -0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 76.8, "s": [407, 9.737, 0], "to": [0, 1.243, 0], "ti": [0, 0.576, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 77.866, "s": [407, 10.453, 0], "to": [0, -0.4, 0], "ti": [0, -0.531, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 78.934, "s": [407, 11.092, 0], "to": [0, 0.216, 0], "ti": [0, -0.269, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 80, "s": [407, 11.643, 0], "to": [0, 0.246, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 81.066, "s": [407, 12.227, 0], "to": [0, 0.275, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 82.134, "s": [407, 12.61, 0], "to": [0, 0.337, 0], "ti": [0, -0.287, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 83.2, "s": [407, 13.036, 0], "to": [0, 0.475, 0], "ti": [0, 0.435, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84.267, "s": [407, 13.4, 0], "to": [0, -0.37, 0], "ti": [0, -0.346, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 85.333, "s": [407, 13.659, 0], "to": [0, 0.154, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 86.4, "s": [407, 13.939, 0], "to": [0, 0.183, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 87.467, "s": [407, 14.071, 0], "to": [0, 0.223, 0], "ti": [0, -0.203, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.533, "s": [407, 14.256, 0], "to": [0, 0.37, 0], "ti": [0, 0.559, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 89.6, "s": [407, 14.312, 0], "to": [0, -0.09, 0], "ti": [0, 0.079, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 90.667, "s": [407, 14.246, 0], "to": [0, -0.093, 0], "ti": [0, 0.08, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 91.733, "s": [407, 14.172, 0], "to": [0, -0.099, 0], "ti": [0, 0.078, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 92.8, "s": [407, 14.025, 0], "to": [0, -0.165, 0], "ti": [0, -0.097, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 93.866, "s": [407, 13.816, 0], "to": [0, 0.048, 0], "ti": [0, -0.188, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 94.934, "s": [407, 13.657, 0], "to": [0, 0.581, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 96, "s": [407, 13.322, 0], "to": [0, -0.112, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 97.066, "s": [407, 13.085, 0], "to": [0, -0.162, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 98.134, "s": [407, 12.724, 0], "to": [0, -0.196, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 99.2, "s": [407, 12.3, 0], "to": [0, -0.249, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100.267, "s": [407, 11.901, 0], "to": [0, -0.246, 0], "ti": [0, -0.16, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 101.333, "s": [407, 11.369, 0], "to": [0, 0.282, 0], "ti": [0, 0.525, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 102.4, "s": [407, 10.87, 0], "to": [0, -0.23, 0], "ti": [0, 0.281, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 103.467, "s": [407, 10.336, 0], "to": [0, -0.273, 0], "ti": [0, 0.279, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 104.533, "s": [407, 9.739, 0], "to": [0, -0.333, 0], "ti": [0, 0.264, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 105.6, "s": [407, 9.11, 0], "to": [0, -0.445, 0], "ti": [0, -0.447, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 106.667, "s": [407, 8.458, 0], "to": [0, 0.324, 0], "ti": [0, 0.665, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 107.733, "s": [407, 7.777, 0], "to": [0, -0.288, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 108.8, "s": [407, 6.985, 0], "to": [0, -0.332, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 109.866, "s": [407, 6.212, 0], "to": [0, -0.389, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 110.934, "s": [407, 5.416, 0], "to": [0, -0.711, 0], "ti": [0, -0.632, 0]}, {"t": 112, "s": [407, 4.625, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-729, 50.846, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-318, 50.846, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-111, 35.846, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.449019607843, 0.898039215686, 0.658780625287, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 3", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-411, 0, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [0, 0, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.449019607843, 0.898039215686, 0.658780625287, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape Layer 1", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-818, -4, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-407, -4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.449019607843, 0.898039215686, 0.658780625287, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "White", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Move", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [197, 57, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "Null 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [407, 1.375, 0], "to": [0, 1.233, 0], "ti": [0, 0.357, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 1.066, "s": [407, 0.514, 0], "to": [0, -0.222, 0], "ti": [0, 0.4, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 2.134, "s": [407, -0.394, 0], "to": [0, -0.308, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 3.2, "s": [407, -1.318, 0], "to": [0, -0.349, 0], "ti": [0, 0.308, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 4.267, "s": [407, -2.388, 0], "to": [0, -0.824, 0], "ti": [0, -0.586, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 5.333, "s": [407, -3.338, 0], "to": [0, 0.827, 0], "ti": [0, 0.926, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 6.4, "s": [407, -4.471, 0], "to": [0, -0.374, 0], "ti": [0, 0.466, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 7.467, "s": [407, -5.626, 0], "to": [0, -0.419, 0], "ti": [0, 0.437, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 8.533, "s": [407, -6.738, 0], "to": [0, -0.465, 0], "ti": [0, 0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 9.6, "s": [407, -7.976, 0], "to": [0, -0.558, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 10.667, "s": [407, -9.212, 0], "to": [0, -0.487, 0], "ti": [0, -0.415, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 11.733, "s": [407, -10.656, 0], "to": [0, 0.668, 0], "ti": [0, 0.941, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 12.8, "s": [407, -11.879, 0], "to": [0, -0.387, 0], "ti": [0, 0.454, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 13.866, "s": [407, -13.413, 0], "to": [0, -0.416, 0], "ti": [0, 0.38, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 14.934, "s": [407, -14.621, 0], "to": [0, -0.473, 0], "ti": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 16, "s": [407, -15.767, 0], "to": [0, -0.594, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.066, "s": [407, -16.859, 0], "to": [0, 0.602, 0], "ti": [0, 0.753, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 18.134, "s": [407, -17.951, 0], "to": [0, -0.304, 0], "ti": [0, 0.356, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 19.2, "s": [407, -18.957, 0], "to": [0, -0.335, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 20.267, "s": [407, -19.897, 0], "to": [0, -0.378, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 21.333, "s": [407, -20.796, 0], "to": [0, -0.657, 0], "ti": [0, -0.636, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 22.4, "s": [407, -21.613, 0], "to": [0, 0.523, 0], "ti": [0, 0.676, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 23.467, "s": [407, -22.434, 0], "to": [0, -0.285, 0], "ti": [0, 0.341, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 24.533, "s": [407, -23.204, 0], "to": [0, -0.325, 0], "ti": [0, 0.321, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 25.6, "s": [407, -23.942, 0], "to": [0, -0.375, 0], "ti": [0, 0.292, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 26.667, "s": [407, -24.603, 0], "to": [0, -0.676, 0], "ti": [0, -1.05, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27.733, "s": [407, -25.178, 0], "to": [0, 0.429, 0], "ti": [0, 0.135, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 28.8, "s": [407, -25.694, 0], "to": [0, -0.08, 0], "ti": [0, 0.11, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 29.866, "s": [407, -26.235, 0], "to": [0, -0.082, 0], "ti": [0, 0.067, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 30.934, "s": [407, -26.645, 0], "to": [0, -0.074, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32, "s": [407, -26.987, 0], "to": [0, -0.052, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 33.066, "s": [407, -27.337, 0], "to": [0, -0.003, 0], "ti": [0, 0.024, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 34.134, "s": [407, -27.5, 0], "to": [0, -0.084, 0], "ti": [0, 0.063, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 35.2, "s": [407, -27.783, 0], "to": [0, -0.123, 0], "ti": [0, 0.031, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 36.267, "s": [407, -27.952, 0], "to": [0, -0.052, 0], "ti": [0, -0.185, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 37.333, "s": [407, -27.959, 0], "to": [0, 0.054, 0], "ti": [0, -0.133, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 38.4, "s": [407, -28.011, 0], "to": [0, 0.084, 0], "ti": [0, -0.06, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 39.467, "s": [407, -27.972, 0], "to": [0, 0.128, 0], "ti": [0, -0.146, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 40.533, "s": [407, -27.932, 0], "to": [0, 0.153, 0], "ti": [0, -0.176, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 41.6, "s": [407, -27.806, 0], "to": [0, 0.188, 0], "ti": [0, -0.214, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 42.667, "s": [407, -27.69, 0], "to": [0, 0.249, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 43.733, "s": [407, -27.465, 0], "to": [0, 0.508, 0], "ti": [0, 0.14, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 44.8, "s": [407, -27.168, 0], "to": [0, -0.61, 0], "ti": [0, -0.711, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 45.866, "s": [407, -26.678, 0], "to": [0, 0.301, 0], "ti": [0, -0.394, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 46.934, "s": [407, -26.177, 0], "to": [0, 0.37, 0], "ti": [0, -0.426, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 48, "s": [407, -25.584, 0], "to": [0, 0.46, 0], "ti": [0, -0.497, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 49.066, "s": [407, -24.975, 0], "to": [0, 1.076, 0], "ti": [0, 0.213, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 50.134, "s": [407, -24.18, 0], "to": [0, -0.161, 0], "ti": [0, -1.082, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51.2, "s": [407, -23.3, 0], "to": [0, 0.521, 0], "ti": [0, -0.68, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 52.267, "s": [407, -22.217, 0], "to": [0, 0.628, 0], "ti": [0, -0.71, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 53.333, "s": [407, -21.177, 0], "to": [0, 0.759, 0], "ti": [0, -0.814, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 54.4, "s": [407, -19.881, 0], "to": [0, 1.001, 0], "ti": [0, -0.939, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 55.467, "s": [407, -18.482, 0], "to": [0, 1.017, 0], "ti": [0, 0.225, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 56.533, "s": [407, -16.99, 0], "to": [0, -0.272, 0], "ti": [0, -1.662, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 57.6, "s": [407, -15.101, 0], "to": [0, 0.805, 0], "ti": [0, -1.002, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58.667, "s": [407, -13.023, 0], "to": [0, 0.949, 0], "ti": [0, -1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 59.733, "s": [407, -11.104, 0], "to": [0, 1.175, 0], "ti": [0, -1.1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 60.8, "s": [407, -9.283, 0], "to": [0, 1.697, 0], "ti": [0, 0.385, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 61.866, "s": [407, -7.489, 0], "to": [0, -0.418, 0], "ti": [0, -1.407, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62.934, "s": [407, -5.666, 0], "to": [0, 0.625, 0], "ti": [0, -0.76, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 64, "s": [407, -3.992, 0], "to": [0, 0.717, 0], "ti": [0, -0.749, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65.066, "s": [407, -2.445, 0], "to": [0, 0.853, 0], "ti": [0, -0.802, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 66.134, "s": [407, -0.949, 0], "to": [0, 1.624, 0], "ti": [0, 0.483, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 67.2, "s": [407, 0.46, 0], "to": [0, -0.451, 0], "ti": [0, -1.106, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 68.267, "s": [407, 1.779, 0], "to": [0, 0.471, 0], "ti": [0, -0.58, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 69.333, "s": [407, 2.944, 0], "to": [0, 0.544, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 70.4, "s": [407, 4.282, 0], "to": [0, 0.626, 0], "ti": [0, -0.59, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 71.467, "s": [407, 5.272, 0], "to": [0, 1.484, 0], "ti": [0, 0.539, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 72.533, "s": [407, 6.27, 0], "to": [0, -0.442, 0], "ti": [0, -0.818, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 73.6, "s": [407, 7.287, 0], "to": [0, 0.335, 0], "ti": [0, -0.412, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 74.667, "s": [407, 8.236, 0], "to": [0, 0.377, 0], "ti": [0, -0.397, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 75.733, "s": [407, 8.974, 0], "to": [0, 0.437, 0], "ti": [0, -0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 76.8, "s": [407, 9.737, 0], "to": [0, 1.243, 0], "ti": [0, 0.576, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 77.866, "s": [407, 10.453, 0], "to": [0, -0.4, 0], "ti": [0, -0.531, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 78.934, "s": [407, 11.092, 0], "to": [0, 0.216, 0], "ti": [0, -0.269, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 80, "s": [407, 11.643, 0], "to": [0, 0.246, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 81.066, "s": [407, 12.227, 0], "to": [0, 0.275, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 82.134, "s": [407, 12.61, 0], "to": [0, 0.337, 0], "ti": [0, -0.287, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 83.2, "s": [407, 13.036, 0], "to": [0, 0.475, 0], "ti": [0, 0.435, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84.267, "s": [407, 13.4, 0], "to": [0, -0.37, 0], "ti": [0, -0.346, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 85.333, "s": [407, 13.659, 0], "to": [0, 0.154, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 86.4, "s": [407, 13.939, 0], "to": [0, 0.183, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 87.467, "s": [407, 14.071, 0], "to": [0, 0.223, 0], "ti": [0, -0.203, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.533, "s": [407, 14.256, 0], "to": [0, 0.37, 0], "ti": [0, 0.559, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 89.6, "s": [407, 14.312, 0], "to": [0, -0.09, 0], "ti": [0, 0.079, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 90.667, "s": [407, 14.246, 0], "to": [0, -0.093, 0], "ti": [0, 0.08, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 91.733, "s": [407, 14.172, 0], "to": [0, -0.099, 0], "ti": [0, 0.078, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 92.8, "s": [407, 14.025, 0], "to": [0, -0.165, 0], "ti": [0, -0.097, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 93.866, "s": [407, 13.816, 0], "to": [0, 0.048, 0], "ti": [0, -0.188, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 94.934, "s": [407, 13.657, 0], "to": [0, 0.581, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 96, "s": [407, 13.322, 0], "to": [0, -0.112, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 97.066, "s": [407, 13.085, 0], "to": [0, -0.162, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 98.134, "s": [407, 12.724, 0], "to": [0, -0.196, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 99.2, "s": [407, 12.3, 0], "to": [0, -0.249, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100.267, "s": [407, 11.901, 0], "to": [0, -0.246, 0], "ti": [0, -0.16, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 101.333, "s": [407, 11.369, 0], "to": [0, 0.282, 0], "ti": [0, 0.525, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 102.4, "s": [407, 10.87, 0], "to": [0, -0.23, 0], "ti": [0, 0.281, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 103.467, "s": [407, 10.336, 0], "to": [0, -0.273, 0], "ti": [0, 0.279, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 104.533, "s": [407, 9.739, 0], "to": [0, -0.333, 0], "ti": [0, 0.264, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 105.6, "s": [407, 9.11, 0], "to": [0, -0.445, 0], "ti": [0, -0.447, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 106.667, "s": [407, 8.458, 0], "to": [0, 0.324, 0], "ti": [0, 0.665, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 107.733, "s": [407, 7.777, 0], "to": [0, -0.288, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 108.8, "s": [407, 6.985, 0], "to": [0, -0.332, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 109.866, "s": [407, 6.212, 0], "to": [0, -0.389, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 110.934, "s": [407, 5.416, 0], "to": [0, -0.711, 0], "ti": [0, -0.632, 0]}, {"t": 112, "s": [407, 4.625, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-729, 50.846, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-318, 50.846, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-111, 35.846, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape Layer 3", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-411, 0, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [0, 0, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Shape Layer 1", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-818, -4, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-407, -4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "Orange", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Move", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [197, 57, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "Null 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [407, 1.375, 0], "to": [0, 1.233, 0], "ti": [0, 0.357, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 1.066, "s": [407, 0.514, 0], "to": [0, -0.222, 0], "ti": [0, 0.4, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 2.134, "s": [407, -0.394, 0], "to": [0, -0.308, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 3.2, "s": [407, -1.318, 0], "to": [0, -0.349, 0], "ti": [0, 0.308, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 4.267, "s": [407, -2.388, 0], "to": [0, -0.824, 0], "ti": [0, -0.586, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 5.333, "s": [407, -3.338, 0], "to": [0, 0.827, 0], "ti": [0, 0.926, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 6.4, "s": [407, -4.471, 0], "to": [0, -0.374, 0], "ti": [0, 0.466, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 7.467, "s": [407, -5.626, 0], "to": [0, -0.419, 0], "ti": [0, 0.437, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 8.533, "s": [407, -6.738, 0], "to": [0, -0.465, 0], "ti": [0, 0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 9.6, "s": [407, -7.976, 0], "to": [0, -0.558, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 10.667, "s": [407, -9.212, 0], "to": [0, -0.487, 0], "ti": [0, -0.415, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 11.733, "s": [407, -10.656, 0], "to": [0, 0.668, 0], "ti": [0, 0.941, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 12.8, "s": [407, -11.879, 0], "to": [0, -0.387, 0], "ti": [0, 0.454, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 13.866, "s": [407, -13.413, 0], "to": [0, -0.416, 0], "ti": [0, 0.38, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 14.934, "s": [407, -14.621, 0], "to": [0, -0.473, 0], "ti": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 16, "s": [407, -15.767, 0], "to": [0, -0.594, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.066, "s": [407, -16.859, 0], "to": [0, 0.602, 0], "ti": [0, 0.753, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 18.134, "s": [407, -17.951, 0], "to": [0, -0.304, 0], "ti": [0, 0.356, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 19.2, "s": [407, -18.957, 0], "to": [0, -0.335, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 20.267, "s": [407, -19.897, 0], "to": [0, -0.378, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 21.333, "s": [407, -20.796, 0], "to": [0, -0.657, 0], "ti": [0, -0.636, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 22.4, "s": [407, -21.613, 0], "to": [0, 0.523, 0], "ti": [0, 0.676, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 23.467, "s": [407, -22.434, 0], "to": [0, -0.285, 0], "ti": [0, 0.341, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 24.533, "s": [407, -23.204, 0], "to": [0, -0.325, 0], "ti": [0, 0.321, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 25.6, "s": [407, -23.942, 0], "to": [0, -0.375, 0], "ti": [0, 0.292, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 26.667, "s": [407, -24.603, 0], "to": [0, -0.676, 0], "ti": [0, -1.05, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 27.733, "s": [407, -25.178, 0], "to": [0, 0.429, 0], "ti": [0, 0.135, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 28.8, "s": [407, -25.694, 0], "to": [0, -0.08, 0], "ti": [0, 0.11, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 29.866, "s": [407, -26.235, 0], "to": [0, -0.082, 0], "ti": [0, 0.067, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 30.934, "s": [407, -26.645, 0], "to": [0, -0.074, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32, "s": [407, -26.987, 0], "to": [0, -0.052, 0], "ti": [0, 0.013, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 33.066, "s": [407, -27.337, 0], "to": [0, -0.003, 0], "ti": [0, 0.024, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 34.134, "s": [407, -27.5, 0], "to": [0, -0.084, 0], "ti": [0, 0.063, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 35.2, "s": [407, -27.783, 0], "to": [0, -0.123, 0], "ti": [0, 0.031, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 36.267, "s": [407, -27.952, 0], "to": [0, -0.052, 0], "ti": [0, -0.185, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 37.333, "s": [407, -27.959, 0], "to": [0, 0.054, 0], "ti": [0, -0.133, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 38.4, "s": [407, -28.011, 0], "to": [0, 0.084, 0], "ti": [0, -0.06, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 39.467, "s": [407, -27.972, 0], "to": [0, 0.128, 0], "ti": [0, -0.146, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 40.533, "s": [407, -27.932, 0], "to": [0, 0.153, 0], "ti": [0, -0.176, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 41.6, "s": [407, -27.806, 0], "to": [0, 0.188, 0], "ti": [0, -0.214, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 42.667, "s": [407, -27.69, 0], "to": [0, 0.249, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 43.733, "s": [407, -27.465, 0], "to": [0, 0.508, 0], "ti": [0, 0.14, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 44.8, "s": [407, -27.168, 0], "to": [0, -0.61, 0], "ti": [0, -0.711, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 45.866, "s": [407, -26.678, 0], "to": [0, 0.301, 0], "ti": [0, -0.394, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 46.934, "s": [407, -26.177, 0], "to": [0, 0.37, 0], "ti": [0, -0.426, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 48, "s": [407, -25.584, 0], "to": [0, 0.46, 0], "ti": [0, -0.497, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 49.066, "s": [407, -24.975, 0], "to": [0, 1.076, 0], "ti": [0, 0.213, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 50.134, "s": [407, -24.18, 0], "to": [0, -0.161, 0], "ti": [0, -1.082, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 51.2, "s": [407, -23.3, 0], "to": [0, 0.521, 0], "ti": [0, -0.68, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 52.267, "s": [407, -22.217, 0], "to": [0, 0.628, 0], "ti": [0, -0.71, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 53.333, "s": [407, -21.177, 0], "to": [0, 0.759, 0], "ti": [0, -0.814, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 54.4, "s": [407, -19.881, 0], "to": [0, 1.001, 0], "ti": [0, -0.939, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 55.467, "s": [407, -18.482, 0], "to": [0, 1.017, 0], "ti": [0, 0.225, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 56.533, "s": [407, -16.99, 0], "to": [0, -0.272, 0], "ti": [0, -1.662, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 57.6, "s": [407, -15.101, 0], "to": [0, 0.805, 0], "ti": [0, -1.002, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58.667, "s": [407, -13.023, 0], "to": [0, 0.949, 0], "ti": [0, -1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 59.733, "s": [407, -11.104, 0], "to": [0, 1.175, 0], "ti": [0, -1.1, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 60.8, "s": [407, -9.283, 0], "to": [0, 1.697, 0], "ti": [0, 0.385, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 61.866, "s": [407, -7.489, 0], "to": [0, -0.418, 0], "ti": [0, -1.407, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 62.934, "s": [407, -5.666, 0], "to": [0, 0.625, 0], "ti": [0, -0.76, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 64, "s": [407, -3.992, 0], "to": [0, 0.717, 0], "ti": [0, -0.749, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 65.066, "s": [407, -2.445, 0], "to": [0, 0.853, 0], "ti": [0, -0.802, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 66.134, "s": [407, -0.949, 0], "to": [0, 1.624, 0], "ti": [0, 0.483, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 67.2, "s": [407, 0.46, 0], "to": [0, -0.451, 0], "ti": [0, -1.106, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 68.267, "s": [407, 1.779, 0], "to": [0, 0.471, 0], "ti": [0, -0.58, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 69.333, "s": [407, 2.944, 0], "to": [0, 0.544, 0], "ti": [0, -0.581, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 70.4, "s": [407, 4.282, 0], "to": [0, 0.626, 0], "ti": [0, -0.59, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 71.467, "s": [407, 5.272, 0], "to": [0, 1.484, 0], "ti": [0, 0.539, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 72.533, "s": [407, 6.27, 0], "to": [0, -0.442, 0], "ti": [0, -0.818, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 73.6, "s": [407, 7.287, 0], "to": [0, 0.335, 0], "ti": [0, -0.412, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 74.667, "s": [407, 8.236, 0], "to": [0, 0.377, 0], "ti": [0, -0.397, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 75.733, "s": [407, 8.974, 0], "to": [0, 0.437, 0], "ti": [0, -0.429, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 76.8, "s": [407, 9.737, 0], "to": [0, 1.243, 0], "ti": [0, 0.576, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 77.866, "s": [407, 10.453, 0], "to": [0, -0.4, 0], "ti": [0, -0.531, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 78.934, "s": [407, 11.092, 0], "to": [0, 0.216, 0], "ti": [0, -0.269, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 80, "s": [407, 11.643, 0], "to": [0, 0.246, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 81.066, "s": [407, 12.227, 0], "to": [0, 0.275, 0], "ti": [0, -0.262, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 82.134, "s": [407, 12.61, 0], "to": [0, 0.337, 0], "ti": [0, -0.287, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 83.2, "s": [407, 13.036, 0], "to": [0, 0.475, 0], "ti": [0, 0.435, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 84.267, "s": [407, 13.4, 0], "to": [0, -0.37, 0], "ti": [0, -0.346, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 85.333, "s": [407, 13.659, 0], "to": [0, 0.154, 0], "ti": [0, -0.19, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 86.4, "s": [407, 13.939, 0], "to": [0, 0.183, 0], "ti": [0, -0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 87.467, "s": [407, 14.071, 0], "to": [0, 0.223, 0], "ti": [0, -0.203, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 88.533, "s": [407, 14.256, 0], "to": [0, 0.37, 0], "ti": [0, 0.559, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 89.6, "s": [407, 14.312, 0], "to": [0, -0.09, 0], "ti": [0, 0.079, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 90.667, "s": [407, 14.246, 0], "to": [0, -0.093, 0], "ti": [0, 0.08, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 91.733, "s": [407, 14.172, 0], "to": [0, -0.099, 0], "ti": [0, 0.078, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 92.8, "s": [407, 14.025, 0], "to": [0, -0.165, 0], "ti": [0, -0.097, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 93.866, "s": [407, 13.816, 0], "to": [0, 0.048, 0], "ti": [0, -0.188, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 94.934, "s": [407, 13.657, 0], "to": [0, 0.581, 0], "ti": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 96, "s": [407, 13.322, 0], "to": [0, -0.112, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 97.066, "s": [407, 13.085, 0], "to": [0, -0.162, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 98.134, "s": [407, 12.724, 0], "to": [0, -0.196, 0], "ti": [0, 0.201, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 99.2, "s": [407, 12.3, 0], "to": [0, -0.249, 0], "ti": [0, 0.196, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 100.267, "s": [407, 11.901, 0], "to": [0, -0.246, 0], "ti": [0, -0.16, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 101.333, "s": [407, 11.369, 0], "to": [0, 0.282, 0], "ti": [0, 0.525, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 102.4, "s": [407, 10.87, 0], "to": [0, -0.23, 0], "ti": [0, 0.281, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 103.467, "s": [407, 10.336, 0], "to": [0, -0.273, 0], "ti": [0, 0.279, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 104.533, "s": [407, 9.739, 0], "to": [0, -0.333, 0], "ti": [0, 0.264, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 105.6, "s": [407, 9.11, 0], "to": [0, -0.445, 0], "ti": [0, -0.447, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 106.667, "s": [407, 8.458, 0], "to": [0, 0.324, 0], "ti": [0, 0.665, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 107.733, "s": [407, 7.777, 0], "to": [0, -0.288, 0], "ti": [0, 0.353, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 108.8, "s": [407, 6.985, 0], "to": [0, -0.332, 0], "ti": [0, 0.337, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 109.866, "s": [407, 6.212, 0], "to": [0, -0.389, 0], "ti": [0, 0.327, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 110.934, "s": [407, 5.416, 0], "to": [0, -0.711, 0], "ti": [0, -0.632, 0]}, {"t": 112, "s": [407, 4.625, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 112.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-729, 50.846, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-318, 50.846, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-111, 35.846, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.772647274242, 0.648636642157, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 3", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-411, 0, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [0, 0, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.772647274242, 0.648636642157, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape Layer 1", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-818, -4, 0], "to": [68.5, 0, 0], "ti": [-68.5, 0, 0]}, {"t": 112, "s": [-407, -4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-88, -50]], "o": [[0, 0], [88, 50]], "v": [[-215, 43.5], [-7, 47.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.772647274242, 0.648636642157, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 101, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 112.5, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "City", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [462.5, 406, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [462.5, 406, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 925, "h": 812, "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": [], "props": {}}