/* eslint-disable no-underscore-dangle */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
// workbox.core.skipWaiting();
import { clientsClaim } from 'workbox-core';
import { precacheAndRoute } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { CacheFirst } from 'workbox-strategies';
import { CacheableResponsePlugin } from 'workbox-cacheable-response';
import { ExpirationPlugin } from 'workbox-expiration';

const getCommonModulesEntry = require('../build-utils/common-modules-entry');
const { getKYCMiniEntryPath } = require('../build-utils/module-entry-paths');

const commonModulesEntry = getCommonModulesEntry(__COMMON_MODULES_ENV__);
const kycEntryPath = getKYCMiniEntryPath(__KYC_MINI_ENV__);

clientsClaim();

self.__precacheManifest = [].concat(
  self.__precacheManifest || self.__WB_MANIFEST,
);

precacheAndRoute(self.__precacheManifest);

self.addEventListener('activate', event => {
  const CACHE_TO_BE_REFRESHED_ON_UPDATE = [
    'chunks',
    'web-chunks',
    'kyc-chunks',
    'kyc-images',
  ];
  event.waitUntil(
    caches
      .keys()
      .then(keys =>
        Promise.all(
          keys.map(
            key =>
              CACHE_TO_BE_REFRESHED_ON_UPDATE.includes(key) &&
              caches.delete(key),
          ),
        ),
      )
      .then(() => {
        // log('new service worker is ready to handle fetches!');
      }),
  );
});

registerRoute(
  ({ url }) =>
    url.origin === 'https://static.paytmmoney.com' &&
    (url.pathname.startsWith('/images/') || url.pathname.startsWith('/logos/')),
  new CacheFirst({
    cacheName: 'be-images',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 50,
        matchOptions: {
          ignoreVary: true,
        },
      }),
    ],
  }),
);

const isAsset = pathname =>
  pathname.endsWith('.png') ||
  pathname.endsWith('.jpg') ||
  pathname.endsWith('.svg');

registerRoute(
  ({ url }) => url.origin === self.location.origin && isAsset(url.pathname),
  new CacheFirst({
    cacheName: 'static-images',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 150,
        matchOptions: {
          ignoreVary: true,
        },
      }),
    ],
  }),
);

registerRoute(
  ({ url }) => url.origin === commonModulesEntry && isAsset(url.pathname),
  new CacheFirst({
    cacheName: 'web-images',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 150,
        matchOptions: {
          ignoreVary: true,
        },
      }),
    ],
  }),
);

registerRoute(
  ({ url }) => url.origin === kycEntryPath && isAsset(url.pathname),
  new CacheFirst({
    cacheName: 'kyc-images',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
      new ExpirationPlugin({
        maxEntries: 150,
        matchOptions: {
          ignoreVary: true,
        },
      }),
    ],
  }),
);

registerRoute(
  ({ url }) =>
    url.origin === self.location.origin &&
    (url.pathname.includes(`/static/js/`) ||
      url.pathname.includes(`/static/css/`)),
  new CacheFirst({
    cacheName: 'chunks',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  }),
);

registerRoute(
  ({ url }) =>
    url.origin === commonModulesEntry &&
    (url.pathname.endsWith(`.js`) || url.pathname.endsWith(`.css`)) &&
    !url.pathname.includes('moduleEntry.js') &&
    !url.pathname.includes('moduleEntryFnOV2.js'),
  new CacheFirst({
    cacheName: 'web-chunks',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  }),
);

registerRoute(
  ({ url }) =>
    url.origin === kycEntryPath &&
    (url.pathname.endsWith(`.js`) || url.pathname.endsWith(`.css`)) &&
    !url.pathname.includes('moduleEntry.js') &&
    !url.pathname.includes('moduleEntryFnOV2.js'),
  new CacheFirst({
    cacheName: 'kyc-chunks',
    plugins: [
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  }),
);

// self.addEventListener('message', event => {
//   if (event.data && event.data.type === 'SKIP_WAITING') {
//     self.skipWaiting();
//   }
// });

self.skipWaiting();
