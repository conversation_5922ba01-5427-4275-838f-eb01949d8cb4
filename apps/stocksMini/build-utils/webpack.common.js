const commonPaths = require('./common-paths');
const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const { ModuleFederationPlugin } = require('webpack').container;
const { dependencies } = require('../package.json');

const isDebug = !process.argv.includes('release');
const removeConsoleLogs = process.argv.includes('noconsole');
const commonModulesPath = commonPaths.getCommonModulesPath(
  process.env.COMMON_MODULES_ENV,
);
const kycMiniPath = commonPaths.getKYCMiniEntryPath(process.env.KYC_MINI_ENV);
const PMLWidgetsPath = commonPaths.getPMLWidgetsEntryPath(
  process.env.COMMON_MODULES_ENV,
);

const config = {
  entry: {},
  output: {
    path: commonPaths.outputPath,
    publicPath: '/',
    assetModuleFilename: isDebug
      ? `images/[path][name].[contenthash:8][ext]`
      : `images/[path][contenthash:8][ext]`,
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules\/(?!kyc-mini)/,
        use: ['babel-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.(woff(2)?|eot|ttf|otf|svg)$/,
        type: 'asset/resource',
      },
      { test: /\.css$/, use: ['style-loader', 'css-loader'] },
      {
        test: /\.(scss|sass)$/,
        exclude: /node_modules\/(?!kyc-mini)/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              modules: {
                compileType: 'module',
                mode: 'local',
                localIdentName: isDebug
                  ? '[name]-[local]-[hash:base64:5]'
                  : '[hash:base64:5]',
              },
              sourceMap: isDebug,
              importLoaders: 1,
            },
          },
          'sass-loader',
          {
            loader: 'sass-resources-loader',
            options: {
              resources: commonPaths.commonCssStyles,
              sassOptions: {
                quietDeps: true,
              },
            },
          },
        ],
      },
    ],
  },
  optimization: {
    minimize: !isDebug,
    minimizer: isDebug
      ? []
      : [
          new TerserPlugin({
            terserOptions: {
              sourceMap: true,
              compress: {
                inline: false,
                drop_console: !!removeConsoleLogs,
              },
            },
          }),
          new CssMinimizerPlugin({
            minimizerOptions: {
              preset: [
                'default',
                {
                  discardComments: { removeAll: true },
                },
              ],
            },
          }),
        ],
    runtimeChunk: false,
    splitChunks: {
      chunks: 'all',
      maxSize: 500000,
      cacheGroups: {
        commons: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    },
    sideEffects: false,
  },
  performance: {
    hints: isDebug ? 'warning' : 'warning',
    maxAssetSize: 250000,
    maxEntrypointSize: 1000000,
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
    new HtmlWebpackPlugin({
      template: `public/index.html`,
      favicon: `public/favicon.ico`,
    }),
    new webpack.DefinePlugin({
      __ENV__: `'${process.env.NODE_ENV}'`,
      __BUILD__: `'${process.env.BUILD_ENV}'`,
      __WEBVITALS__: `'${process.env.WEB_VITALS}'`,
      __COMMON_MODULES_ENV__: `'${process.env.COMMON_MODULES_ENV}'`,
      __KYC_MINI_ENV__: `'${process.env.KYC_MINI_ENV}'`,
      __DEV__: isDebug,
    }),
    new CopyPlugin({
      patterns: [
        {
          from: 'public/AppShell',
          to: `${commonPaths.outputPath}/AppShell`,
        },
        { from: '.well-known', to: `${commonPaths.outputPath}/.well-known` },
        { from: 'public/mocks', to: 'mocks' },
      ],
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),

    new Dotenv({
      path: `./.env.${process.env.ENV_NAME || process.env.NODE_ENV}`,
    }),
    new ModuleFederationPlugin({
      name: 'StocksMini',
      filename: 'moduleEntry.js',
      remotes: {
        PMLCOMMON: commonModulesPath,
        KYCMINI: kycMiniPath,
        PMLWIDGETS: PMLWidgetsPath,
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: dependencies.react,
        },
        'react-dom': {
          singleton: true,
          requiredVersion: dependencies['react-dom'],
        },
        'react-query': {
          singleton: true,
          requiredVersion: dependencies['react-query'],
        },
        'universal-router': {
          singleton: true,
          requiredVersion: dependencies['universal-router'],
        },
        history: {
          singleton: true,
          requiredVersion: dependencies.history,
        },
      },
    }),
  ],
  resolve: {
    extensions: ['.js', '.json'], // Add default extensions
    fallback: {
      process: require.resolve('process/browser.js'),
    },
    alias: {
      '@src': path.resolve(__dirname, '../src'),
      '@assets': path.resolve(__dirname, '../src/assets'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@config': path.resolve(__dirname, '../src/config'),
      '@context': path.resolve(__dirname, '../src/context'),
      '@HOC': path.resolve(__dirname, '../src/HOC'),
      '@layout': path.resolve(__dirname, '../src/layout'),
      '@query': path.resolve(__dirname, '../src/query'),
      '@services': path.resolve(__dirname, '../src/services'),
      '@utils': path.resolve(__dirname, '../src/utils'),
      '@paytm-money/store': require.resolve('../../../shared/src/index.js'),
      react: require.resolve('react'),
      'react-dom': require.resolve('react-dom'),
    },
  },
};

module.exports = config;
