const path = require('path');

const commonModulesEntry = require('./common-modules-entry');
const moduleEntryPaths = require('./module-entry-paths');

const PROJECT_ROOT = path.resolve(__dirname, '../');
const COMMON_STYLES = path.join(PROJECT_ROOT, 'src', 'commonStyles');
// const getCommonModulesPath = env =>
//   `PMLCOMMON@${commonModulesEntry(env)}/moduleEntryFnOV2.js`;
const getCommonModulesPath = env =>
  `PMLCOMMON@http://localhost:5007/moduleEntryFnOV2.js`;

const getKYCMiniEntryPath = env =>
  `KYCMINI@${moduleEntryPaths.getKYCMiniEntryPath(env)}/moduleEntry.js`;

// const getPMLWidgetsEntryPath = env =>
//   `PMLWIDGETS@${moduleEntryPaths.getPMLWidgetsEntryPath(
//     env,
//   )}/moduleEntryWidget02.js`;
const getPMLWidgetsEntryPath = env =>
  `PMLWIDGETS@http://localhost:3002/moduleEntryWidget02.js`;

module.exports = {
  projectRoot: PROJECT_ROOT,
  outputPath: path.join(PROJECT_ROOT, 'build'),
  appEntry: path.join(PROJECT_ROOT, 'src'),
  buildTools: path.join(PROJECT_ROOT, 'build-utils'),
  commonCssStyles: [
    path.join(COMMON_STYLES, 'colors.scss'),
    path.join(COMMON_STYLES, 'commoncss.scss'),
    path.join(COMMON_STYLES, 'custom-font.scss'),
    path.join(COMMON_STYLES, 'modified-normalize.scss'),
    path.join(COMMON_STYLES, 'variables.scss'),
  ],
  getCommonModulesPath,
  getKYCMiniEntryPath,
  getPMLWidgetsEntryPath,
};
