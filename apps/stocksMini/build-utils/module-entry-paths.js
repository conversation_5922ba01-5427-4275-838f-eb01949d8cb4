const getKYCMiniEntryPath = env => {
  let kycEntryPath = 'https://kyc-staging.paytmmoney.com';

  if (env === 'prod') {
    kycEntryPath = 'https://kyc.paytmmoney.com';
  } else if (env === 'preprod') {
    kycEntryPath = 'https://kyc-preprod.paytmmoney.com';
  }
  return kycEntryPath;
};

const getPMLWidgetsEntryPath = env => {
  let pmlWidgetsEntryPath = 'https://pml-widgets-stg.paytmmoney.com';

  if (env === 'preprod') {
    pmlWidgetsEntryPath = 'https://pml-widgets-beta.paytmmoney.com';
  } else if (env === 'prod') {
    pmlWidgetsEntryPath = 'https://pml-widgets.paytmmoney.com';
  }
  return pmlWidgetsEntryPath;
};

module.exports = {
  getKYCMiniEntryPath,
  getPMLWidgetsEntryPath,
};
