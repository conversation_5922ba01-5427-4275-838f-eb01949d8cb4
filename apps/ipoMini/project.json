{"name": "ipoMini", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ipoMini/src", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project ipoMini --web", "targets": {"build": {"executor": "@nrwl/react:webpack", "options": {"outputPath": "dist/apps/ipoMini", "main": "apps/ipoMini/src/main.js", "tsConfig": "apps/ipoMini/tsconfig.app.json", "webpackConfig": "apps/ipoMini/webpack.config.js", "externalDependencies": "all"}}}}