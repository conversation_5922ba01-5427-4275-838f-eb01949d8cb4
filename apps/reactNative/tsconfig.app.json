{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["node"]}, "files": ["../../node_modules/@nx/react-native/typings/svg.d.ts"], "exclude": ["src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/test-setup.ts"], "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"]}