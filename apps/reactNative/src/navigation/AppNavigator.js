import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Lazy load screens
const CompanyDetail = lazy(() => import('../pages/CompanyPage'));
const OrderPad = lazy(() => import('../pages/OrderPad'));

// Create stack navigator
const Stack = createNativeStackNavigator();

// Default parameters
const DEFAULT_COMPANY_ID = "1000000118";

// Loading component to show while screens are loading
const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#1576DB" />
  </View>
);

// Define the types for our routes (useful for TypeScript)
export const ROUTES = {
  COMPANY_DETAIL: 'CompanyDetail',
  ORDER_PAD: 'OrderPad',
};

// Screen wrapper with Suspense
const ScreenWithSuspense = ({ component: Component, ...props }) => (
  <Suspense fallback={<LoadingScreen />}>
    <Component {...props} />
  </Suspense>
);

const AppNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName={ROUTES.COMPANY_DETAIL}
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFFFFF' }
      }}
    >
      <Stack.Screen 
        name={ROUTES.COMPANY_DETAIL}
        options={{ title: 'Company Details' }}
        initialParams={{ companyId: DEFAULT_COMPANY_ID }}
      >
        {(props) => <ScreenWithSuspense component={CompanyDetail} {...props} />}
      </Stack.Screen>
      
      <Stack.Screen 
        name={ROUTES.ORDER_PAD}
        options={{ title: 'Order Pad' }}
        initialParams={{ companyId: DEFAULT_COMPANY_ID }}
      >
        {(props) => <ScreenWithSuspense component={OrderPad} {...props} />}
      </Stack.Screen>
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
});

export default AppNavigator; 