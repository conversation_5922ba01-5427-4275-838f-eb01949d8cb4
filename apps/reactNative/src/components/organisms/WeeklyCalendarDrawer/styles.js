import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 10,
    paddingBottom: 30,
  },
  label: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 40,
    borderColor: '#10101021', // FooterBorderGray
    borderWidth: 1,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  labelText: {
    ...typography.body2B1, // 12px, 600 weight, 1.5 lineHeight
    color: '#101010B2', // Text-grey600
  },
  active: {
    backgroundColor: '#013DA6', // var(--background-primary-strong)
    borderColor: '#013DA6',
  },
  activeText: {
    ...typography.body2B1, // maintaining same typography for active state
    color: '#fff', // PureWhite
  },
});
