import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Drawer from '../../molecules/Drawer';
import { WEEK_DAYS, TITLE } from './enums';
import { styles } from './styles';

const WeeklyCalendarDrawer = ({ activeValue, onSelect, isOpen, onClose }) => {
  const handleDatePress = useCallback((id) => {
    onSelect(parseInt(id, 10));
    onClose();
  }, [onSelect, onClose]);

  const renderWeekDay = useCallback(({ id, label }) => {
    const isActive = parseInt(id, 10) === activeValue;
    return (
      <TouchableOpacity
        key={id}
        style={[
          styles.label,
          isActive && styles.active
        ]}
        onPress={() => handleDatePress(id)}
      >
        <Text style={[
          styles.labelText,
          isActive && styles.activeText
        ]}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  }, [activeValue, handleDatePress]);

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={TITLE}
      showCross={false}
      showHandleBar
    >
      <View style={styles.container}>
        {WEEK_DAYS.map(renderWeekDay)}
      </View>
    </Drawer>
  );
};

export default React.memo(WeeklyCalendarDrawer);
