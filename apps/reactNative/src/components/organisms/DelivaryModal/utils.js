import SuccessIcon from '../../../assets/success.svg';
import OrderExecuted from '../../../assets/OrderExecuted.svg';
import InvalidOrder from '../../../assets/InvalidOrder.svg';
export const OVERALL_STATUS = {
  PENDING: 'PENDING',
  TRADED: 'TRADED',
  FAILED: 'FAILED',
};

export const ORDER_STATUS_MAP = {
  [OVERALL_STATUS.PENDING]: {
    status: OVERALL_STATUS.PENDING,
    header: 'Order Placed',
    StatusIcon: SuccessIcon,
    path: 'MINI_APP_ROUTES.ORDERS',
    revampPath: ``,
    cta: 'View Orderbook',
  },
  [OVERALL_STATUS.TRADED]: {
    status: OVERALL_STATUS.TRADED,
    header: 'Order Executed',
    StatusIcon: OrderExecuted,
    path: 'MINI_APP_ROUTES.POSITIONS',
    revampPath: ``,
    cta: 'View Position',
  },
  [OVERALL_STATUS.FAILED]: {
    status: OVERALL_STATUS.FAILED,
    header: 'Invalid Order',
    StatusIcon: InvalidOrder,
    path: '',
    cta: 'Ok, Got it',
  },
};