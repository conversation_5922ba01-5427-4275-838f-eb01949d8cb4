import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    width: Dimensions.get('screen').width - 32,
    borderRadius: 16,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 24,
    padding: 30
  },
  iconContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    width: Dimensions.get('screen').width - 32,
    paddingHorizontal: 16,
    marginTop: -10,
  },
  closeIcon: {
    backgroundColor: 'red'
  },
  title: {
    color: '#2CB079',
    textAlign: "center",
    fontSize: 16,
    fontStyle: "normal",
    fontWeight: 600,
    lineHeight: 22,
    letterSpacing: 0.01,
  },
  [OVERALL_STATUS.TRADED]: {
    color: '#101010'
  },
  [OVERALL_STATUS.FAILED]: {
    color: '#EB4B4B'
  },
  failedDescription: {
    textAlign: 'center'
  },
  primaryBtn: {
    backgroundColor: '#013DA6',

  },

  button: {
    borderRadius: 8,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 14,
    textAlign: 'center',
    height: 'fit-content',
  },
  subTitleContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: 16,
    color: 'rgba(16, 16, 16, 0.54)'
  },
  separator: {
    marginHorizontal: 4,
  }
});

export default styles;