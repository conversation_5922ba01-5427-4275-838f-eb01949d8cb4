import { Text, TouchableOpacity, View } from 'react-native';
import CustomModal from '../../Molecules/Modal';

import { useState } from 'react';
import CloseIcon from '../../../assets/CloseCircle.svg';
import SuitCase from '../../../assets/suitCase.svg';
import Button from '../../atoms/Button/Button';
import { ORDER_STATUS_MAP, OVERALL_STATUS } from './utils';
import styles from './styles';

function DeliveryStatus({ isOpen, data = { status: OVERALL_STATUS.FAILED } }) {

  const [isModal, setIsModal] = useState(true);

  const onClose = () => {
    setIsModal(false);
  }

  const { StatusIcon, header, cta } = ORDER_STATUS_MAP[data.status];

  return <View style={{ height: 300, width: 300 }}>
    <CustomModal
      isOpen={isModal}
      onClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <TouchableOpacity style={styles.closeIcon} onPress={onClose} width={18} height={18}><CloseIcon width={18} height={18} /></TouchableOpacity>
        </View>
        <StatusIcon width={150} height={150} />
        <Text style={[styles.title, styles[data.status]]}>{header}</Text>
        {data.status !== OVERALL_STATUS.FAILED ? <View style={styles.subTitleContainer}>
          <SuitCase width={16} height={16} />
          <Text style={styles.quantity}>1</Text>
          <Text style={styles.separator}>∙</Text>
          <Text style={styles.desc}>NBCC (India)</Text>
          <Text style={styles.separator}>∙</Text>
          <Text style={styles.type}>Delivery</Text>
        </View> : null}
        {data.status === OVERALL_STATUS.FAILED ? <View style={styles.subTitleContainer}>
          <Text style={styles.failedDescription}>Particular scrip is not allowed under bracket order. Please try placing a delivery order with a stop loss order</Text>
        </View> : null}
        {data.status !== OVERALL_STATUS.FAILED ? <Text style={styles.orderNo}>Order No: 1123080731777</Text> : null}
        <Button
          isPrimaryBlue
          buttonText={cta}
          onPress={onClose}
          buttonClassName={[styles.button, styles.primaryBtn]}
          buttonTextClassName={styles.btnText}
        />
      </View>
    </CustomModal>
  </View>
}




export default DeliveryStatus;