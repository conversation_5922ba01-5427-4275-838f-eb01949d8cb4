import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Drawer from '../../molecules/Drawer';
import { TITLE, MAX_DATE } from "./enums"
import { styles } from './styles';


const MonthlyCalendarDrawer = ({ isOpen, onClose, onSelect, activeValue }) => {
    const handleDatePress = (date) => {
        onSelect(date);
        onClose();
    };

    return (
        <Drawer
            isOpen={isOpen}
            onClose={onClose}
            title={TITLE}
            showCross={false}
            showHandleBar
        >
            <View style={styles.container}>
                {Array.from({ length: MAX_DATE }, (_, index) => index + 1).map(date => (
                    <TouchableOpacity
                        key={date}
                        style={[
                            styles.date,
                            date === activeValue && styles.active
                        ]}
                        onPress={() => handleDatePress(date)}
                    >
                        <Text style={[
                            styles.dateText,
                            date === activeValue && styles.activeText
                        ]}>
                            {date}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>
        </Drawer>
    );
};

export default MonthlyCalendarDrawer;
