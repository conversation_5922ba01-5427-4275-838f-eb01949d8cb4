import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    margin: 10,
  },
  date: {
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 25, // Half of height/width for circle
  },
  dateText: {
    ...typography.heading3B1, // 16px, 600 weight, 1.5 lineHeight
    color: '#1D2F54', // DBlue2 from colors
  },
  active: {
    backgroundColor: '#013DA6', // var(--background-primary-strong)
  },
  activeText: {
    ...typography.heading3B1, // maintaining same typography for active state
    color: '#fff', // PureWhite from colors
  },
});
