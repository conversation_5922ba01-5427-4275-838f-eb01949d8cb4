import React from "react"
import {
    useNavigation,
  } from '@react-navigation/native';
  
import {usePeersDetails} from "@paytm-money/store"

import Accordion from "../../atoms/Accordion/Accordion";
import StockCarousel from "../../molecules/StockCarousel/StockCarousel";

const MostBought = ({id}) => {
    const { data: stocks } = usePeersDetails(id);
    const navigation = useNavigation();
    const onCardPress=(item)=>{
        navigation.navigate("CompanyDetail" ,{companyId: item?.id})
    }
    if(!stocks?.length) return null
    return (
        <Accordion
            title="Most Bought Peers"
            isOpen={true}
        >
            <StockCarousel data={stocks} onCardPress={onCardPress}/>
        </Accordion>
    )

}

export default MostBought;