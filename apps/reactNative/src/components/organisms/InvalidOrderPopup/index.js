import React from 'react';
import { View, Text } from 'react-native';
import Drawer from '../../molecules/Drawer';
import OrderPadButton from '../../molecules/OrderPadButton';
import { styles } from './styles';

const InvalidOrderPopup = ({ message, showPopup, onClose, header, cta }) => {
  return (
    <Drawer
      isOpen={showPopup}
      onClose={onClose}
      showHandleBar
      title={header}
      customTitleClassName={styles.header}
    >
      <View style={styles.container}>
        <Text style={styles.message}>{message}</Text>
        <OrderPadButton 
          buttonText={cta} 
          isPrimary
          onClickHandler={onClose}
        />
      </View>
    </Drawer>
  );
};

export default InvalidOrderPopup;
