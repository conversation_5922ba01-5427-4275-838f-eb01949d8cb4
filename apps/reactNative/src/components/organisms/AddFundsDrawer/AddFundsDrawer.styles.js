import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    marginTop: 10,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 11,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    color: '#FD5154', // ETFRed from colors.scss
  },
  desc: {
    margin: 0,
    fontSize: 14,
    color: '#101010', // Black5 from colors.scss
  },
  errorMessage: {
    marginTop: 0,
    marginBottom: 20,
    marginHorizontal: 0,
    fontSize: 14,
    color: '#101010', // Black5 from colors.scss
  },
  icon: {
    marginLeft: 10,
  },
  btnContainer: {
    marginTop: 24,
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
  primaryBtn: {
    backgroundColor: '#013DA6',

  },

  button: {
    borderRadius: 8,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 14,
    textAlign: 'center',
    height: 'fit-content',
  },
  btnText: {

  }
});
