import React, { Fragment } from 'react';
import { Text, View } from 'react-native';
import Button from '../../atoms/Button/Button.js';
import styles from './AddFundsDrawer.styles.js';
import { ADD_FUNDS } from './Statics.js';
import Drawer from '../../Molecules/Drawer/Drawer.js';
import InfoIcon from '../../../assets/info.svg';

function AddFundsDrawer({
  isOpen,
  onClose,
  errorMessage,
  funds,
  quantity,
  events,
  // redirectRoute = MINI_APP_ROUTES.ADD_MONEY,
  // source = MINI_APP_ROUTES.ORDER_PAD,
}) {
  // const navigation = useNavigation();

  const ERROR_MESSAGE = "₹340 additional funds are required to place this order. Available fund is ₹4,500."
  const INSTRUCTION = "Add funds to proceed or retry with a different order";

  const redirectToAddMoney = () => {
    if (events) events.addFunds();
    // navigation.navigate(redirectRoute, {
    //   cashBalance: funds?.toString(),
    //   source,
    //   quantity,
    // });
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>{ADD_FUNDS.HEADER}</Text>
          {/* <Icon name="info-red" size={24} style={styles.icon} /> */}
          <InfoIcon width={18} height={18} />
        </View>
      }
    >
      <Fragment>
        <View style={styles.container}>
          <Text style={styles.errorMessage}>{errorMessage || ERROR_MESSAGE}</Text>
          <View style={{ height: 10 }} />
          <Text style={styles.errorMessage}>{INSTRUCTION}</Text>
          <View style={styles.btnContainer}>
            <Button
              isPrimaryBlue
              buttonText={ADD_FUNDS.PRIMARY_CTA}
              onPress={redirectToAddMoney}
              buttonClassName={[styles.button, styles.primaryBtn]}
              buttonTextClassName={styles.btnText}
            />

            <Button
              isSecondary
              buttonText={ADD_FUNDS.SECONDARY_CTA}
              onPress={() => {
                if (events) events.modifyFunds();
                onClose();
              }}
              buttonClassName={styles.button}
              buttonTextClassName={styles.btnText}
            />
          </View>
        </View>
      </Fragment>
    </Drawer>
  );
}

export default AddFundsDrawer;
