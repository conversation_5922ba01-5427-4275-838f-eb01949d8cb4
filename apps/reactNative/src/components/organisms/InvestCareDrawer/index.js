import React from 'react';
import { View, Text } from 'react-native';
import If from '../../atoms/If';
import Button from '../../atoms/Button/Button';
import InvestCareMessage from '../../atoms/InvestCareMessage/InvestCareMessage';
import Drawer from '../../molecules/Drawer';
import InvestCareSvg from "../../../assets/investCare.svg";
import { STATICS, CTA ,TEMP,TEMP1} from './enums';
import { styles } from './styles';

const InvestCareDrawer = ({
  investCareHandler,
  displayName,
  analyticalEvents,
  isOpen,
  onClose,
}) => {
    // TODO: Remove Contants
  const { onAccept, onReject, nudgeInfo, equityInfoCard } = investCareHandler || {
    onAccept:()=>null,
    onReject:()=>null,
    nudgeInfo:TEMP1,
    equityInfoCard:TEMP
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      showHandleBar
      showCross={false}
    >
      <View style={styles.container}>
        <View style={styles.imageContainer}>
          <InvestCareSvg 
            width={150}
            height={150}
            style={styles.image}
          />
        </View>
        <Text style={styles.header}>{STATICS.HEADER}</Text>
        <If test={nudgeInfo?.is_ban}>
          <InvestCareMessage
            displayName={displayName}
            message={nudgeInfo?.ban_desc}
            ctaText={STATICS.SURVEILLANCE_CTA_TEXT}
            ctaLink={STATICS.SURVEILLANCE_CTA_LINK}
            analyticalEvents={analyticalEvents}
            isFno
          />
        </If>

        <If test={nudgeInfo?.is_surveillance_indicator}>
          <InvestCareMessage
            displayName={displayName}
            message={STATICS.SURVEILLANCE_DESC(nudgeInfo?.surveillance_desc)}
            ctaText={STATICS.SURVEILLANCE_CTA_TEXT}
            ctaLink={STATICS.SURVEILLANCE_CTA_LINK}
            analyticalEvents={analyticalEvents}
          />
        </If>

        <If test={nudgeInfo?.is_re_scrip}>
          <InvestCareMessage
            displayName={displayName}
            message={equityInfoCard?.company_RE?.message}
            ctaLink={equityInfoCard?.company_RE?.cta_link}
            ctaText={equityInfoCard?.company_RE?.cta_text}
            analyticalEvents={analyticalEvents}
          />
        </If>

        <If test={nudgeInfo?.is_t2t_scrip}>
          <InvestCareMessage
            message={equityInfoCard?.company_T2T?.message}
            ctaLink={equityInfoCard?.company_T2T?.cta_link}
            ctaText={equityInfoCard?.company_T2T?.cta_text}
            analyticalEvents={analyticalEvents}
          />
        </If>

        {(nudgeInfo?.is_surveillance_indicator ||
          nudgeInfo?.is_ban ||
          nudgeInfo?.is_t2t_scrip) ? (
          <View style={styles.btnContainer}>
            <Button
              buttonText={CTA.YES}
              isPrimaryBlue
              buttonClassName={styles.btn}
              onClickHandler={() => {
                onAccept();
                if (analyticalEvents) analyticalEvents.investCareCta('YES');
              }}
            />
            <Button
              buttonText={CTA.NO}
              isSecondary
              buttonClassName={styles.secondaryBtn}
              buttonTextClassName={styles.secondaryBtnText}
              onClickHandler={() => {
                onReject();
                if (analyticalEvents) analyticalEvents.investCareCta('NO');
              }}
            />
          </View>
        ) : (
          <View style={styles.btnContainer}>
            <Button
              buttonText={CTA.PROCEED}
              onClickHandler={() => {
                onAccept();
                if (analyticalEvents) analyticalEvents.investCareCta('YES');
              }}
              isPrimary
            />
          </View>
        )}
      </View>
    </Drawer>
  );
};

export default InvestCareDrawer; 