import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    padding: 16,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    marginBottom: 8,
    ...typography.heading1B4, // 24px, 700 weight
    color: '#101010',
  },
  btnContainer: {
    width: '100%',
    marginTop: 33,
  },
  btn: {
    backgroundColor: '#013da6',
    marginBottom: 16,
    borderRadius: 8,
  },
  secondaryBtn: {
    borderWidth: 1,
    borderColor: '#013da6',
    borderRadius: 8,
  },
  secondaryBtnText: {
    color: '#013da6',
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  image: {
    width: 150,
    height: 150,
  },
}); 