import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Drawer from '../../molecules/Drawer';
import { styles } from './styles';
import { STATICS } from './enums';
import { formatCurrency } from '../../../utils/commonUtils';

const FundsInfoDrawer = ({
  showPopup,
  onClose,
  availableCash = 0,
  tradeBalance = 0,
  onKnowMorePress,
  message,
  ctaText,
}) => {
    // TODO: handle API calls
  return (
    <Drawer
      isOpen={showPopup}
      onClose={onClose}
      title={STATICS.HEADER}
      showHandleBar
    >
      <View style={styles.container}>
        <View style={styles.column}>
          <Text style={styles.label}>{STATICS.TOTAL_CASH}</Text>
          <Text style={styles.value}>
            ₹{formatCurrency(availableCash)}
          </Text>
        </View>

        <View style={styles.column}>
          <Text style={styles.label}>{STATICS.CASH_COLL}</Text>
          <Text style={styles.value}>
            ₹{formatCurrency(tradeBalance)}
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.message}>
            {message || STATICS.MESSAGE}
          </Text>
          <TouchableOpacity onPress={onKnowMorePress}>
            <Text style={styles.cta}>
              {ctaText || STATICS.KNOW_MORE}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Drawer>
  );
};

export default FundsInfoDrawer;
