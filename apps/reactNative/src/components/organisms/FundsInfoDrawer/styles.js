import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  column: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  label: {
    ...typography.text2, // 14px, 500 weight
    color: '#101010B2', // Black5
  },
  value: {
    ...typography.text, // 14px, 500 weight
    color: '#101010', // Black5
  },
  footer: {
    marginTop: 15,
  },
  message: {
    ...typography.body2B3, // 12px, 400 weight
    color: '#101010', // Black5
  },
  cta: {
    ...typography.body2B3, // 12px, 400 weight
    color: '#013DA6', // ETFBlue
    marginTop: 2
  },
});
