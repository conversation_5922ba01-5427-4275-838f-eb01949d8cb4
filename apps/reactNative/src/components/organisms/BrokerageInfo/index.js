import { StyleSheet, Text, View } from 'react-native';
import ModalLoader from '../../atoms/ModalLoader/ModalLoader';
import { FormatNumber } from '../../../utils/commonUtils';
import Drawer from '../../Molecules/Drawer/Drawer';
import Disclaimer from './_partials/Disclaimer';
import OrderCharges from './_partials/OrderCharges';

const BrokerageInfo = ({ data, isLoading, isOpen, onClose }) => {
  if (isLoading) return <ModalLoader />;

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>Order Charges</Text>
        </View>
      }
    >
      <View style={styles.container}>
        {data.sections.map((section) => (
          <OrderCharges key={section.title} data={section} />
        ))}

        <View style={styles.totalContainer}>
          <Text style={styles.totalHeader}>Total Charges</Text>
          <FormatNumber input={data.total} />
        </View>

        {data?.auto_sq_off ? <Disclaimer autoSqOff={data?.auto_sq_off} /> : null}
      </View>
    </Drawer>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 11,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    color: '#101010',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 16
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderColor: '#E0E0E0'
  },
  totalHeader: {
    fontSize: 16,
    fontWeight: '600'
  }
});

export default BrokerageInfo;