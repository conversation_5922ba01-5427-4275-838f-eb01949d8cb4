import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { formatPrice } from '../../../../../utils/commonUtils';
import { PRODUCT_TYPES, STATICS, TRANSACTION_TYPES } from '../../statics';
import styles from './styles';

const Disclaimer = ({
  auto_sq_off,
  isSikkimUser,
  postData,
  isSl,
  isLimitOrder,
  ...rest
}) => {
  const { product_type, transaction_type } = postData;
  const isTypeSell = transaction_type === TRANSACTION_TYPES.SELL;
  const isIntraday = product_type === PRODUCT_TYPES.INTRADAY;
  const isMKTOrder = !isLimitOrder;

  const getDescription = () => {
    const convertOrder = rest?.convertOrder;
    const productFrom = rest?.productFrom;

    if (convertOrder) {
      if (productFrom === PRODUCT_TYPES.DELIVERY) {
        return STATICS.AUTO_SQ_OFF(formatPrice(auto_sq_off));
      }
      return null;
    }

    // ... rest of the getDescription logic remains the same as original
    // (only JSX parts need conversion, logic can stay identical)
  };

  const description = getDescription();
  const sikkimText = isSikkimUser ? STATICS.SIKKIM_USER : null;

  return (
    <View style={styles.container}>
      {sikkimText && (
        <Text style={styles.disclaimerText}>
          {sikkimText}
        </Text>
      )}
      {description && (
        <Text style={styles.disclaimerText}>
          {description.split('\n').map((line, i) => (
            <Text key={i}>
              {line}
              {'\n'}
            </Text>
          ))}
        </Text>
      )}
    </View>
  );
};



export default Disclaimer;