import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { FormatNumber } from '../../../../../utils/commonUtils';
import { useState } from 'react';
import Summary from '../Summary';
import ArrowUp from '../../../../../assets/arrowUp.svg'
import ArrowDown from '../../../../../assets/arrowDown.svg'
import styles from './styles';


const OrderCharges = ({ data }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { items, title, total } = data;

  if (!items) {
    return (
      <View style={[styles.container, styles.border]}>
        <Text style={styles.header}>{title}</Text>
        <FormatNumber input={total} />
      </View>
    );
  }

  return (
    <View style={styles.border}>
      <View style={[styles.container, styles.accContainer]}>
        <TouchableOpacity
          style={styles.accHeader}
          onPress={() => setIsOpen(!isOpen)}
        >
          <Text style={styles.header}>{title}</Text>
          {isOpen ?
            <ArrowUp width={10} height={10} /> :
            <ArrowDown width={10} height={10} />}
        </TouchableOpacity>
        <FormatNumber input={total} />
      </View>

      {isOpen && items?.length ? <Summary items={items} /> : null}
      {!isOpen && <Text style={styles.breakUp}>View breakup</Text>}
    </View>
  );
};


export default OrderCharges;