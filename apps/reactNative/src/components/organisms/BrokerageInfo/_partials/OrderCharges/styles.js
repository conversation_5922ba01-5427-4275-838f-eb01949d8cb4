import { StyleSheet } from 'react-native';
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  border: {
    borderBottomWidth: 1,
    borderColor: '#E7F1F8',
    paddingVertical: 10
  },
  header: {
    marginRight: 5,
    fontSize: 16, // Adjust based on your typography scale
    fontWeight: 'bold',
    color: '#101010' // Replace DBlue2 with actual color
  },
  amount: {
    margin: 0,
    fontSize: 14, // Adjust based on your typography scale
    color: '#1A237E' // Replace DBlue2 with actual color
  },
  accHeader: {
    flexDirection: 'row',
    alignItems: 'center',

  },
  opened: {
    transform: [{ rotate: '270deg' }]
  },
  closed: {
    transform: [{ rotate: '90deg' }],
    paddingHorizontal: 16
  },
  accContainer: {
    paddingVertical: 10
  },
  breakUp: {
    marginBottom: 10,
    fontSize: 12,
    color: '#616161',
    paddingHorizontal: 16
  }
});

export default styles;