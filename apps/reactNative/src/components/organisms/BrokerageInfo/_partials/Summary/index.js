import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { formatPrice } from '../../../../../utils/commonUtils';
import styles from './styles';


function Summary({ items }) {
  return (
    <View style={styles.container}>
      {items.map(data => {
        const { header, formula, value } = data;
        if (value === null) return null;

        return (
          <View key={header} style={styles.summaryContainer}>
            <View style={styles.headerContainer}>
              <Text style={styles.header}>{header}</Text>
              <Text style={styles.formula}>({formula})</Text>
            </View>
            <Text style={styles.value}>₹{formatPrice(value)}</Text>
          </View>
        );
      })}
    </View>
  );
}



export default Summary;