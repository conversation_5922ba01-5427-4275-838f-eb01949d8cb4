import React from 'react';
import { View } from 'react-native';
import { useTechnical } from "@paytm-money/store"
import Scores from '../../molecules/Scores/Scores';
import GroupComparison from "../../molecules/GroupComparison/GroupComparison";
import MovingAverages from '../../molecules/MovingAverages';
import PivotPoints from '../../molecules/PivotPoints/PivotPoints';
import { styles } from './styles';

const CompanyTechnicals = ({ pmlId ,companyName}) => {
    const {
        scores,
        comparison,
        overAllScore,
        movingAverages,
        isScoreLoading,
        isScoreError,
        refetchScore,
        isMovingAverageError,
        refetchMovingAverage,
        isMovingAverageLoading
    } = useTechnical(pmlId)

    return (
        <View style={styles.container}>
            <Scores
            companyName={companyName}
                scores={scores}
                isLoading={isScoreLoading}
                isError={isScoreError}
                overAllScore={overAllScore}
                refetch={refetchScore} 
            />
            <GroupComparison
                comparisonData={comparison}
                isLoading={isScoreLoading}
                isError={isScoreError}
                refetch={refetchScore}
            />
            <PivotPoints pmlId={pmlId} />
            <MovingAverages
                data={movingAverages}
                isError={isMovingAverageError}
                isLoading={isMovingAverageLoading}
                refetch={refetchMovingAverage} 
            />
        </View>
    );
};

export default CompanyTechnicals;