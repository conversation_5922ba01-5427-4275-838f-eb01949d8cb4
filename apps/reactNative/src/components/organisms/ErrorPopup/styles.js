import { StyleSheet, Dimensions } from 'react-native';
import { typography } from '../../../theme/typography';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    width: '100%',
    paddingHorizontal: 16,
    paddingBottom: 10,
  },
  header: {
    ...typography.text, // 14px, 600 weight, 1.43 lineHeight
    color: '#101010B2', // ETFRed from colors
    textAlign: "center",
    paddingBottom:35
  },
  image: {
    width: SCREEN_WIDTH,
    height: 150,
  },
  message: {
    marginBottom: 24,
    ...typography.body1R, // 14px, regular weight, 1.43 lineHeight
    color: '#101010', // Black5 from colors
  },
});
