import React from 'react';
import { View, Text,Image} from 'react-native';
import Drawer from '../../molecules/Drawer';
import OrderPadButton from '../../molecules/OrderPadButton';
import { styles } from './styles';

const ErrorPopup = ({ showPopup, onClose, cta }) => {

  return (
    <Drawer
      isOpen={showPopup}
      onClose={onClose}
      showCross
    >
      <View>
        <Image source={require("../../../assets/errorState.png")} style={styles.image} />
        <View style={styles.container}>
        <Text style={styles.header}>We are facing some technical error. Please try again</Text>
        <OrderPadButton 
          buttonText={cta} 
          isPrimary
          onClickHandler={onClose}
        />
        </View>
      </View>
    </Drawer>
  );
};

export default ErrorPopup;
