
import { TouchableOpacity, StyleSheet, View, Text } from 'react-native';
import Drawer from '../../Molecules/Drawer/Drawer';
import { useState } from 'react';
import styles from './styles';

const RadioButtonList = ({ isOpen, onClose, options = [{
  value: 0,
  label: 'Buy Order'
}, {
  value: 1,
  label: 'Sell Order'
}]
  // , selectedValue, onSelect
}) => {

  const [selectedValue, setSelectedValue] = useState();

  const onSelect = (value) => setSelectedValue(value);

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>More Options</Text>
        </View>
      }
    >
      <View style={styles.listContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={styles.optionContainer}
            onPress={() => onSelect(option.value)}
          >
            <View style={styles.radioCircle}>
              {selectedValue === option.value && <View style={styles.selectedRb} />}
            </View>
            <Text style={styles.optionText}>{option.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Drawer>
  );
};



export default RadioButtonList;