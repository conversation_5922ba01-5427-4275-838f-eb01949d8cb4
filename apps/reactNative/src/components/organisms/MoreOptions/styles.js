import { StyleSheet } from 'react-native';
// Add new styles to existing StyleSheet
const styles = StyleSheet.create({
  listContainer: {
    padding: 16,
  },
  headerText: {
    fontSize: 16,
    fontStyle: 'normal',
    fontWeight: 600,
    lineHeight: 22,
    letterSpacing: 0.01
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
  },
  radioCircle: {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'rgba(1, 61, 166, 1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  selectedRb: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(1, 61, 166, 1)',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
});

export default styles;