import { StyleSheet, Dimensions } from 'react-native';
import { typography } from '../../../theme/typography';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  content: {
    padding: 16
  },
  header: {
    ...typography.heading3B1, // 16px, 700 weight
    color: '#101010', // DBlue2
  },
  description: {
    ...typography.text2, // 12px
    color: '#101010B2', // Grey14
    marginVertical: 10,
  },
  ctaContainer: {
    width: '100%',
    paddingBottom: 16,
    paddingHorizontal:16,
  },
});

export { SCREEN_WIDTH }; 