import React from 'react';
import { View, Text } from 'react-native';
import Drawer from '../../molecules/Drawer';
import OrderPadButton from '../../molecules/OrderPadButton';
import DeviceLocationImage from '../../../assets/deviceLocation.svg';
import { styles, SCREEN_WIDTH } from './styles';
import { CONSTANTS, CTA, SCREEN_TYPE } from './enums';

const DeviceLocationDrawer = ({
    showPopup,
    onClose,
    enableLocation,
    goToDashboard,
    geoLocationPermissionStatus,
}) => {
    return (
        <Drawer
            isOpen={showPopup}
            onClose={onClose}
            showHandleBar={false}
            showCross={false}
        >
            <View >
                <DeviceLocationImage
                    width={SCREEN_WIDTH}
                    height={170}
                    style={styles.image}
                />
                <View style={styles.content}>
                    <Text style={styles.header}>{CONSTANTS.HEADER}</Text>
                    <Text style={styles.description}>{CONSTANTS.DESCRIPTION}</Text>
                </View>
            </View>
            <View style={styles.ctaContainer}>
                <OrderPadButton
                    isPrimary
                    buttonText={CTA.ENABLE_LOCATION}
                    onClickHandler={enableLocation}
                    isLoading={geoLocationPermissionStatus === SCREEN_TYPE.IN_PROGRESS}
                />
            </View>
        </Drawer>
    );
};

export default DeviceLocationDrawer;
