import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import PropTypes from 'prop-types';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const ModalLoader = ({ showHeader = false }) => {
  // Create animated values for each dot
  const animatedValues = useRef([
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
  ]).current;

  useEffect(() => {
    const animations = animatedValues.map((value, index) => {
      return Animated.sequence([
        // Wait for previous dots
        Animated.delay(index * 120),
        // Animation loop
        Animated.loop(
          Animated.sequence([
            // Scale down
            Animated.timing(value, {
              toValue: 0.35,
              duration: 300,
              useNativeDriver: true,
            }),
            // Scale up
            Animated.timing(value, {
              toValue: 1,
              duration: 450,
              useNativeDriver: true,
            }),
          ])
        ),
      ]);
    });

    // Start all animations
    Animated.parallel(animations).start();

    // Cleanup
    return () => {
      animations.forEach(anim => anim.stop());
    };
  }, []);

  return (
    <View style={[styles.loaderBox, showHeader && styles.headerTabsVisible]}>
      <View style={[styles.loaderInner, showHeader && styles.loaderInnerHeaderVisible]}>
        {animatedValues.map((value, index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor: index < 3 ? '#012b72' : '#00b9f5',
                transform: [{ scale: value }],
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  loaderBox: {
    width: SCREEN_WIDTH,
    height: 200,
    backgroundColor: 'transparent',
    zIndex: 21,
    marginTop: 51,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTabsVisible: {
    marginTop: 120,
  },
  loaderInner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderInnerHeaderVisible: {
    marginTop: -120,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    margin: 2,
  },
});

ModalLoader.propTypes = {
  showHeader: PropTypes.bool,
};

ModalLoader.defaultProps = {
  showHeader: false,
};

export default ModalLoader;
