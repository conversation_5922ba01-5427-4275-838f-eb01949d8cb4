import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 24,
    backgroundColor: '#FFFFFF',
  },
  button: {
    alignItems: 'center',
    flex: 1,
  },
  buttonText: {
    ...typography.body2R,
    color: '#101010',
    textAlign: 'center',
    marginTop: 8,
  },
  separator: {
    width: 1,
    height: 40,
    backgroundColor: '#E7F1F8',
    marginHorizontal: 8,
  },
}); 