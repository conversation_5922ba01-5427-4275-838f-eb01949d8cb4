import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { styles } from './styles';
import GttIcon from '../../../assets/gtt.svg';
import SipIcon from '../../../assets/equitySip.svg';
import PriceAlertIcon from '../../../assets/priceAlerts.svg';
import BasketIcon from '../../../assets/basket.svg';

const ActionButtons = ({ onAlertPress }) => {
  const actions = [
    {
      id: 'gtt',
      title: 'Create\nGTT',
      Icon: GttIcon,
      onPress: () => console.log('GTT pressed')
    },
    {
      id: 'sip',
      title: 'Start\nSIP',
      Icon: SipIcon,
      onPress: () => console.log('SIP pressed')
    },
    {
      id: 'alert',
      title: 'Add Price\nAlert',
      Icon: PriceAlertIcon,
      onPress: onAlertPress || (() => console.log('Alert pressed'))
    },
    {
      id: 'basket',
      title: 'Add to\nBasket',
      Icon: BasketIcon,
      onPress: () => console.log('Basket pressed')
    }
  ];

  return (
    <View style={styles.container}>
      {actions.map(({ id, title, Icon, onPress }, index) => (
        <React.Fragment key={id}>
          <TouchableOpacity 
            style={styles.button}
            onPress={onPress}
          >
            <Icon width={32} height={32} />
            <Text style={styles.buttonText}>{title}</Text>
          </TouchableOpacity>
          {index < actions.length - 1 && <View style={styles.separator} />}
        </React.Fragment>
      ))}
    </View>
  );
};

export default ActionButtons; 