import { Text, View } from "react-native";
import { getAbsoluteValue, isValidNumber, roundValue } from "../../../utils/commonUtils";
import styles from "./styless";


function formatPrice(value, decimals = 2, format = true, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return `0${decimals ? `.${'0'.repeat(decimals)}` : ''}`;
  }
  if (format) {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: decimals,
    }).format(getAbsoluteValue(value, decimals, getAbsolute));
  }
  return getAbsoluteValue(value, decimals, getAbsolute);
}

export default function Change({
  value,
  withSign,
  bothSign,
  signClassName,
  withRupee,
  className,
  neutral,
  withFraction = true,
  withArrow = false,
}) {
  return (
    <View style={[priceClassName(value, className, neutral), styles.container]}>
      {((withSign && !(withRupee && roundValue(value) > 0)) || bothSign) && (
        <Text style={signClassName}>
          <Sign value={value} />
        </Text>
      )}
      {withArrow && (
        <Icon
          name={getIcon(value)}
          style={styles.withArrow}
          size={14} // Adjusted for RN (typically uses numbers instead of rem/em)
        />
      )}
      {withRupee && <Text>₹</Text>}
      <Text>
        {formatPrice(value).toString().split('.')[0]}
      </Text>
      {withFraction && (
        <Text>
          .<Text>{formatPrice(value).toString().split('.')[1]}</Text>
        </Text>
      )}
    </View>
  );
}


// Update priceClassName utility
function priceClassName(value, baseStyle = styles.base, neutral = false) {
  const dynamicStyles = [];
  if (!neutral) {
    if (roundValue(value) > 0) dynamicStyles.push(styles.positive);
    if (roundValue(value) < 0) dynamicStyles.push(styles.negative);
  }
  return [baseStyle, ...dynamicStyles];
}