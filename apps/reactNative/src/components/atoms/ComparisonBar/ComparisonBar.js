import React from 'react';
import { View, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { styles } from './styles';

const ComparisonBar = ({ label, value, maxValue = 10, isPercentage }) => {
  const progressWidth = `${(value / maxValue) * 100}%`;

  return (
    <View style={styles.barContainer}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>{label}</Text>
        <View style={styles.progressBarBackground}>
          <LinearGradient
            colors={['#00B8F5', '#D3D4FF']}
            style={[styles.progressBar, { width: progressWidth }]}
            end={{ x: 0, y: 0 }}
            start={{ x: 1, y: 0 }}
          />
        </View>
      </View>
      <Text style={styles.value}>
        {isPercentage ? `${value}%` : `${value}/${maxValue}`}
      </Text>
    </View>
  );
};

export default ComparisonBar; 