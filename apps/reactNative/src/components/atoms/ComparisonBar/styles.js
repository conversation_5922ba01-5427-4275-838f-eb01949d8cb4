import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  barContainer: {
    gap: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  labelContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  label: {
    ...typography.body2,
    color: '#101010B2',
    paddingBottom: 3
  },
  value: {
    ...typography.text,
    color: '#101010',
    width: 55,
    textAlign: 'right'
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#D8E7F7',
    borderRadius: 100,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 100,
  },
}); 