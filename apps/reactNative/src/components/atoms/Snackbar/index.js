import React, { useEffect, useState, memo } from 'react';
import { View, Text, Animated } from 'react-native';
import { styles } from './styles';
import { triggerCallback } from './utils';

const Snackbar = ({
  text = '',
  theme = 'positive',
  autoHide = false,
  autoHideAfter = 5000,
  reserveSpaceForStatusBar = true,
  TrailingIcon,
  LeadingIcon,
  customStyle = {},
  onHide,
  noClamp = false,
  position = 'top',
  reserveSpaceForBottomBar = false,
}) => {
  const [show, setShow] = useState(true);
  const translateY = new Animated.Value(position === 'top' ? -100 : 100);
  const opacity = new Animated.Value(0);

  const showAnimation = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideAnimation = (callback) => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start(callback);
  };

  useEffect(() => {
    if (show) {
      showAnimation();
    }
  }, [show]);

  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        hideAnimation(() => {
          setShow(false);
          triggerCallback(onHide);
        });
      }, autoHideAfter);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideAfter, onHide]);

  useEffect(() => {
    const nextShow = !!text;
    setShow(nextShow);
    if (!nextShow) {
      triggerCallback(onHide);
    }
  }, [text, onHide]);

  if (!show) return null;

  const containerStyle = [
    styles.container,
    styles[theme],
    position === 'bottom' && styles.bottomPosition,
    reserveSpaceForStatusBar && styles.extraPaddingTop,
    reserveSpaceForBottomBar && styles.extraMarginBottom,
    customStyle,
    {
      transform: [{ translateY }],
      opacity,
    },
  ];

  return (
    <Animated.View style={containerStyle}>
      {LeadingIcon && (
        <View style={styles.icon}>
          <LeadingIcon />
        </View>
      )}
      <Text 
        style={styles.textWrapper}
        // numberOfLines={noClamp ? undefined : 2}
      >
        {text}
      </Text>
      {!LeadingIcon && TrailingIcon && (
        <View style={styles.icon}>
          <TrailingIcon />
        </View>
      )}
    </Animated.View>
  );
};

export default memo(Snackbar);
