import { StyleSheet, Platform } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 36,
    zIndex: 99999,
    elevation: 99999,
    gap: 4,
  },
  bottomPosition: {
    bottom: 20,
    width: '100%',
  },
  textWrapper: {
    ...typography.text, // 14px, 500 weight
    color: '#FFFFFF',
    textAlign: 'center',
    paddingVertical: 2,
  },
  icon: {
    width: 24,
    height: 24,
  },
  extraPaddingTop: {
    paddingTop: Platform.OS === 'ios' ? 50 : 44,
  },
  extraMarginBottom: {
    marginBottom: 62,
  },
  // Theme styles
  positive: {
    backgroundColor: '#21C179', // background-positive-strong
  },
  negative: {
    backgroundColor: '#FD5154', // background-negative-strong
  },
  notice: {
    backgroundColor: '#FF9E00', // background-notice-strong
  },
  neutral: {
    backgroundColor: '#101010', // background-neutral-strong
  },
});
