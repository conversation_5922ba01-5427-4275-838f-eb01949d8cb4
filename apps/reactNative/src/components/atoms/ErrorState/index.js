import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import ErrorImage from '../../../assets/Error.svg';
import RetryErrorImage from '../../../assets/RetryError.svg';
import RefreshIcon from '../../../assets/Refresh.svg';
import { styles } from './styles';
import { ERROR_STATE_CONSTANTS } from './constants';

const ErrorState = ({ 
  title = "",
  description = "",
  isRetry = false,
  onRetry,
  style,
  ErrorIcon = ErrorImage,
  RetryIcon = RetryErrorImage,
  iconProps = ERROR_STATE_CONSTANTS.ICON.DEFAULT_DIMENSIONS
}) => {
  const Icon = isRetry ? RetryIcon : ErrorIcon;

  return (
    <View style={[styles.container, style]}>
      <Icon {...iconProps} />
      {title ? <Text style={styles.title}>{title}</Text> : null}
      {description ?<Text style={styles.description}>{description}</Text> :null}
      {isRetry && (
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={onRetry}
        >
          <RefreshIcon {...ERROR_STATE_CONSTANTS.ICON.REFRESH} />
          <Text style={styles.retryText}>{ERROR_STATE_CONSTANTS.BUTTON.TEXT}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ErrorState; 