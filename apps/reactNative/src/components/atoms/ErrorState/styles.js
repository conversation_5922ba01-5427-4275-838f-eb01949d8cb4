import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 8,
  },
  title: {
    ...typography.heading4B1,
    color: '#101010',
    marginTop: 16,
  },
  description: {
    ...typography.body2R,
    color: '#1010108A',
    paddingHorizontal:16,
    textAlign: 'center',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginTop: 8,
  },
  retryText: {
    ...typography.button2,
    color: '#1576DB',
  }
}); 