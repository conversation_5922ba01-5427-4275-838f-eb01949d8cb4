import React from 'react';
import { View, Text } from 'react-native';
import PropTypes from 'prop-types';
import { styles } from './styles';

const PercentageRatio = ({
  percentageSecond,
  firstSubLabel,
  secondSubLabel,
  firstLabelStyle,
  secondLabelStyle,
  primaryBackgroundColor = '#21C179',
  secondaryBackgroundColor = '#FD5154',
  description,
  containerStyle,
  labelContainerStyle,
  progressBarStyle,
  descriptionStyle,
}) => {
  const primaryPercentage = 100 - percentageSecond;

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.percentageBar, progressBarStyle]}>
        {primaryPercentage > 0 && (
          <View
            style={[
              styles.primaryValue,
              primaryPercentage === 100 && styles.onlyOneValue,
              {
                width: `${primaryPercentage}%`,
                backgroundColor: primaryBackgroundColor,
              },
            ]}
          />
        )}
        {percentageSecond > 0 && (
          <View
            style={[
              styles.secondaryValue,
              percentageSecond === 100 && styles.onlyOneValue,
              {
                width: `${percentageSecond}%`,
                backgroundColor: secondaryBackgroundColor,
              },
            ]}
          />
        )}
      </View>
      <View style={styles.labels}>
      <Text style={[styles.subLabel, firstLabelStyle]}>{firstSubLabel}</Text>
          <Text style={[styles.subLabel, secondLabelStyle]}>{secondSubLabel}</Text>
      </View>

      {description && (
        <Text style={[styles.description, descriptionStyle]}>
          {description}
        </Text>
      )}
    </View>
  );
};

export default PercentageRatio; 