import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  labels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    ...typography.body2R,
    color: '#101010',
  },
  subLabel: {
    ...typography.body2R,
    marginLeft: 4,
  },
  percentageBar: {
    height: 4,
    flexDirection: 'row',
    borderRadius: 2,
    overflow: 'hidden',
    backgroundColor: '#E7F1F8',
  },
  primaryValue: {
    height: '100%',
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
  },
  secondaryValue: {
    height: '100%',
    borderTopRightRadius: 2,
    borderBottomRightRadius: 2,
  },
  onlyOneValue: {
    borderRadius: 2,
  },
  description: {
    ...typography.body3R,
    color: '#101010B2',
    marginTop: 8,
    textAlign: 'center',
  },
}); 