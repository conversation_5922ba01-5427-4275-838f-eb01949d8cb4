import React from 'react';
import { View, Text } from 'react-native';
import { styles } from './styles';

const MarketDepth = ({
  depthResponse,
  priceKey,
  valKey,
  type,
  isCompanyDetailLoading
}) => {

  const DEPTH_TEXT = {
    BUY: 'Bid Price',
    SELL: 'Ask Price',
  };

  const getDepthRow = (rowData, index) => (
    <View key={index} style={styles.bids}>
      <View style={styles.col1}>
        {isCompanyDetailLoading ? (
          <View style={styles.shimmer} />
        ) : (
          <Text style={styles.bidPrice}>
            {rowData ? rowData[priceKey] : '0'}
          </Text>
        )}
      </View>
      <View style={styles.col2}>
        <View style={[
          styles.bidValue,
          type === 'SELL' ? styles.sellVal : styles.buyVal
        ]}>
          {isCompanyDetailLoading ? (
            <View style={styles.shimmer} />
          ) : (
            <Text style={[
              styles.bidValueText,
              type === 'SELL' ? styles.sellValText : styles.buyValText
            ]}>
              {rowData ? rowData[valKey] : '0'}
            </Text>
          )}
        </View>
      </View>
    </View>
  );

  return (
    <View style={
      styles.bidList
    }>
      <View style={styles.listHeader}>
        {isCompanyDetailLoading ? (
          <View style={styles.shimmerContainer}>
            <View style={[styles.shimmer, styles.shimmerLarge]} />
            <View style={[styles.shimmer, styles.shimmerSmall]} />
          </View>
        ) : (
          <>
            <Text style={[styles.listHeaderTitle, styles.col1]}>
              {DEPTH_TEXT[type]}
            </Text>
            <Text style={[styles.listHeaderTitle, styles.col2]}>
              Qty
            </Text>
          </>
        )}
      </View>
      {(depthResponse?.depth || Array(5).fill(null)).map((rowData, index) =>
        getDepthRow(rowData, index)
      )}
    </View>
  );
};

export default MarketDepth;
