import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  bidList: {
    paddingVertical: 12,
  },
  askPrice: {
  },
  listHeader: {
    flexDirection: 'row',
    marginBottom: 10
  },
  listHeaderTitle: {
    ...typography.body2R,
    color: '#101010'
  },
  col1: {
    flex: 1
  },
  col2: {
    alignItems: 'flex-end'
  },
  bids: {
    flexDirection: 'row',
    marginBottom: 10
  },
  bidPrice: {
    ...typography.body2R,
    color: '#101010B2'
  },
  bidValue: {
    alignItems: 'flex-end'
  },
  bidValueText: {
    ...typography.body2R,
    color: '#101010'
  },
  buyVal: {
    backgroundColor: '#E3F6EC',
    borderRadius: 4,
    paddingHorizontal: 2,
    paddingVertical: 1
  },
  buyValText: {
    color: '#21C179'
  },
  sellVal: {
    backgroundColor: '#FFEBEF',
    borderRadius: 4,
    paddingHorizontal: 2,
    paddingVertical: 1
  },
  sellValText: {
    color: '#FD5154'
  },
  shimmerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%'
  },
  shimmer: {
    backgroundColor: '#EEEEEE26',
    borderRadius: 4,
    height: 10
  },
  shimmerLarge: {
    width: 60
  },
  shimmerSmall: {
    width: 30
  },
}); 