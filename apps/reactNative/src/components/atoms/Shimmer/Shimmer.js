import React, { useEffect } from 'react';
import { View, Animated, StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

const Shimmer = ({
    type = 'line',
    height = '20px',
    width: customWidth = '100%',
    cardArray = [
        {
            height: '20px',
            row: ['20%', '30%'],
            margin: '0 0 10px 0',
        },
    ],
    margin = 0,
    style = {},
}) => {
    const animatedValue = React.useRef(new Animated.Value(0)).current;

    useEffect(() => {
        const shimmerAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(animatedValue, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: true,
                }),
                Animated.timing(animatedValue, {
                    toValue: 0,
                    duration: 0,
                    useNativeDriver: true,
                }),
            ])
        );

        shimmerAnimation.start();

        return () => {
            shimmerAnimation.stop();
        };
    }, []);

    const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [-width, width],
    });

    const getShimmerComponent = (height, width, margin, borderRadius = 0) => (
        <View
            style={[
                styles.shimmerContainer,
                { height, width, margin, borderRadius },
                style,
            ]}
        >
            <Animated.View
                style={[
                    styles.shimmer,
                    {
                        transform: [{ translateX }],
                        backgroundColor: 'rgba(226, 226, 226, 0.3)',
                    },
                ]}
            />
        </View>
    );

    switch (type) {
        case 'line':
            return getShimmerComponent(
                parseFloat(height),
                parseFloat(customWidth),
                margin
            );

        case 'card':
            return (
                <>
                    {cardArray.map((shimmerItem, index) => (
                        <View
                            key={`shimmer-${index}`}
                            style={[
                                styles.row,
                                {
                                    margin: shimmerItem.margin || 0,
                                    justifyContent: shimmerItem.justifyContent || 'flex-start',
                                },
                                style,
                            ]}
                        >
                            {shimmerItem.row.map((shimmerWidth, itemIndex) => (
                                <View
                                    key={`shimmerItem-${itemIndex}`}
                                    style={[
                                        styles.rowItem,
                                        itemIndex !== shimmerItem.row.length - 1 && styles.marginRight,
                                    ]}
                                >
                                    {getShimmerComponent(
                                        parseFloat(shimmerItem.height) || 20,
                                        parseFloat(shimmerWidth) || '20%'
                                    )}
                                </View>
                            ))}
                        </View>
                    ))}
                </>
            );

        case 'circle':
            return getShimmerComponent(
                parseFloat(customWidth),
                parseFloat(customWidth),
                margin,
                parseFloat(customWidth) / 2
            );

        default:
            return null;
    }
};

const styles = StyleSheet.create({
    shimmerContainer: {
        overflow: 'hidden',
        backgroundColor: 'rgba(239, 241, 243, 0.3)',
    },
    shimmer: {
        width: '100%',
        height: '100%',
        position: 'absolute',
        opacity: 0.5,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rowItem: {
        flex: 1,
    },
    marginRight: {
        marginRight: 10,
    },
});

export default Shimmer;
