import React from 'react';
import { View ,TouchableOpacity } from 'react-native';
import { styles } from './styles';
import LeftArrow from '../../../assets/leftArrow.svg';
import OptionChain from '../../../assets/optionChain.svg';
import Watchlist from '../../../assets/watchlist.svg';
import ThreeDots from '../../../assets/threeDots.svg';

const Header = ({ 
  onBackPress, 
  onOptionChainPress, 
  onWatchlistPress, 
  onMorePress 
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <TouchableOpacity 
          onPress={onBackPress}
          style={styles.iconButton}
        >
          <LeftArrow width={24} height={24} />
        </TouchableOpacity>
      </View>

      <View style={styles.rightSection}>
        <TouchableOpacity 
          onPress={onOptionChainPress}
          style={styles.iconButton}
        >
          <OptionChain width={24} height={24} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          onPress={onWatchlistPress}
          style={styles.iconButton}
        >
          <Watchlist width={24} height={24} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          onPress={onMorePress}
          style={styles.iconButton}
        >
          <ThreeDots width={24} height={24} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Header; 