import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  snapshotContainer: {
    paddingHorizontal:16,
    paddingBottom:16
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  headerText: {
    
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 20,
    color: '#101010',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    backgroundColor: '#ECF2F8',
    padding: 16,
    borderRadius: 12,
    gap: 32,
  },
  leftAlign: {
    alignItems: 'flex-start',
  },
  rightAlign: {
    alignItems: 'flex-end',
  },
  statItem: {
    flex: 1,
    gap: 4,
  },
  statLabel: {
    ...typography.caption2R,
    color: '#1010108A',
  },
  statValue: {
    ...typography.caption2M,
    textAlign: 'right',
  },
  rangeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  rangeItem: {
    flex: 1,
    gap: 4,
  },
  rangeCenterLabel: {
    ...typography.caption2R,
    position: 'absolute',
    left: '50%',
    transform: [{ translateX: -25 }],
    color: '#1010108A',
  },
  rangeLabel: {
    ...typography.caption2R,
    color: '#1010108A',
  },
  rangeValue: {
    ...typography.caption1M,
    textAlign: 'right',
  },
  slider: {
    position: 'relative',
    height: 4,
    backgroundColor: '#D8E7F7',
    borderRadius: 2,
    marginBottom: 12,
  },
  sliderTrack: {
    flex: 1,
    height: 4,
    backgroundColor: '#D8E7F7',
    borderRadius: 2,
  },
  sliderThumb: {
    position: 'absolute',
    top: -4,
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderBottomWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#1576DB',
  },
  circuitRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 32,
  },
  circuitItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  circuitLabel: {
    ...typography.caption2R,
    color: '#1010108A',
  },
  circuitValue: {
    ...typography.caption1M,
  },
}); 