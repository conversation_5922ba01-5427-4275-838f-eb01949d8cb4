import React from 'react';
import { View, Text } from 'react-native';
import { styles } from './styles';

const QuickSnapshot = ({ data, maxValue, minValue, currentValue }) => {
  return (
    <View style={styles.snapshotContainer}>
      <View style={styles.statsRow}>
        <View style={[styles.statItem, styles.leftAlign]}>
          <Text style={styles.statLabel}>Open</Text>
          <Text style={styles.statValue}>{data.open.toLocaleString()}</Text>
        </View>
        <View style={[styles.statItem, styles.rightAlign]}>
          <Text style={styles.statLabel}>High</Text>
          <Text style={styles.statValue}>{data.high.toLocaleString()}</Text>
        </View>
        <View style={[styles.statItem, styles.leftAlign]}>
          <Text style={styles.statLabel}>Low</Text>
          <Text style={styles.statValue}>{data.low.toLocaleString()}</Text>
        </View>
        <View style={[styles.statItem, styles.rightAlign]}>
          <Text style={styles.statLabel}>Close</Text>
          <Text style={styles.statValue}>{data.close.toLocaleString()}</Text>
        </View>
      </View>

      <View style={styles.rangeRow}>
        <View style={[styles.rangeItem, styles.leftAlign]}>
          <Text style={styles.rangeLabel}>Low</Text>
          <Text style={styles.rangeValue}>{data.yearLow.toLocaleString()}</Text>
        </View>
        <Text style={styles.rangeCenterLabel}>52 Week</Text>
        <View style={[styles.rangeItem, styles.rightAlign]}>
          <Text style={styles.rangeLabel}>High</Text>
          <Text style={styles.rangeValue}>{data.yearHigh.toLocaleString()}</Text>
        </View>
      </View>

      <View style={styles.slider}>
        <View style={styles.sliderTrack} />
        <View 
          style={[
            styles.sliderThumb,
            {
              left: `${(100 / (maxValue - minValue)) * (currentValue - minValue)}%`,
            }
          ]} 
        />
      </View>

      <View style={styles.circuitRow}>
        <View style={styles.circuitItem}>
          <Text style={styles.circuitLabel}>Lower Circuit:</Text>
          <Text style={styles.circuitValue}>
            {data.lowerCircuit.toLocaleString()}
          </Text>
        </View>
        <View style={styles.circuitItem}>
          <Text style={styles.circuitLabel}>Upper Circuit:</Text>
          <Text style={styles.circuitValue}>
            {data.upperCircuit.toLocaleString()}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default QuickSnapshot; 