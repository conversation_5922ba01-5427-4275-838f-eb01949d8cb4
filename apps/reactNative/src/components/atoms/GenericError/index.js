import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import GenericErrorImage from '../../../assets/GenericError.svg';
import ErrorImage from '../../../assets/Error.svg';
import { styles } from './styles';

const GenericError = ({ 
  description = "We do not have a lot of technical info for this company, feel free to check out others!",
  onRetry,
  style
}) => {
  const isNoData = !onRetry;

  return (
    <View style={[styles.container, style]}>
      {isNoData ? (
        <ErrorImage width={200} height={114} />
      ) : (
        <GenericErrorImage width={279} height={162} />
      )}
      <Text style={styles.title}>
        {description}
      </Text>
      {!isNoData && (
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={onRetry}
        >
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default GenericError; 