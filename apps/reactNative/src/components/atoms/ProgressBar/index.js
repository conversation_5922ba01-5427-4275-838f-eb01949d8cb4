import React from 'react';
import { View } from 'react-native';
import Svg, { Circle, Text, G } from 'react-native-svg';

const cleanPercentage = percentage => {
  const tooLow = !Number.isFinite(+percentage) || percentage < 0;
  const tooHigh = percentage > 100;
  return tooLow ? 0 : tooHigh ? 100 : +percentage;
};

const CircleComponent = ({ colour, pct }) => {
  const r = 34;
  const circ = 2 * Math.PI * r;
  const strokePct = ((100 - pct) * circ) / 100;
  return (
    <Circle
      r={r}
      cx={42}
      cy={42}
      fill="transparent"
      stroke={strokePct !== circ ? colour : ''}
      strokeWidth="6"
      strokeDasharray={`${circ}`}
      strokeDashoffset={pct ? strokePct : 0}
      strokeLinecap="round"
    />
  );
};

const PercentageComponent = ({ percentage, colour }) => (
  <Text
    x="42"
    y="35"
    fontSize="20"
    fontWeight="700"
    fill={colour}
    textAnchor="middle"
    alignmentBaseline="central"
  >
   {`${percentage.toFixed(0)}%`}
  </Text>
);

const TextComponent = ({ middleText, colour }) => (
  <Text
    x="42"
    y="52"
    fontSize="12"
    fontWeight="500"
    fill={colour}
    textAnchor="middle"
    alignmentBaseline="central"
    textTransform="uppercase"
  >
    {middleText}
  </Text>
);

const ProgressBar = ({ percentage, colour, middleText }) => {
  const pct = cleanPercentage(percentage);
  return (
    <View>
      <Svg width={84} height={84}>
        <G rotation={-90} origin="42, 42">
          <CircleComponent colour="#1010100F" />
          <CircleComponent colour={colour} pct={pct} />
        </G>
        <PercentageComponent percentage={pct} colour={colour} />
        <TextComponent middleText={middleText} colour={colour} />
      </Svg>
    </View>
  );
};

export default ProgressBar; 