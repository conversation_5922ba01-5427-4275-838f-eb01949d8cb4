import { StyleSheet } from 'react-native';

const DOT_WIDTH = 2;
const DOT_HEIGHT = 5;
const RADIUS = 7;
const QUARTER = (RADIUS / 2) + (RADIUS / 5.5);

export const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: RADIUS + DOT_HEIGHT,
    height: RADIUS + DOT_HEIGHT,
    margin: RADIUS,
  },
  dot: {
    width: DOT_WIDTH,
    height: DOT_HEIGHT,
    position: 'absolute',
    backgroundColor: '#fff', // PureWhite
  },
  greyDot: {
    backgroundColor: '#8ba6c1', // Grey11
  },
  // Position styles for each dot
  dot1: {
    top: RADIUS,
    left: 0,
  },
  dot2: {
    top: QUARTER,
    left: QUARTER,
    transform: [{ rotate: '-45deg' }],
  },
  dot3: {
    top: 0,
    left: RADIUS,
    transform: [{ rotate: '90deg' }],
  },
  dot4: {
    top: -QUARTER,
    left: QUARTER,
    transform: [{ rotate: '45deg' }],
  },
  dot5: {
    top: -RADIUS,
    left: 0,
  },
  dot6: {
    top: -QUARTER,
    left: -QUARTER,
    transform: [{ rotate: '-45deg' }],
  },
  dot7: {
    top: 0,
    left: -RADIUS,
    transform: [{ rotate: '90deg' }],
  },
  dot8: {
    top: QUARTER,
    left: -QUARTER,
    transform: [{ rotate: '45deg' }],
  },
}); 