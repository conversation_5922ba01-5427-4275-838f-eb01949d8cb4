import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import PropTypes from 'prop-types';
import { styles } from './FadeLoader.styles';

const FadeLoader = ({ customSpinner, showGreySpinner }) => {
  // Create animated values for each dot
  const fadeValues = useRef([
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
    new Animated.Value(1),
  ]).current;

  useEffect(() => {
    // Create animation sequence for each dot
    const animations = fadeValues.map((value, index) => {
      return Animated.sequence([
        // Initial delay based on position
        Animated.delay((8 - index) * 200),
        // Animation loop
        Animated.loop(
          Animated.sequence([
            // Fade out
            Animated.timing(value, {
              toValue: 0.2,
              duration: 800,
              useNativeDriver: true,
            }),
            // Fade in
            Animated.timing(value, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
            }),
          ])
        ),
      ]);
    });

    // Start all animations
    Animated.parallel(animations).start();

    // Cleanup
    return () => {
      animations.forEach(anim => anim.stop());
    };
  }, []);

  // Create array of dot positions and styles
  const dots = [
    styles.dot1,
    styles.dot2,
    styles.dot3,
    styles.dot4,
    styles.dot5,
    styles.dot6,
    styles.dot7,
    styles.dot8,
  ];

  if (customSpinner) {
    return <View style={customSpinner} />;
  }

  return (
    <View style={styles.container}>
      {dots.map((dotStyle, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            dotStyle,
            showGreySpinner && styles.greyDot,
            {
              opacity: fadeValues[index],
            },
          ]}
        />
      ))}
    </View>
  );
};

FadeLoader.propTypes = {
  customSpinner: PropTypes.object,
  showGreySpinner: PropTypes.bool,
};

FadeLoader.defaultProps = {
  customSpinner: null,
  showGreySpinner: false,
};

export default FadeLoader;
