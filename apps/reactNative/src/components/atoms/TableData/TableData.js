import React from 'react';
import { View, Text } from 'react-native';
import ErrorState from '../ErrorState';
import { styles } from './styles';
import InfoIcon from "../../../assets/Info_icon.svg"
import { TABLE_DATA_CONSTANTS as CONSTANTS } from './constants';

const LoadingShimmer = () => {
  return (
    <>
      {[...Array(CONSTANTS.LOADING.ROW_COUNT)].map((_, i) => (
        <View key={i} style={styles.rowShimmer}>
          <View style={styles.labelShimmer} />
          <View style={styles.valueShimmer} />
        </View>
      ))}
    </>
  );
};

const AverageItem = ({ label, value }) => (
  <View style={styles.averageItem}>
    <Text style={styles.label}>{label}</Text>
    <Text style={styles.value}>{value}</Text>
  </View>
);

const TableData = ({ 
  data, 
  title, 
  showInfoIcon = false, 
  isLoading = false,
  isError = false,
  onRetry,
  isEmpty = false,
  errorTitle = CONSTANTS.ERROR.TITLE,
  errorDescription = CONSTANTS.ERROR.DESCRIPTION,
  emptyTitle = CONSTANTS.EMPTY.TITLE,
  emptyDescription = CONSTANTS.EMPTY.DESCRIPTION
}) => {
  const renderContent = () => {
    if (isLoading) return <LoadingShimmer />;
    if (isError) return (
      <ErrorState 
        title={errorTitle}
        description={errorDescription}
        isRetry
        onRetry={onRetry}
      />
    );
    if (isEmpty) return (
      <ErrorState 
        title={emptyTitle}
        description={emptyDescription}
      />
    );

    return (
      <View style={styles.gridContainer}>
        {data?.map((item, index) => (
          <AverageItem
            key={index}
            label={item.label}
            value={item.value}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {showInfoIcon ? <InfoIcon/> : null}
      </View>
      {renderContent()}
    </View>
  );
};

export default TableData; 