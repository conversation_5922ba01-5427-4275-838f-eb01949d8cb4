import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical:8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  info: {
    ...typography.text,
    color: '#101010',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  averageItem: {
    width: '45%', // Allows 2 items per row with spacing
  },
  label: {
    ...typography.body2,
    color: '#101010B2',
    marginBottom: 4,
  },
  value: {
    ...typography.heading14,
    color: '#101010',
  },
  rowShimmer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  labelShimmer: {
    width: '40%',
    height: 24,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  valueShimmer: {
    width: '30%',
    height: 24,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  }
}); 