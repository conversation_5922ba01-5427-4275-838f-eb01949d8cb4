import React, { useState } from 'react';
import { View, Image } from 'react-native';
// import { useColorScheme } from 'react-native';
import { ICONS, ICONS_NAME } from './IconList';
import { styles } from './styles';

const CompanyIcon = ({ name, type = ICONS_NAME.STOCKS, style, url, fallBackImage }) => {
  const [imageError, setImageError] = useState(false);
  const isDark = false;

  const getFallBackImage = () => {
    if (fallBackImage) {
      return fallBackImage;
    }
    const iconType = ICONS[type] || ICONS[ICONS_NAME.STOCKS];
    const FallbackIcon = isDark ? iconType.dark : iconType.light;
    return <FallbackIcon width={32} height={32} />;
  };

  const getCompanyLogoUrl = () => {
    if (url) {
      return url;
    }
    return `https://static.paytmmoney.com/logos/company/${name}.png`;
  };

  return (
    <View style={[styles.container, style]}>
      {imageError ? (
        getFallBackImage()
      ) : (
        <Image
          style={styles.image}
          source={{ uri: getCompanyLogoUrl() }}
          onError={() => setImageError(true)}
          resizeMode="contain"
        />
      )}
    </View>
  );
};

export default CompanyIcon; 