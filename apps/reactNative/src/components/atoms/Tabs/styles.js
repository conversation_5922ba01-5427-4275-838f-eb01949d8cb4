import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E7F1F8',
  },
  // scrollContent: {
  //   paddingHorizontal: 16,
  // },
  tab: {
    marginHorizontal: 12,
    
  },
  activeTab:{
    borderBottomWidth: 2,
    borderBottomColor: '#1F51E6',
  },
  tabText: {
    ...typography.body2R,
    color: '#1010108A',
    paddingBottom: 8,
  },
  activeTabText: {
    ...typography.body2B2,
    color: '#101010',

  },
}); 