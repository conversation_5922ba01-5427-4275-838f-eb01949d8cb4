import React, { useState, useRef } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Animated } from 'react-native';
import { styles } from './styles';

const Tabs = ({ tabs, activeTab, onTabPress }) => {
  const [tabWidths, setTabWidths] = useState({});
  const [textWidths, setTextWidths] = useState({});
  const [containerWidth, setContainerWidth] = useState(0);
  const scrollViewRef = useRef(null);
  const indicatorAnim = useRef(new Animated.Value(0)).current;

  const handleTabPress = (tab, index) => {
    onTabPress(tab);
    
    // Calculate indicator position
    let position = 0;
    for (let i = 0; i < index; i++) {
      position += (tabWidths[i] || 0);
    }
    // Add padding to center the indicator
    position += (tabWidths[index] - textWidths[index]) / 2;

    // Animate indicator
    Animated.timing(indicatorAnim, {
      toValue: position,
      duration: 200,
      useNativeDriver: true,
    }).start();

    // Scroll to make active tab visible
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: position - containerWidth / 2 + (tabWidths[index] || 0) / 2,
        animated: true,
      });
    }
  };

  const measureTab = (event, index) => {
    const { width } = event.nativeEvent.layout;
    setTabWidths(prev => ({
      ...prev,
      [index]: width,
    }));
  };

  const measureText = (event, index) => {
    const { width } = event.nativeEvent.layout;
    setTextWidths(prev => ({
      ...prev,
      [index]: width,
    }));
  };

  return (
    <View 
      style={styles.container}
      onLayout={(e) => setContainerWidth(e.nativeEvent.layout.width)}
    >
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {tabs.map((tab, index) => (
          <TouchableOpacity
            key={tab}
            onPress={() => handleTabPress(tab, index)}
            onLayout={(e) => measureTab(e, index)}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
          >
            <Text 
              style={[
                styles.tabText,
                activeTab === tab && styles.activeTabText
              ]}
              onLayout={(e) => measureText(e, index)}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
        {/* <Animated.View 
          style={[
            styles.indicator,
            {
              width: textWidths[tabs.indexOf(activeTab)],
              transform: [{ translateX: indicatorAnim }],
            }
          ]} 
        /> */}
      </ScrollView>
    </View>
  );
};

export default Tabs; 