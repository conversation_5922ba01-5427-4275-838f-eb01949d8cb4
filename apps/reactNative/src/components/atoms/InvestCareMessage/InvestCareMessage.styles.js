import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 10,
  },
  contentView: {
    paddingLeft: 10,
    flex: 1,
  },
  displayName: {
    alignItems: 'center',
    width: '100%',
  },
  displayNameText: {
    ...typography.heading4B1, // 14px, 600 weight, 1.43 lineHeight
    color: '#101010', // Black5
    textAlign: 'center',
  },
  messageText: {
    ...typography.body1R, // 14px, regular weight, 1.43 lineHeight
    color: '#101010', // Black5
    textAlign: 'center',
  },
  descContainer: {
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  desc: {
    ...typography.body1R, // 14px, regular weight, 1.43 lineHeight
    color: '#101010', // Black5
    textAlign: 'center',
  },
  knowMore: {
    marginLeft: 4,
    ...typography.body2B1, // 12px, 600 weight, 1.5 lineHeight
    color: '#013da6', // Blue6
    textAlign: 'center',
  },
}); 