import React from 'react';
import { View, Text, TouchableOpacity, Linking } from 'react-native';
import If from '../If';
import {STATICS} from "./enums"
import { styles } from './InvestCareMessage.styles';

const InvestCareMessage = ({
  displayName = '',
  message = '',
  ctaText = '',
  ctaLink = '',
  analyticalEvents = {},
  isFno = false,
}) => {
  const onCtaClick = (link) => {
    if (link) {
      Linking.openURL(link);
    }
  };

  if (isFno) {
    return (
      <View style={styles.container}>
        <View style={styles.displayName}>
          <Text>
            <Text style={styles.displayNameText}>
              {displayName}
            </Text>
            <Text style={styles.messageText}>
              {' '}{message}
            </Text>
          </Text>
          <If test={ctaText && ctaLink}>
            <TouchableOpacity
              onPress={() => {
                if (analyticalEvents?.investCareKnowMore) {
                  analyticalEvents.investCareKnowMore();
                }
                onCtaClick(ctaLink);
              }}
            >
              <Text style={styles.knowMore}>
                {' '}{ctaText}
              </Text>
            </TouchableOpacity>
          </If>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.displayName}>
        <Text>
          <Text style={styles.displayNameText}>
            {displayName}
          </Text>
          <Text style={styles.messageText}>
            {' '}{message}
          </Text>
        </Text>
      </View>

      <If test={ctaText && ctaLink}>
        <View style={styles.descContainer}>
          <Text style={styles.desc}>
            {STATICS.DESC}
          </Text>
          <TouchableOpacity
            onPress={() => {
              if (analyticalEvents?.investCareKnowMore) {
                analyticalEvents.investCareKnowMore();
              }
              onCtaClick(ctaLink);
            }}
          >
            <Text style={styles.knowMore}>
              {ctaText}
            </Text>
          </TouchableOpacity>
        </View>
      </If>
    </View>
  );
};

export default InvestCareMessage; 