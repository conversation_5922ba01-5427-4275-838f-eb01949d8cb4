import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  scrollView: {
    flexGrow: 0,
  },
  tabsContainer: {
    flexDirection: 'row',
    // paddingHorizontal: 16,
    gap: 8,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#F5F0F0',
  },
  activeTab: {
    backgroundColor: '#101010',
  },
  tabText: {
    ...typography.text,
    color: '#101010',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
}); 