import React, { useRef } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { styles } from './styles';

const FlatTabs = ({ 
  tabs = [], 
  activeTab, 
  onTabPress,
  containerStyle = {},
  tabStyle = {},
  activeTabStyle = {},
  tabTextStyle = {},
  activeTabTextStyle = {},
}) => {
  const scrollViewRef = useRef(null);
  const tabRefs = useRef({});
  const screenWidth = Dimensions.get('window').width;

  const handleTabPress = (tab) => {
    onTabPress(tab);
    
    // Scroll the selected tab into center view
    if (tabRefs.current[tab] && scrollViewRef.current) {
      tabRefs.current[tab].measureLayout(
        scrollViewRef.current,
        (x) => {
          scrollViewRef.current.scrollTo({
            x: Math.max(0, x - (screenWidth - 100) / 2),
            animated: true,
          });
        },
        (error) => console.log('Failed to measure:', error)
      );
    }
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.scrollView}
      contentContainerStyle={styles.scrollViewContent}
    >
      <View style={[styles.tabsContainer, containerStyle]}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            ref={(ref) => (tabRefs.current[tab] = ref)}
            style={[
              styles.tab,
              tabStyle,
              activeTab === tab && styles.activeTab,
              activeTab === tab && activeTabStyle,
            ]}
            onPress={() => handleTabPress(tab)}
          >
            <Text style={[
              styles.tabText,
              tabTextStyle,
              activeTab === tab && styles.activeTabText,
              activeTab === tab && activeTabTextStyle,
            ]}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

export default FlatTabs; 