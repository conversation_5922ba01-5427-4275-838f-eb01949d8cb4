import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: '#E7F1F8',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding:16
  },
  title: {
    ...typography.heading4B1,
    color: '#101010',
  },
  arrow: {
    opacity: 0.7,
  },
  content: {
    overflow: 'hidden',
  }
}); 