import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated, LayoutAnimation } from 'react-native';
import ArrowUpSvg from '../../../assets/arrowUp.svg';
import { styles } from './styles';

const Accordion = ({
  title,
  children,
  isOpen = false,
  onToggle = () => null,
  customStyle = {},
  titleStyle = {},
  contentStyle = {},
}) => {
  const [expanded, setExpanded] = useState(isOpen);
  const [animation] = useState(new Animated.Value(isOpen ? 1 : 0));

  const toggleAccordion = () => {
    const newValue = !expanded;
    
    // Configure spring animation
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    
    setExpanded(newValue);
    onToggle(newValue);

    Animated.timing(animation, {
      toValue: newValue ? 1 : 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const arrowRotation = animation.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '0deg'],
  });

  return (
    <View style={[styles.container, customStyle]}>
      <TouchableOpacity 
        onPress={toggleAccordion}
        style={[styles.header, titleStyle]}
      >
        <Text style={styles.title}>{title}</Text>
        <Animated.View 
          style={[
            styles.arrow,
            { transform: [{ rotate: arrowRotation }] }
          ]}
        >
          <ArrowUpSvg width={10} height={6} />
        </Animated.View>
      </TouchableOpacity>
      
      {expanded && (
        <View style={[styles.content, contentStyle]}>
          {children}
        </View>
      )}
    </View>
  );
};

export default Accordion; 