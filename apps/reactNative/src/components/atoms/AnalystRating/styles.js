import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    gap: 16,
    borderRadius: 12,
    width: "100%",
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 16,
    position: 'relative',
  },
  patternContainer: {
    position: 'absolute',
    width: 84,
    height: 84,
    left: -35,
    top: -42,
    zIndex: 1,
  },
  progressBarContainer: {
    width: 84,
    height: 84,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
  },
  ratingContainer: {
    flex: 1,
    gap: 16,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 8,
  },
  ratingType: {
    ...typography.body2R,
    color: '#101010',
    width: 40,
  },
  barSection: {
    flex: 1,
    flexDirection: 'column',
  },
  barContainer: {
    height: 8,
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
    width: "100%",
    overflow: 'hidden',
  },
  bar: {
    height: '100%',
    borderRadius: 4,
  },
  percentage: {
    ...typography.text,
    color: '#101010',
  },
  targetContainer: {
    paddingTop: 16,
    marginHorizontal: 16,
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#F5F0F0"
  },
  targetClass: {
    ...typography.body2B1,
    color: '#101010',
    paddingBottom: 8
  },
  headersContainer: {
    flexDirection: 'row',
    justifyContent: "space-between"
  },
  headersText: {
    ...typography.body2B3,
    color: '#1010108A'
  },
  low: {
    ...typography.text,
    color: '#E63757'
  },
  mean: {
    ...typography.text,
    color: '#101010'
  },
  high: {
    ...typography.text,
    color: '#52CB77'
  },
  bottomContainer: {
    paddingVertical: 7,
    paddingHorizontal: 12,
    backgroundColor: '#ECF2F8',
    borderBottomEndRadius: 12,
    borderBottomLeftRadius: 12,
    flexDirection: 'row',
    alignContent: 'center',
    justifyContent: 'space-between'
  },
  bottomtext: {
    ...typography.body3R2,
    color: '#101010',
    paddingLeft: 8
  },
  bottomContent: {
    display: "flex",
    flexDirection: 'row',
    alignItems: 'center'
  }
}); 