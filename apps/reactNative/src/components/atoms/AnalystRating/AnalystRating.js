import React from 'react';
import { View, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useCompanyReports } from "@paytm-money/store";
import ProgressBar from "../ProgressBar";
import Accordion from '../Accordion/Accordion';
import PatternBuy from "../../../assets/patternBuy.svg";
import PatternHold from "../../../assets/patternHold.svg";
import PatternSell from "../../../assets/patternSell.svg";
import TestingImage from "../../../assets/testing.svg";
import InfoIcon from "../../../assets/Info_icon.svg";
import { styles } from './styles';

const AnalystRating = ({ isin,pmlId }) => {
  const { calculatedRatings, isLoading, getGradientColors } = useCompanyReports(isin,pmlId);

  const getPattern = () => {
    const type = calculatedRatings?.highestRating?.type?.toLowerCase();
    if (!type) return null;
    
    switch (type) {
      case 'buy':
        return <PatternBuy style={styles.patternContainer} />;
      case 'hold':
        return <PatternHold style={styles.patternContainer} />;
      case 'sell':
        return <PatternSell style={styles.patternContainer} />;
      default:
        return <PatternBuy style={styles.patternContainer} />;
    }
  };

  if (isLoading || !calculatedRatings) return null;

  const { highestRating, ratings, headers, totalAnalysts ,isReportData} = calculatedRatings;

  return (
    <Accordion
      title="Expert Opinion"
      isOpen={true}
      customStyle={styles.analystRating}
    >
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.progressBarContainer}>
            {getPattern()}
            <ProgressBar 
              percentage={highestRating.percentage} 
              colour={getGradientColors(highestRating.type)[0]} 
              middleText={highestRating.type}
            />
          </View>
          <View style={styles.ratingContainer}>
            {ratings.map(({ type, percentage }) => (
              <View key={type} style={styles.ratingRow}>
                <View style={styles.barSection}>
                  <Text style={styles.ratingType}>{type}</Text>
                  <View style={styles.barContainer}>
                    <LinearGradient
                      colors={getGradientColors(type)}
                      end={{x: 0, y: 0}} 
                      start={{x: 1, y: 0}}
                      style={[
                        styles.bar,
                        { width: `${percentage}%` }
                      ]}
                    />
                  </View>
                </View>
                <Text style={styles.percentage}>{percentage}%</Text>
              </View>
            ))}
          </View>
        </View>
        {isReportData && <View style={styles.targetContainer}>
          <Text style={styles.targetClass}>12-Month Target Price</Text>
          <View style={styles.headersContainer}>
            {headers.map((el) => (
              <Text key={el.title} style={styles.headersText}>{el.title}</Text>
            ))}
          </View>
          <View style={styles.headersContainer}>
            {headers.map((el) => (
              <Text key={el.title} style={styles[el.title.toLowerCase()]}>{el.value}</Text>
            ))}
          </View>
        </View>}
        <View style={styles.bottomContainer}>
          <View style={styles.bottomContent}>
            <TestingImage/>
            <Text style={styles.bottomtext}>
              Aggregated by Refinitiv from {totalAnalysts} analysts
            </Text>
          </View>
          <InfoIcon/>
        </View>
      </View>
    </Accordion>
  );
};

export default AnalystRating; 