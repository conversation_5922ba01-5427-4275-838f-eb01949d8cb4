import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 46,
    paddingHorizontal: 10,
    width: '100%',
    borderRadius: 4,
  },
  isPrimaryBlue: {
    backgroundColor: '#004393', // DBlue
  },
  isSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3e74dd', // Blue6
  },
  isTertiary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#004393', // DBlue
    height: 30,
    maxWidth: 130,
  },
  isRedButton: {
    backgroundColor: '#fff', // secondaryBgColor
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 3 },
    shadowRadius: 11,
    shadowOpacity: 1,
    elevation: 3,
  },
  buttonText: {
    ...typography.heading3B1, // 16px, 600 weight, 1.5 lineHeight
  },
  isWhiteText: {
    color: '#fff',
  },
  isSecondaryText: {
    color: '#3e74dd', // Blue6
  },
  isTertiaryText: {
    ...typography.body3B3, // 10px, 600 weight
    color: '#004393', // DBlue
  },
  isRed: {
    color: '#d23d50', // Red
  },
  isTextOnly: {
    backgroundColor: 'transparent',
    padding: 0,
    width: 'auto',
    color: '#004393', // DBlue
  },
  isPrimaryText: {
    backgroundColor: 'transparent',
    padding: 0,
    width: 'auto',
    color: '#3e74dd', // Blue6
  },
  isDisabledText: {
    color: '#fff',
    ...typography.heading3B, // 16px, 700 weight, 1.5 lineHeight
  },
  isDisabled: {
    backgroundColor: '#6f6f6f', // DisabledGray
    opacity: 0.3,
  },
  isPrimary: {
    backgroundColor: '#3e74dd', // Blue6
  },
  image: {
    width: 16,
    height: 15,
    resizeMode: 'contain',
    marginRight: 5,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 