import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Image,
  View,
} from 'react-native';
import { styles } from './Button.styles';

const Button = ({
  buttonText = '',
  image = null,
  rightImage = null,
  isDisabled = false,
  isPrimaryBlue = false,
  isSecondary = false,
  isTertiary = false,
  isTextOnly = false,
  onClickHandler = () => null,
  buttonClassName = {},
  buttonTextClassName = {},
  isPrimary = false,
  isRed = false,
  isPrimaryText = false,
  isLoading = false,
  loadingText = '',
  showGreySpinner = true,
  testID = '',
  customImageStyle = {},
  applyCss= false,
  customRightImageStyle = {},
}) => {
  const buttonStyles = [
    styles.button,
    isSecondary && styles.isSecondary,
    isTertiary && styles.isTertiary,
    isTextOnly && styles.isTextOnly,
    isPrimaryText && styles.isPrimaryText,
    (isDisabled || isLoading) && styles.isDisabled,
    isRed && styles.isRedButton,
    isPrimaryBlue && styles.isPrimaryBlue,
    isPrimary && styles.isPrimary,
    buttonClassName,
  ];

  const textStyles = [
    !applyCss && styles.buttonText,
    isPrimaryBlue && styles.isWhiteText,
    isSecondary && styles.isSecondaryText,
    isTertiary && styles.isTertiaryText,
    isRed && styles.isRed,
    isDisabled && styles.isDisabledText,
    buttonTextClassName,
  ];

  return (
    <TouchableOpacity
      testID={testID}
      disabled={isDisabled || isLoading}
      onPress={onClickHandler}
      style={buttonStyles}
      activeOpacity={0.7}
    >
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator 
            color={showGreySpinner ? '#6f6f6f' : '#fff'} 
            size="small" 
          />
          {loadingText && (
            <Text style={textStyles}>{loadingText}</Text>
          )}
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {image && (
            <Image
              style={[styles.image, customImageStyle]}
              source={typeof image === 'string' ? { uri: image } : image}
            />
          )}
          <Text style={textStyles}>{buttonText}</Text>
          {rightImage && (
            <Image
              style={[styles.image, customRightImageStyle]}
              source={typeof rightImage === 'string' ? { uri: rightImage } : rightImage}
            />
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default Button;
