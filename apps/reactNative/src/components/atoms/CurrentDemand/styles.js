import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  accordionContainer: {
    marginBottom: 32,
  },
  header: {
    padding: 0,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    // marginTop: 16,
    
    gap: 16,
  },
  contentContainer:{
    paddingHorizontal:16,
    paddingBottom:16
  },
  bidContainer: {
    flex: 1,
  },
  bidTitle: {
    marginBottom: 8,
  },
  bidTitleText: {
    ...typography.body2B2,
    color: '#101010',
  },
  sellTitle: {
    alignItems: 'flex-end',
  },
  countContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  count: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  countLabel: {
    ...typography.body2R,
    color: '#101010',
  },
  countValue: {
    ...typography.body2B2,
    color: '#101010',
  },
  demandRatioContainer: {
    marginTop: 16,
  },
  progressCount1: {
    color: '#21C179',
  },
  progressCount2: {
    color: '#FD5154',
  },
  shimmerSmall: {
    width: 60,
    height: 10,
    borderRadius: 4,
    backgroundColor: '#EEEEEE26',
  },
  shimmerLarge: {
    width: '100%',
    height: 6,
    borderRadius: 4,
    backgroundColor: '#EEEEEE26',
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 6,
  },
  divider: {
    marginTop:40,
    marginBottom:20,
    width: 1,
    backgroundColor: '#D8E7F7',
    alignSelf: 'stretch',
  },

  volumeLable:{
    ...typography.body2R,
    color: '#1010108A',
  },
  volumeValue:{
    ...typography.text,
    color: '#101010',
  }
}); 