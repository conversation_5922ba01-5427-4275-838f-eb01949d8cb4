import React, { useState, useEffect ,memo} from 'react';
import { View, Text } from 'react-native';
import Shimmer from '../Shimmer/Shimmer';
import Accordion from '../Accordion/Accordion';
import MarketDepth from '../MarketDepth/MarketDepth';
import PercentageRatio from '../PercentageRatio/PercentageRatio';
import { styles } from './styles';

const STATIC_DATA = {
  BUY_TITLE: 'Bid (Buy Orders)',
  SELL_TITLE: 'Ask (Sell Orders)',
  BID_TOTAL: 'Bid Total',
  ASK_TOTAL: 'Ask Total',
  VOLUME:'Volume',
  AVG_PRICE:'Avg. Price'
};

const DEPTH_TYPE = {
  BUY: 'BUY',
  SELL: 'SELL',
};

const CurrentDemand = ({
  securityId,
  exchange,
  segment,
  pmlId,
  isCompanyDetailLoading,
  depthResponse,
  topAskBidFeedResponse,
}) => {
  const [isMarketDepthOpen, setIsMarketDepthOpen] = useState(true);


  // const ctx = useMarketStatus();

  // const status = !ctx?.marketStatus?.[segment]?.[exchange || 'NSE']?.amo;

  // const depthResponse = useResultFromFeed(
  //   REQUEST_TYPES.DEPTH,
  //   requiredDepthFeedResponse,
  //   { securityId, exchange, segment },
  // );

  // const stockFeed = useFeeds(REQUEST_TYPES.STOCK, requiredStockFeedResponse, {
  //   securityId,
  //   exchange,
  //   segment,
  // });

  // const topAskBidFeedResponse = useObservable(
  //   () =>
  //     getFeedByReqType(stockFeed, RESPONSE_TYPES.TOP_BID_ASK).pipe(
  //       map(({ totalSellQuantity = 0, totalBuyQuantity = 0 }) => {
  //         if (totalSellQuantity === 0 && totalBuyQuantity === 0) {
  //           return {
  //             totalSellQty: totalSellQuantity,
  //             totalBuyQty: totalBuyQuantity,
  //             totalBidPercent: 50,
  //             totalAskPercent: 50,
  //           };
  //         }
  //         return {
  //           totalSellQty: totalSellQuantity,
  //           totalBuyQty: totalBuyQuantity,
  //           totalBidPercent:
  //             (totalBuyQuantity / (totalBuyQuantity + totalSellQuantity)) * 100,
  //           totalAskPercent:
  //             (totalSellQuantity / (totalBuyQuantity + totalSellQuantity)) *
  //             100,
  //         };
  //       }),
  //     ),
  //   [stockFeed],
  // );

  
  const toggleAccordion = () => {
    const newValue = !isMarketDepthOpen;
    setIsMarketDepthOpen(newValue);
  };

  const getMarketDepth = () => (
    <>
      <View style={styles.container}>
        <View style={styles.bidContainer}>
          <View style={styles.bidTitle}>
            {isCompanyDetailLoading ? (
              <Shimmer style={styles.shimmerSmall} />
            ) : (
              <Text style={styles.bidTitleText}>{STATIC_DATA.BUY_TITLE}</Text>
            )}
          </View>
          <MarketDepth
            depthResponse={depthResponse}
            priceKey="bidPrice"
            valKey="bidQuantity"
            type={DEPTH_TYPE.BUY}
            isCompanyDetailLoading={isCompanyDetailLoading}
          />
        </View>
        <View style={styles.divider} />
        <View style={styles.bidContainer}>
          <View style={[styles.bidTitle, styles.sellTitle]}>
            {isCompanyDetailLoading ? (
              <Shimmer style={styles.shimmerSmall} />
            ) : (
              <Text style={styles.bidTitleText}>{STATIC_DATA.SELL_TITLE}</Text>
            )}
          </View>
          <MarketDepth
            depthResponse={depthResponse}
            priceKey="askPrice"
            valKey="askQuantity"
            type={DEPTH_TYPE.SELL}
            isCompanyDetailLoading={isCompanyDetailLoading}
          />
        </View>
      </View>

      <View style={styles.countContainer}>
        {isCompanyDetailLoading ? (
          <View style={styles.count}>
            <Shimmer style={styles.shimmerSmall} />
            <Shimmer style={styles.shimmerSmall} />
          </View>
        ) : (
          <>
            <View style={styles.count}>
              <Text style={styles.countLabel}>{STATIC_DATA.BID_TOTAL}</Text>
              <Text style={styles.countValue}>
                {topAskBidFeedResponse?.totalBuyQty || '0'}
              </Text>
            </View>
            <View style={styles.count}>
              <Text style={styles.countLabel}>{STATIC_DATA.ASK_TOTAL}</Text>
              <Text style={styles.countValue}>
                {topAskBidFeedResponse?.totalSellQty || '0'}
              </Text>
            </View>
          </>
        )}
      </View>
    </>
  );

  return (
    <Accordion
      isOpen={isMarketDepthOpen}
      onToggle={toggleAccordion}
      title="Market Depth"
    >
      <View style={styles.contentContainer}>
      {getMarketDepth()}
      {isCompanyDetailLoading ? (
        <>
          <Shimmer style={styles.shimmerLarge} />
          <View style={styles.labelContainer}>
            <Shimmer style={styles.shimmerSmall} />
            <Shimmer style={styles.shimmerSmall} />
          </View>
        </>
      ) : (
        <View style={styles.demandRatioContainer}>
          <PercentageRatio
            percentageSecond={topAskBidFeedResponse?.totalAskPercent || 50}
            firstSubLabel={`${Math.round(topAskBidFeedResponse?.totalBidPercent || 50)}%`}
            secondSubLabel={`${Math.round(topAskBidFeedResponse?.totalAskPercent || 50)}%`}
            firstLabelStyle={styles.progressCount1}
            secondLabelStyle={styles.progressCount2}
          />
          <View style={styles.countContainer}>
            <View style={styles.count}>
              <Text style={styles.volumeLable}>{STATIC_DATA.VOLUME}</Text>
              <Text style={styles.volumeValue}>
                123
              </Text>
            </View>
            <View style={styles.count}>
              <Text style={styles.volumeLable}>{STATIC_DATA.AVG_PRICE}</Text>
              <Text style={styles.volumeValue}>
                456
              </Text>
            </View>
      </View>
        </View>
        
      )}
      </View>
    </Accordion>
  );
};

export default memo(CurrentDemand); 