import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  breakoutContainer: {
    borderRadius: 12,
    marginVertical:8
  },
  gradientBackground: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  arrowButton: {
    padding: 4,
  },
  cardsContainer: {
    flexDirection: 'row',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    width: 250,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 6,
  },
  cardTitle: {
   
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
  },
  infoButton: {
    padding: 4,
  },
  cardDescription: {
    marginBottom: 8,
    fontSize: 12,
    lineHeight: 16,
    color: '#101010B2',
  },
  cardDate: {
    fontSize: 12,
    color: '#1010108A',
   
    lineHeight: 16,
  },
}); 