import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useBreakoutDetailsForStock } from '@paytm-money/store';

// Import SVG assets
import ProfitArrow from '../../../assets/profit_slanted_arrow_green.svg';
import LossArrow from '../../../assets/loss_slanted_arrow_red.svg';
import GrayInfoIcon from '../../../assets/gray_info_icon.svg';
import RightArrowIcon from '../../../assets/right_arrow_icon.svg';
import BreakoutIcon from '../../../assets/breakout_icon.svg';

import { styles } from './styles';


const Breakout = ({ pmlId }) => {
  const { data: breakoutData, isLoading } = useBreakoutDetailsForStock(pmlId);

  const handleInfoClick = () => {
    // Handle info click
    console.log('Info icon clicked');
  };

  const handleArrowClick = () => {
    // Handle arrow click
    console.log('Right arrow clicked');
  };

  if (isLoading || !breakoutData?.length ) return null;

  return (
    <LinearGradient
      colors={['#FFFFFF', '#D3D4FF']}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.1, y: 1 }}
      style={styles.breakoutContainer}
    >
      <View style={styles.gradientBackground}>
        <View style={styles.header}>
          <View style={styles.title}>
            <BreakoutIcon width={142} height={32} />
          </View>
          <TouchableOpacity onPress={handleArrowClick}>
            <RightArrowIcon width={24} height={24} style={styles.arrowButton} />
          </TouchableOpacity>
        </View>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.cardsContainer}
        >
          {breakoutData.map((item, index) => (
            <View key={index} style={styles.card}>
              <View style={styles.cardHeader}>
                <View style={styles.titleContainer}>
                  <View style={styles.icon}>
                    {item.trend === 'up' ? 
                      <ProfitArrow width={16} height={16} /> : 
                      <LossArrow width={16} height={16} />
                    }
                  </View>
                  <Text style={styles.cardTitle} numberOfLines={1}>{item.title}</Text>
                </View>
                <TouchableOpacity onPress={handleInfoClick}>
                  <GrayInfoIcon width={15} height={15} style={styles.infoButton} />
                </TouchableOpacity>
              </View>
              <Text style={styles.cardDescription}>{item.description}</Text>
              <Text style={styles.cardDate}>{item.date}</Text>
            </View>
          ))}
        </ScrollView>
      </View>
    </LinearGradient>
  );
};

export default Breakout; 