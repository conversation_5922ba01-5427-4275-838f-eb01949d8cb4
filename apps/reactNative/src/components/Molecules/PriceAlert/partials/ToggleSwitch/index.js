import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';
import UpArrow from '../../../../../assets/UpArrow.svg';
import DownArrow from '../../../../../assets/DownArrow.svg';

const ToggleSwitch = ({ options, selected, onChange, disabled, showIcons }) => {
  const getIcon = (option) => {
    if (!showIcons) return null;
    
    switch (option) {
      case 'Up':
        return <UpArrow width={13} height={12} style={styles.icon} />;
      case 'Down':
        return <DownArrow width={13} height={12} style={styles.icon} />;
      default:
        return null;
    }
  };

  return (
    <View style={[styles.toggleSwitch, disabled && styles.disabled]}>
      {options.map((option) => (
        <TouchableOpacity
          key={option}
          style={[
            styles.toggleButton,
            selected === option && styles.active
          ]}
          onPress={() => !disabled && onChange(option)}
          disabled={disabled}
        >
          <View style={styles.buttonContent}>
            {getIcon(option)}
            <Text style={[
              styles.toggleText,
              selected === option && styles.activeText
            ]}>
              {option}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  toggleSwitch: {
    flexDirection: 'row',
    backgroundColor: '#D8E7F7',
    borderRadius: 6,
    padding: 2,
  },
  toggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    width: 75,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  active: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  toggleText: {
    ...typography.caption1R,
    color: '#1010108A',
    textAlign: "center",
  },
  activeText: {
    color: '#101010',
    ...typography.caption1M,
  },
  disabled: {
    opacity: 0.5,
  },
  icon: {
    marginRight: 4,
  }
});

export default ToggleSwitch;