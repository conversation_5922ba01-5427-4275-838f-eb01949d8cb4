import React from 'react';
import { View, TextInput, TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { styles } from './styles';

const InputSection = ({ value, onChange, onAdd, isLoading, isEditing }) => {
  return (
    <View style={styles.inputContainer}>
      <TextInput
        style={styles.inputField}
        placeholder="Enter Change %"
        value={value}
        onChangeText={onChange}
        keyboardType="numeric"
        placeholderTextColor="#1010108A"
        placeholderStyle={styles.placeholder}
        editable={!isLoading}
      />
      <TouchableOpacity 
        style={[styles.addButton, !value && styles.disabledButton]}
        onPress={onAdd}
        disabled={!value || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.addButtonText}>
            {isEditing ? 'Update Alert' : 'Add Alert'}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default InputSection; 