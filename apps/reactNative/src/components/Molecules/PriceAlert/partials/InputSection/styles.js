import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';

export const styles = StyleSheet.create({
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    gap: 12,
  },
  inputField: {
    flex: 1,
    height: 40,
    paddingHorizontal: 12,
    backgroundColor: 'white',
    ...typography.body2R,
    color: '#101010',
    fontSize: 16,
  },
  placeholder: {
    fontSize: 16,
  },
  addButton: {
    height: 40,
    paddingHorizontal: 16,
    backgroundColor: '#ECF2F8',
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: "#D8E7F7"
  },
  addButtonText: {
    ...typography.body2B1,
    color: '#101010',
  },
  disabledButton: {
    opacity: 0.7,
  },
  disabledText: {
    opacity: 0.7,
  }
});
