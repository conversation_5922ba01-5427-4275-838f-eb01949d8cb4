import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';

export const styles = StyleSheet.create({
  drawerContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  firstGridRow: {
    flexDirection: 'row',
    paddingVertical: 16,
  },
  columnHeaderFirst: {
    ...typography.body2R,
    color: '#101010B2',
    flex: 1,
  },
  columnHeader: {
    ...typography.body2R,
    color: '#101010B2',
    flex: 1,
    textAlign: "right",
    marginRight: 16
  },
  columnHeaderLast: {
    width: 40,
  },
  manageAllAlerts: {
    flexDirection: "row",
    gap: 8,
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1010100F',
  },
  manageAllAlertsText: {
    ...typography.heading3B2,
    color: '#1576DB',
  },
  createNewButton: {
    backgroundColor: '#101010',
    borderRadius: 48,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  createNewButtonText: {
    ...typography.heading3B1,
    color: '#FFFFFF',
  }
});
