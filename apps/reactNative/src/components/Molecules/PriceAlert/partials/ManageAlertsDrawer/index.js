import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { styles } from './styles';
import SwipeableRow from '../SwipeableRow';
import Shimmer from '../../../../atoms/Shimmer/Shimmer';
import RightIcon from "../../../../../assets/rightIconBlue.svg";

const LoadingShimmer = () => (
  <Shimmer
    type="card"
    cardArray={[
      {
        height: '20px',
        row: ['40%', '30%', '20%'],
        margin: '16px 0',
      },
      {
        height: '20px',
        row: ['40%', '30%', '20%'],
        margin: '16px 0',
      },
      {
        height: '20px',
        row: ['40%', '30%', '20%'],
        margin: '16px 0',
      },
    ]}
  />
);

const ManageAlertsDrawer = ({ alerts = [], onEdit, onDelete, isLoading, onCreateNew }) => {
  return (
    <View style={styles.drawerContainer}>
      <View style={styles.firstGridRow}>
        <Text style={styles.columnHeaderFirst}>Target Price</Text>
        <Text style={styles.columnHeader}>At LTP</Text>
        <View style={styles.columnHeaderLast} />
      </View>

      {isLoading ? (
        <LoadingShimmer />
      ) : (
        alerts.map((alert) => (
          <SwipeableRow 
            key={alert.id}
            alert={alert}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))
      )}

      <TouchableOpacity 
        style={styles.manageAllAlerts}
        onPress={onCreateNew}
      >
        <Text style={styles.manageAllAlertsText}>Manage All Alerts</Text>
        <RightIcon/>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.createNewButton}
        onPress={onCreateNew}
      >
        <Text style={styles.createNewButtonText}>Create New Alert</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ManageAlertsDrawer; 