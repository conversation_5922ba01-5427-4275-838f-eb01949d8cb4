import React, { useRef, useState } from 'react';
import { Animated, View, Text, TouchableOpacity, PanResponder } from 'react-native';
import EditIcon from '../../../../../assets/editIcon.svg';
import { styles } from './styles';

const SWIPE_THRESHOLD = -80;

const SwipeableRow = ({ alert, onEdit, onDelete }) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const [isOpen, setIsOpen] = useState(false);
  
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        translateX.setOffset(translateX._value);
        translateX.setValue(0);
      },
      onPanResponderMove: Animated.event(
        [null, { dx: translateX }],
        { 
          useNativeDriver: false,
          listener: (_, gestureState) => {
            const currentValue = translateX._value + translateX._offset;
            
            if (isOpen) {
              // If open, allow movement from -80 to 0
              if (currentValue > 0 || currentValue < SWIPE_THRESHOLD) {
                translateX.setValue(0);
              }
            } else {
              // If closed, only allow left swipe up to -80
              if (currentValue < SWIPE_THRESHOLD) {
                translateX.setValue(SWIPE_THRESHOLD - translateX._offset);
              } else if (currentValue > 0) {
                translateX.setValue(-translateX._offset);
              }
            }
          }
        }
      ),
      onPanResponderRelease: (_, gestureState) => {
        translateX.flattenOffset();
        const currentValue = translateX._value;

        if (isOpen) {
          if (gestureState.dx > 20) {
            // Close if swiped right enough
            Animated.spring(translateX, {
              toValue: 0,
              useNativeDriver: true,
              friction: 5,
            }).start(() => setIsOpen(false));
          } else {
            // Stay open
            Animated.spring(translateX, {
              toValue: SWIPE_THRESHOLD,
              useNativeDriver: true,
              friction: 5,
            }).start();
          }
        } else {
          if (gestureState.dx < -20) {
            // Open if swiped left enough
            Animated.spring(translateX, {
              toValue: SWIPE_THRESHOLD,
              useNativeDriver: true,
              friction: 5,
            }).start(() => setIsOpen(true));
          } else {
            // Stay closed
            Animated.spring(translateX, {
              toValue: 0,
              useNativeDriver: true,
              friction: 5,
            }).start();
          }
        }
      },
      onPanResponderTerminate: () => {
        Animated.spring(translateX, {
          toValue: isOpen ? SWIPE_THRESHOLD : 0,
          useNativeDriver: true,
          friction: 5,
        }).start();
      },
    })
  ).current;

  return (
    <View style={styles.rowContainer}>
      <TouchableOpacity 
        style={styles.deleteButton}
        onPress={() => {
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
            friction: 5,
          }).start(() => {
            setIsOpen(false);
            onDelete(alert);
          });
        }}
      >
        <Text style={styles.deleteButtonText}>Delete</Text>
      </TouchableOpacity>

      <Animated.View 
        style={[
          styles.gridRow,
          { transform: [{ translateX }] }
        ]}
        {...panResponder.panHandlers}
      >
        <Text style={styles.tiggerPrice}>{`₹${alert.trigger_price}`}</Text>
        <Text style={styles.priceValue}>{`₹${alert.ltp}`}</Text>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={() => onEdit(alert)}
        >
          <EditIcon width={20} height={20} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default SwipeableRow; 