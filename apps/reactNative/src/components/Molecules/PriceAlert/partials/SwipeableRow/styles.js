import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';

export const styles = StyleSheet.create({
  rowContainer: {
    position: 'relative',
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  deleteButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    height: '100%',
    width: 80,
    backgroundColor: '#E63757',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButtonText: {
    ...typography.body2B2,
    color: '#FFFFFF',
  },
  gridRow: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1010100F',
    backgroundColor: '#FFFFFF',
    width: '100%',
  },
  tiggerPrice: {
    ...typography.heading3,
    color: '#101010',
    flex: 1,
  },
  priceValue: {
    ...typography.heading14,
    color: '#101010B2',
    marginRight: 16,
    textAlign: 'right',
  },
  editButton: {
    width: 40,
    alignItems: 'center',
  }
});

