import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginVertical: 8,
  },
  predefinedContainer: {
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    ...typography.heading4B1,
    marginLeft: 8,
  },
  alertButtonsScroll: {
    marginHorizontal: -16,
  },
  alertButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 8,
  },
  alertButton: {
    padding: 6,
    borderRadius: 40,
    borderWidth: 1,
    borderColor: '#D8E7F7'
  },
  alertButtonText: {
    ...typography.caption1M,
    textAlign: 'center',
    color: '#FFFFFF'
  },
  red: {
    backgroundColor: '#E63757',
    color: '#FFFFFF'
  },
  green: {
    backgroundColor: '#52CB77',
    color: '#FFFFFF'
  },
  text_red: {
    color: '#E63757'
  },
  text_green: {
    color: '#52CB77'
  },
  customButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 48,
    minWidth: 65,
    backgroundColor: '#F5F5F5',
  },
  activeCustom: {
    backgroundColor: '#101010',
  },
  activeCustomText: {
    color: '#FFFFFF'
  },
  customButtonText: {
    ...typography.caption1M,
    color: '#101010',
    textAlign: 'center'
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  manageAlerts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    backgroundColor: '#ECF2F8',
    borderEndEndRadius: 16,
    borderEndStartRadius: 16
  },
  activeAlertsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeAlertCount: {
    ...typography.heading4B1,
    marginRight: 4,
  },
  activeAlertsText: {
    ...typography.body2R,
    color: '#1010108A',
  },
  manageButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  manageButtonText: {
    ...typography.body2B2,
    color: '#1576DB',
    marginRight: 4,
  },
  customContainer: {
    backgroundColor: '#ECF2F8',
    padding: 16,
  }
}); 