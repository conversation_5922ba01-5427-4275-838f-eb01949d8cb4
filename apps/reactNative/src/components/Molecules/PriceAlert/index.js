import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { styles } from './styles';
import { useAddPriceAlert, useSnackbar, usePriceAlertDetails, useDeletePriceAlert } from '@paytm-money/store';
import ToggleSwitch from './partials/ToggleSwitch';
import InputSection from './partials/InputSection';
import ManageAlertsDrawer from './partials/ManageAlertsDrawer';
import PmlLogo from "../../../assets/PmlLogo.svg";
import { APPEARANCE_TYPES } from '../../../enums/snackbarEnums';
import Drawer from '../Drawer';

const predefinedAlerts = [
  { value: -10, color: 'red' },
  { value: -5, color: 'red' },
  { value: -2, color: 'red' },
  { value: 2, color: 'green' },
  { value: 5, color: 'green' },
  { value: 10, color: 'green' },
];

const PriceAlert = ({ manageAlertClicked, pmlId }) => {
  const ltp= 7.90
  const { addSnackbar } = useSnackbar();
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [isCustom, setIsCustom] = useState(false);
  const [alertType, setAlertType] = useState('%');
  const [trend, setTrend] = useState('Up');
  const [alertValue, setAlertValue] = useState('');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [editingAlert, setEditingAlert] = useState(null);
  const [isLoadingAction,setIsLoadingAction] = useState(false)

  const { data: alertData = [], refetch: refetchAlerts, isFetching } = usePriceAlertDetails(pmlId, !!pmlId);

  const { mutate: addPriceAlert, isLoading } = useAddPriceAlert({
    onSuccess: (data) => {
      const value = alertValue || selectedAlert.value;
      const sign = isCustom ? 
        (trend === 'Down' ? '-' : '') : 
        (selectedAlert?.value > 0 ? '' : '');
      
      addSnackbar({
        message: `Price alert ${editingAlert ? 'updated' : 'set'} successfully for ${alertType === '%' ? `${sign}${value}%` : `₹${value}`}`,
        type: APPEARANCE_TYPES.SUCCESS,
      });
      setSelectedAlert(null);
      setAlertValue('');
      setIsCustom(false);
      setEditingAlert(null);
      setTimeout(() => {
        setIsLoadingAction(false)
        refetchAlerts();
      }, 1000);
    },
    onError: (error) => {
      console.log(error)
      addSnackbar({
        message: error?.meta?.displayMessage || 'Failed to set price alert',
        type: APPEARANCE_TYPES.FAIL,
      });
    },
  });

  const { mutate: deletePriceAlert, isLoading: isDeleting } = useDeletePriceAlert({
    onSuccess: () => {
      addSnackbar({
        message: 'Price alert deleted successfully',
        type: APPEARANCE_TYPES.SUCCESS,
      });
      setTimeout(() => {
        setIsLoadingAction(false)
        refetchAlerts();
      }, 1000);
      
    },
    onError: (error) => {
      setIsLoadingAction(false)
      addSnackbar({
        message: error?.meta?.displayMessage || 'Failed to delete price alert',
        type: APPEARANCE_TYPES.FAIL,
      });
    },
  });

  const handlePredefinedClick = (alert) => {
    if(alert === selectedAlert){
      setSelectedAlert(null);
      setIsCustom(false)
    } else {
      setSelectedAlert(alert);
      setIsCustom(false);
    }
  };

  const handleCustomClick = () => {
    if (!selectedAlert) {
      setIsCustom(isCustom => !isCustom);
    } else {
      handleAddAlert();
    }
    setSelectedAlert(null);
  };

  const calculateTriggerPrice = (value, type, trend) => {
    if (type === 'Price') return value;
    
    const percentage = parseFloat(value);
    const multiplier = trend === 'Up' ? (1 + percentage/100) : (1 - percentage/100);
    return (ltp * multiplier).toFixed(2);
  };

  const handleAddAlert = () => {
    if (!pmlId || !ltp) return;

    let value, tp;
    
    if (isCustom) {
      value = alertValue;
      tp = calculateTriggerPrice(value, alertType, trend);
    } else if (selectedAlert) {
      value = selectedAlert.value;
      tp = calculateTriggerPrice(Math.abs(value), '%', value > 0 ? 'Up' : 'Down');
    } else {
      return;
    }

    const queryParams = {
      id: pmlId,
      tp,
      ltp,
    };

    if (editingAlert) {
      queryParams.doc_id = editingAlert.id;
    }

    addPriceAlert({ queryParams });
  };

  const handleEditAlert = (alert) => {
    setIsDrawerOpen(false);
    setEditingAlert(alert);
    setIsCustom(true);
    setAlertType('Price');
    setAlertValue(alert.trigger_price.toString());
    
    const trend = Number(alert.trigger_price) > Number(alert.ltp) ? 'Up' : 'Down';
    setTrend(trend);
  };

  const handleDeleteAlert = (alert) => {
    if (!alert?.id) return;
    setIsLoadingAction(true)
    deletePriceAlert({ 
      doc_id: alert.id 
    });
  };

  const handleCreateNew = () => {
    setIsDrawerOpen(false);
    setIsCustom(true);
    setAlertType('%');
    setTrend('Up');
    setAlertValue('');
    setEditingAlert(null);
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.predefinedContainer}>
          <View style={styles.header}>
            <PmlLogo/>
            <Text style={styles.headerText}>1-Click Price Alert</Text>
          </View>

          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.alertButtonsScroll}
          >
            <View style={styles.alertButtons}>
              {predefinedAlerts.map((alert) => (
                <TouchableOpacity
                  key={alert.value}
                  style={[
                    styles.alertButton,
                    selectedAlert?.value === alert.value && styles[alert.color],
                  ]}
                  onPress={() => handlePredefinedClick(alert)}
                >
                  <Text style={[
                    styles.alertButtonText,
                    selectedAlert?.value !== alert.value && styles[`text_${alert.color}`]
                    ]}>
                    {alert.value > 0 ? `+${alert.value}%` : `${alert.value}%`}
                  </Text>
                </TouchableOpacity>
              ))}
              <TouchableOpacity 
                style={[
                  styles.customButton,
                  (isCustom || selectedAlert) && styles.activeCustom
                ]} 
                onPress={handleCustomClick}
              >
                <Text style={[styles.customButtonText,(isCustom || selectedAlert) && styles.activeCustomText]}>
                  {(selectedAlert && !isCustom) ? 'Set' : 'Custom'}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
        {isCustom && (
          <View style={styles.customContainer}>
            <View style={styles.toggleContainer}>
              <ToggleSwitch
                options={['%', 'Price']}
                selected={alertType}
                onChange={setAlertType}
              />
              <ToggleSwitch
                options={['Up', 'Down']}
                selected={trend}
                onChange={setTrend}
                disabled={alertType === 'Price' || !!editingAlert}
                showIcons
              />
            </View>

            <InputSection
              value={alertValue}
              onChange={setAlertValue}
              onAdd={handleAddAlert}
              isLoading={isLoading}
              isEditing={!!editingAlert}
            />
          </View>
        )}
        {alertData?.length > 0 && (
          <View style={styles.manageAlerts}>
            <View style={styles.activeAlertsContainer}>
              <Text style={styles.activeAlertCount}>{alertData.length}</Text>
              <Text style={styles.activeAlertsText}>Active Alerts</Text>
            </View>
            <TouchableOpacity 
              style={styles.manageButton} 
              onPress={() => setIsDrawerOpen(true)}
            >
              <Text style={styles.manageButtonText}>Manage Alerts</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {isDrawerOpen ? <Drawer
        isOpen={isDrawerOpen}
        title="Active Alerts"
        onClose={() => setIsDrawerOpen(false)}
        showHandleBar
      >
        <ManageAlertsDrawer 
          alerts={alertData}
          onEdit={handleEditAlert}
          onDelete={handleDeleteAlert}
          isLoading={isDeleting || isFetching || isLoadingAction}
          onCreateNew={handleCreateNew}
        />
      </Drawer>:null}
    </>
  );
};

export default PriceAlert; 