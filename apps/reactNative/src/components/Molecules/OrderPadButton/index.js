import React from 'react';
import Button from '../../atoms/Button/Button';
import { styles } from './styles';

const OrderPadButton = ({ onClickHandler, isPrimary, buttonText, ...rest }) => {
  if (isPrimary) {
    return (
      <Button
        buttonClassName={styles.primaryButton}
        isPrimaryBlue
        buttonText={buttonText}
        onClickHandler={onClickHandler}
        {...rest}
      />
    );
  }
  return (
    <Button
      buttonText={buttonText}
      onClickHandler={onClickHandler}
      buttonClassName={styles.secondaryButton}
      {...rest}
    />
  );
};

export default OrderPadButton;
