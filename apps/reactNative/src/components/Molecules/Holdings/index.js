import React from 'react';
import { View, Text } from 'react-native';
import Accordion from '../../atoms/Accordion/Accordion';
import RupeeCircle from '../../../assets/rupeeCircle.svg';
import { styles } from './styles';

// Mock data
const MOCK_DATA = {
  holdingWeightage: 67,
  investments: 540000,
  currentValue: 620232.04,
  overallReturns: {
    value: 200.04,
    percentage: 26.15
  },
  todayReturns: {
    value: 20.02,
    percentage: 6.11
  },
  transactions: [
    {
      investment: '3.12 Cr',
      date: '2 Feb 2024',
      shares: 12,
      avgPrice: '1,256.85',
      returns: 30.85,
      type: 'B'
    },
    {
      investment: '1,55,000',
      date: '30 Jan 2024',
      shares: 26,
      avgPrice: '1,256.85',
      returns: 18.85,
      type: 'S'
    },
    {
      investment: '55,000',
      date: '28 Jan 2024',
      shares: 14,
      avgPrice: '1,070.85',
      returns: 18.85,
      type: 'B'
    }
  ]
};

const Holdings = () => {
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.weightageContainer}>
        <RupeeCircle width={16} height={16} />
        <Text style={styles.weightageText}>
          Holding Weightage: {MOCK_DATA.holdingWeightage}%
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statBlock}>
          <Text style={styles.statLabel}>Investments</Text>
          <Text style={styles.statValue}>₹{MOCK_DATA.investments.toLocaleString()}</Text>
        </View>
        <View style={styles.statBlock}>
          <Text style={styles.statLabel}>Current Value</Text>
          <Text style={[styles.statValue, styles.greenText]}>
            ₹{MOCK_DATA.currentValue.toLocaleString()}
          </Text>
        </View>
      </View>

      <View style={styles.returnsContainer}>
        <View style={styles.returnBlock}>
          <Text style={styles.returnLabel}>Overall Returns</Text>
          <View style={styles.returnValueContainer}>
            <Text style={[styles.returnValue, styles.greenText]}>
              ₹{MOCK_DATA.overallReturns.value}
            </Text>
            <Text style={[styles.returnPercentage, styles.greenText]}>
              (+{MOCK_DATA.overallReturns.percentage}%)
            </Text>
          </View>
        </View>
        <View style={styles.returnBlock}>
          <Text style={styles.returnLabel}>Today's Returns</Text>
          <View style={styles.returnValueContainer}>
            <Text style={[styles.returnValue, styles.greenText]}>
              ₹{MOCK_DATA.todayReturns.value}
            </Text>
            <Text style={[styles.returnPercentage, styles.greenText]}>
              (+{MOCK_DATA.todayReturns.percentage}%)
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderBreakupContent = () => (
    <View style={styles.breakupContainer}>
      <View style={styles.breakupTitleContainer}>
        <Text style={styles.breakupTitle}>Holding Break-up</Text>
        <Text style={styles.viewAllText}>View All</Text>
      </View>

      <View style={styles.breakupHeader}>
        <Text style={[styles.columnHeader, styles.columnHeaderLeft]}>Investments</Text>
        <Text style={[styles.columnHeader, styles.columnHeaderCenter]}>Share × Avg. Price</Text>
        <Text style={[styles.columnHeader, styles.columnHeaderRight]}>Returns</Text>
      </View>
      {MOCK_DATA.transactions.map((transaction, index) => (
        <View key={index} style={[
          styles.transactionRow,
          index !== MOCK_DATA.transactions.length - 1 && styles.borderBottom
        ]}>
          <View style={styles.investmentColumn}>
            <Text style={styles.investmentValue}>{transaction.investment}</Text>
            <View style={styles.dateContainer}>
              <Text style={styles.dateText}>{transaction.date}</Text>
              <View style={[
                styles.typeIndicator,
                { backgroundColor: transaction.type === 'B' ? '#E3F6EC' : '#FFEBEF' }
              ]}>
                <Text style={[
                  styles.typeText,
                  { color: transaction.type === 'B' ? '#21C179' : '#FD5154' }
                ]}>
                  {transaction.type}
                </Text>
              </View>
            </View>
          </View>
          <Text style={styles.sharesText}>
            {transaction.shares} × {transaction.avgPrice}
          </Text>
          <Text style={[
            styles.returnsText,
            { color: transaction.returns >= 0 ? '#21C179' : '#FD5154' }
          ]}>
            {transaction.returns}%
          </Text>
        </View>
      ))}
    </View>
  );

  return (
    <Accordion
      title="Your Holdings"
      isOpen={true}
    >
      {renderHeader()}
      <View style={styles.divider} />
      {renderBreakupContent()}
    </Accordion>
  );
};

export default Holdings;
