import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  headerContainer: {
    padding: 16,
    paddingTop:0,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#101010',
    marginBottom: 8,
  },
  weightageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  weightageText: {
    fontSize: 14,
    color: '#101010B2',
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statBlock: {
    flex: 1,
  },
  statLabel: {
    fontSize: 14,
    color: '#101010B2',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '600',
    color: '#101010',
  },
  returnsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  returnBlock: {
    flex: 1,
  },
  returnLabel: {
    fontSize: 14,
    color: '#101010B2',
    marginBottom: 4,
  },
  returnValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  returnValue: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 4,
  },
  returnPercentage: {
    fontSize: 14,
  },
  greenText: {
    color: '#21C179',
  },
  breakupContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  breakupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  columnHeader: {
    fontSize: 12,
    color: '#101010B2',
  },
  columnHeaderLeft: {
    flex: 1,
  },
  columnHeaderCenter: {
    flex: 1,
    textAlign: 'center',
  },
  columnHeaderRight: {
    width: 80,
    textAlign: 'right',
  },
  transactionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: '#E7F1F8',
  },
  investmentColumn: {
    flex: 1,
  },
  investmentValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#101010',
    marginBottom: 4,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#101010B2',
    marginRight: 8,
  },
  typeIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  typeText: {
    fontSize: 10,
    fontWeight: '500',
  },
  sharesText: {
    flex: 1,
    fontSize: 14,
    color: '#101010',
    textAlign: 'center',
  },
  returnsText: {
    width: 80,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'right',
  },
  divider: {
    height: 1,
    backgroundColor: '#E7F1F8',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  breakupTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  breakupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#101010',
  },
  viewAllText: {
    fontSize: 14,
    color: '#013DA6', // Link blue color
  },
});