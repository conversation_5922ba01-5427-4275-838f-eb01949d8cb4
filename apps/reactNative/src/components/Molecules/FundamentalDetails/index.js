import React from 'react';
import { useFundamentals } from "@paytm-money/store";
import TableData from "../../atoms/TableData/TableData";
import { FUNDAMENTAL_DETAILS_CONSTANTS as CONSTANTS } from './constants';

const FundamentalDetails = ({ pmlId }) => {
    const { fundamentalsData, isLoading, isError, refetch } = useFundamentals(pmlId);

    return (
        <TableData 
            data={fundamentalsData}
            title="Details"
            isLoading={isLoading}
            isError={isError}
            onRetry={refetch}
            isEmpty={!fundamentalsData?.length}
            errorTitle={CONSTANTS.ERROR.TITLE}
            errorDescription={CONSTANTS.ERROR.DESCRIPTION}
            emptyTitle={CONSTANTS.EMPTY.TITLE}
            emptyDescription={CONSTANTS.EMPTY.DESCRIPTION}
        />
    );
};

export default FundamentalDetails;