import React from 'react'
import Accordion from '../../atoms/Accordion/Accordion';
import QuickSnapshot from '../../atoms/QuickSnapshot';

import usePerformanceData, {
  getFormattedDataForCard,
} from "../../../hooks/usePerformanceData"

const PerformanceOveview = ({ exchange, securityId, segment, expiryDate }) => {
  const { ohlcData, limitData, weekPerformanceData } = usePerformanceData({
    exchange,
    securityId,
    segment,
  });

  const formattedData = getFormattedDataForCard(
    {
      ohlcData,
      limitData,
      weekPerformanceData,
      expiryDate,
    },
    segment,
  );

  return (
    <Accordion
      title="Quick Snapshot"
      isOpen={true}
    >
      <QuickSnapshot
        data={{
          open: formattedData["Open"],
          high: formattedData["Today's High"],
          low: formattedData["Today's Low"],
          close: formattedData["Prev. Close"],
          yearLow: formattedData["52-wk_Low"],
          yearHigh: formattedData["52-wk_High"],
          lowerCircuit: formattedData["Lower Circuit"],
          upperCircuit: formattedData["Upper Circuit"],
        }}
        maxValue={weekPerformanceData?.yearHigh}
        minValue={weekPerformanceData?.yearLow}
        currentValue={weekPerformanceData?.ltp}
      />
    </Accordion>
  )
}

export default PerformanceOveview;
