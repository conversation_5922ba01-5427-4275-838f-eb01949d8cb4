import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  tabsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 100,
    backgroundColor: '#F5F9FF',
  },
  activeTab: {
    backgroundColor: '#101010',
  },
  tabText: {
    ...typography.text,
    color: '#101010',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  barsContainer: {
    gap: 16,
  },
  patternContainer: {
    marginTop: 16,
    gap: 16,
  },
  progressItem: {
    gap: 8,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    color: '#101010',
  },
  value: {
    fontSize: 14,
    color: '#101010',
    fontWeight: '500',
  },
  tabsShimmer: {
    flexDirection: 'row',
    gap: 8,
  },
  tabShimmerItem: {
    flex: 1,
    height: 32,
    backgroundColor: '#1010100F',
    borderRadius: 40,
  },
  barShimmer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  labelShimmer: {
    width: '70%',
    height: 24,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  valueShimmer: {
    width: '25%',
    height: 24,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  }
}); 