import React, { useEffect, useState } from 'react';
import { View, Text } from 'react-native';
import { useShareHoldingPattern } from "@paytm-money/store";
import ComparisonBar from '../../atoms/ComparisonBar/ComparisonBar';
import FlatTabs from '../../atoms/FlatTabs/FlatTabs';
import ErrorState from '../../atoms/ErrorState';
import { styles } from './styles';
import { SHAREHOLDING_PATTERN_CONSTANTS as CONSTANTS } from './constants';

const LoadingShimmer = () => {
  return (
    <>
      <View style={styles.tabsShimmer}>
        {[...Array(CONSTANTS.LOADING.TAB_COUNT)].map((_,i) => (
          <View key={i} style={styles.tabShimmerItem} />
        ))}
      </View>
      {[...Array(CONSTANTS.LOADING.BAR_COUNT)].map((_,i) => (
        <View key={i} style={styles.barShimmer}>
          <View style={styles.labelShimmer} />
          <View style={styles.valueShimmer} />
        </View>
      ))}
    </>
  );
};

const ShareholdingPattern = ({ pmlId }) => {
  const { shareholdingData, tabs, isLoading, isError, refetch } = useShareHoldingPattern(pmlId);
  const [activeTab, setActiveTab] = useState();

  useEffect(() => {
    if(tabs?.length) {
      setActiveTab(tabs[0])
    }
  }, [tabs])

  const getComponents = () => {
    if (isLoading) return <LoadingShimmer />;
    if (isError) return (
      <ErrorState 
        title={CONSTANTS.ERROR.TITLE}
        description={CONSTANTS.ERROR.DESCRIPTION}
        isRetry 
        onRetry={refetch}
      />
    );
    if (!tabs.length) return (
      <ErrorState 
        title={CONSTANTS.EMPTY.TITLE}
        description={CONSTANTS.EMPTY.DESCRIPTION}
      />
    );

    return (
      <>
        <FlatTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={setActiveTab}
        />
        <View style={styles.barsContainer}>
          {shareholdingData[activeTab]?.map((item, index) => (
            <ComparisonBar
              key={index}
              label={item.label}
              value={item.value}
              maxValue={CONSTANTS.CHART.MAX_VALUE}
              isPercentage
            />
          ))}
        </View>
      </>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Shareholding Pattern</Text>
      {getComponents()}
    </View>
  );
};

export default ShareholdingPattern;