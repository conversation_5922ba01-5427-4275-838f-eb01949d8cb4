import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    // marginBottom removed from here
  },
  container: {
    width: "100%",
    height: 190,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E8E1E1',
    flexDirection: "column",
    justifyContent: 'space-between',
    zIndex: 2,
    elevation: 2,
  },
  content: {
    padding: 16,
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 8,
  },
  detailsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameContainer: {
    gap: 4,
  },
  name: {
    ...typography.text,
    color: '#101010',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 4,
  },
  price: {
    ...typography.heading14,
    color: '#101010',
  },
  change: {
    ...typography.body2,
  },
  positive: {
    color: '#21C179',
  },
  negative: {
    color: '#FD5154',
  },
  graphContainer: {
    width: '100%',
    height: 50,
  },
  bottomTip: {
    position: 'absolute',
    top: 180,
    left: 0,
    height: 40,
    backgroundColor: '#ECF2F8',
    paddingHorizontal: 12,
    paddingTop: 16,
    paddingBottom: 8,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    width: "100%",
    zIndex: 1,
    elevation: 1,
  },
  bottomTipText: {
    ...typography.caption2R,
    color: '#101010',
  }
});

