import TableData from "../../atoms/TableData/TableData";
import { CONSTANTS } from './constants';

const MovingAverages = ({ data, isLoading, isError, refetch }) => <TableData
    isLoading={isLoading}
    data={data}
    isError={isError}
    isEmpty={!data?.length}
    title="Moving Averages"
    onRetry={refetch}
    errorTitle={CONSTANTS.ERROR.TITLE}
    errorDescription={CONSTANTS.ERROR.DESCRIPTION}
    emptyTitle={CONSTANTS.EMPTY.TITLE}
    emptyDescription={CONSTANTS.EMPTY.DESCRIPTION}
    showInfoIcon
/>


export default MovingAverages