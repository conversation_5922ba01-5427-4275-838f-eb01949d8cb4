import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical:8,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  content:{
    padding: 16,
    paddingBottom:0
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom:16
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  info: {
    ...typography.text,
    color: '#101010',
  },
  overallContainer: {
    backgroundColor: '#F5F9FF',
    borderRadius: 12,
    padding: 16,
    height:100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  overallShimmerContainer:{
    height:72,
    backgroundColor:'#1010100F',
    borderRadius:8,
    margin:16
  },
  scoreContent: {
    flex: 1,
    gap: 8,
  },
  overallLabel: {
    ...typography.text,
    color: '#101010',
  },
  overallScore: {
    ...typography.heading1B1,
    color: '#101010',
  },
  totalScore:{
    ...typography.heading2B1,
    color: '#101010',
  },
  targetImage: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
  },
  scoresList: {
    gap: 16,
    marginTop:16
  },
  scoreItem: {
    gap: 8,
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center'
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    ...typography.text,
    color: '#101010',
    width:140
  },
  score: {
    ...typography.text,
    color: '#101010',
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#D8E7F7',
    borderRadius: 100,
    overflow: 'hidden',
    width:"40%"
  },
  progressBar: {
    height: '100%',
    borderRadius: 100,
  },
  footer: {
    paddingHorizontal:12,
    paddingVertical:7,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor:'#F5F0F0',
    borderEndEndRadius:12,
    borderEndStartRadius:12,
    gap: 8,
  },
  refinitivLogo: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  footerText: {
    ...typography.caption,
    color: '#101010B2',
    flex: 1,
  },

  labelShimmer: {
    height: 16,
    width: '30%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  progressBarShimmer: {
    height: 8,
    width: '35%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  scoreShimmer: {
    height: 16,
    width: '15%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  footerTextShimmer: {
    height: 16,
    width: '60%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
    flex: 1,
  },
}); 