import React from 'react';
import { View, Text, ImageBackground } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ScoreCardBg from "../../../assets/ScoreCardBg.png"
import InfoIcon from "../../../assets/Info_icon.svg"
import TestingImage from "../../../assets/testing.svg"
import ErrorState from '../../atoms/ErrorState';
import { SCORES_CONSTANTS as CONSTANTS } from './constants';
import { styles } from './styles';

const ScoreBar = ({ label, score, maxScore = 10 }) => {
  const progressWidth = `${(score / maxScore) * 100}%`;

  return (
    <View style={styles.scoreItem}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.progressBarBackground}>
        <LinearGradient
          colors={['#00B8F5', '#D3D4FF']}
          style={[styles.progressBar, { width: progressWidth }]}
          end={{ x: 0, y: 0 }}
          start={{ x: 1, y: 0 }}
        />
      </View>
      <Text style={styles.score}>{score}/10</Text>
    </View>
  );
};

const OverallScore = ({ score, companyName }) => (
  <ImageBackground 
    source={ScoreCardBg}
    style={styles.overallContainer}
    resizeMode="cover"
  >
    <View style={styles.scoreContent}>
      <Text style={styles.overallLabel}>Overall Score for {companyName}</Text>
      <View style={styles.scoreContainer}>
        <Text style={styles.overallScore}>{score}<Text style={styles.totalScore}>/10</Text></Text>
      </View>
    </View>
  </ImageBackground>
);

const Header = () => (
  <View style={styles.header}>
    <Text style={styles.title}>Scores</Text>
    <InfoIcon />
  </View>
);

const LoadingShimmer = () => (
  <View style={styles.scoresList}>
    {[...Array(4)].map((_, index) => (
      <View key={index} style={styles.scoreItem}>
        <View style={styles.labelShimmer} />
        <View style={styles.progressBarShimmer} />
        <View style={styles.scoreShimmer} />
      </View>
    ))}
  </View>
);

const Footer = () => (
  <View style={styles.footer}>
    <TestingImage />
    <Text style={styles.footerText}>Aggregated by Refinitiv from 25 analysts</Text>
    <InfoIcon />
  </View>
);

const Scores = ({ scores, isLoading, overAllScore, isError, refetch ,companyName}) => {
  const renderContent = () => {
    if (isLoading) {
      return (
        <>
          <View style={styles.overallShimmerContainer} />
          <LoadingShimmer />
        </>
      );
    }

    if (isError) {
      return (
        <ErrorState
          title={CONSTANTS.ERROR.TITLE}
          description={CONSTANTS.ERROR.DESCRIPTION}
          isRetry
          onRetry={refetch}
        />
      );
    }

    if (!scores?.length) {
      return (
        <ErrorState
          title={CONSTANTS.EMPTY.TITLE}
          description={CONSTANTS.EMPTY.DESCRIPTION}
        />
      );
    }

    return (
      <>
        <OverallScore score={overAllScore} companyName={companyName} />
        <View style={styles.scoresList}>
          {scores?.map((item, index) => (
            <ScoreBar
              key={index}
              label={item.label}
              score={item.value}
            />
          ))}
        </View>
      </>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Header />
        {renderContent()}
      </View>
      {!isError && !isLoading && scores?.length > 0 && <Footer />}
    </View>
  );
};

export default Scores; 