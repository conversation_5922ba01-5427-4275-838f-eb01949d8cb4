import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 8,
  },
  title: {
    ...typography.heading4B1,
    color: '#101010',
  },
  description: {
    ...typography.heading14,
    color: '#101010B2',
  },
  readMore: {
    ...typography.heading14,
    color: '#1576DB',
  },
  drawerContent: {
    paddingHorizontal: 16,
    gap: 16,
  },
  drawerText: {
    ...typography.body2,
    color: '#101010',
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
    borderWidth:1,
    borderColor:'#10101021'
  },
  tagText: {
    ...typography.body3B2,
    color: '#101010B2',
  },
  customHeaderStyle:{
    paddingBottom:0
  },
  headerContainer:{
    flexDirection:'row',
    gap:5,
    alignItems:"center"
  },
  companyName:{
    ...typography.heading2B1,
    color: '#101010',
  },
  descriptionShimmer: {
    height: 16,
    width: '100%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
    marginBottom: 8,
  },
  tagContainerShimmer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  tagShimmer: {
    height: 24,
    width: 120,
    backgroundColor: '#1010100F',
    borderRadius: 16,
  },
}); 