import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useDrawer } from '../Drawer/useDrawer';
import Drawer from '../Drawer';
import { styles } from './styles';
import CompanyIcon from '../../atoms/CompanyIcon/CompanyIcon';
import { useFundamentals } from "@paytm-money/store";
import ErrorState from '../../atoms/ErrorState';
import { ABOUT_COMPANY_CONSTANTS as CONSTANTS } from './constants';

const CHAR_LIMIT = 250;

const LoadingShimmer = () => {
  return (
    <>
      <View style={styles.descriptionShimmer} />
      <View style={styles.descriptionShimmer} />
      <View style={[styles.descriptionShimmer, { width: '70%' }]} />

      <View style={styles.tagContainerShimmer}>
        <View style={styles.tagShimmer} />
        <View style={styles.tagShimmer} />
        <View style={styles.tagShimmer} />
      </View>
    </>
  );
};

const AboutCompany = ({ pmlId }) => {
  const { isOpen, onOpen, onClose } = useDrawer();
  const { companyDescription, companyName, isLoading, isError, refetch, tags } = useFundamentals(pmlId);

  const getBottomTags = () => (
    <View style={styles.tagContainer}>
      {tags.map(tagText => <View key={tagText} style={styles.tag}>
        <Text style={styles.tagText}>{tagText}</Text>
      </View>)}
    </View>
  );

  const renderContent = () => {
    if (isLoading) return <LoadingShimmer />;

    if (isError) {
      return (
        <ErrorState
          title={CONSTANTS.ERROR.TITLE}
          description={CONSTANTS.ERROR.DESCRIPTION}
          ErrorIcon={CONSTANTS.ERROR.IMAGE}
          isRetry
          onRetry={refetch}
        />
      );
    }

    if (!companyDescription) return (
      <ErrorState
        title={CONSTANTS.EMPTY.TITLE}
        description={CONSTANTS.EMPTY.DESCRIPTION}
        ErrorIcon={CONSTANTS.EMPTY.IMAGE}
      />
    );;

    const shouldShowReadMore = companyDescription?.length > CHAR_LIMIT;
    const truncatedDescription = shouldShowReadMore
      ? `${companyDescription.slice(0, CHAR_LIMIT)}...`
      : companyDescription;

    return (
      <>
        <Text style={styles.description}>{truncatedDescription}</Text>
        {shouldShowReadMore && (
          <TouchableOpacity onPress={onOpen}>
            <Text style={styles.readMore}>Read more</Text>
          </TouchableOpacity>
        )}
        {tags?.length ? getBottomTags() : null}
      </>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>About {companyName}</Text>
      {renderContent()}
      <Drawer
        isOpen={isOpen}
        onClose={onClose}
        customHeaderStyle={styles.customHeaderStyle}
      >
        <View style={styles.drawerContent}>
          <View style={styles.headerContainer}>
            <CompanyIcon name={pmlId} type="stocks" />
            <Text style={styles.companyName}>{companyName}</Text>
          </View>
          {getBottomTags()}
          <Text style={styles.description}>{companyDescription}</Text>
        </View>
      </Drawer>
    </View>
  );
};

export default AboutCompany; 