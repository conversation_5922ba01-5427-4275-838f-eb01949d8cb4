import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical:8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  info: {
    ...typography.text,
    color: '#101010',
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  typeText: {
    ...typography.text,
    color: '#1576DB',
  },
  dropdownIcon: {
    color: '#1576DB',
    fontSize: 16,
  },
  contentContainer: {
    flexDirection: 'row',
    // alignItems: 'flex-start',
    position: 'relative',
    minHeight: 250,
  },
  pivotList: {
    marginVertical:16,
    flex: 1,
    gap: 16,
    width:"50%"
  },
  pivotItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    height: 22,
    width: 200,
  },
  label: {
    ...typography.body2,
    color: '#101010B2',
    width: 30,
    height:22
  },
  pivotLabel: {
    ...typography.body2B1,
    color: '#101010',
  },
  dotLineContainer: {
    width: 100,
    height: 22,
    justifyContent: 'center'
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  dot: {
    width: 4,
    height: 1,
    borderRadius: 1,
    backgroundColor: '#10101021',
  },
  pivotDot: {
    backgroundColor: '#1576DB',
  },
  value: {
    ...typography.text2,
    color: '#101010B2',
    minWidth: 30,
    height:22,
    textAlign: 'left',
  },
  pivotValue: {
    ...typography.heading4B1,
    color: '#101010',
  },
  liveWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    right: 0,
    zIndex: 1,
    width:"50%"
  },
  liveDottedLine: {
    width: 40,
    height: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginRight: 4,
  },
  liveDot: {
    width: 2,
    height: 1,
    backgroundColor: '#1576DB',
  },
  liveContainer: {
    backgroundColor: '#1576DB',
    borderRadius: 6,
    paddingVertical: 4,
    paddingHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  liveLabel: {
    ...typography.caption,
    color: '#FFFFFF',
  },
  liveValue: {
    ...typography.text,
    color: '#FFFFFF',
  },
  liveContainerTop: {
    top: 0,
  },
  liveContainerBottom: {
    bottom: 0,
  },
}); 