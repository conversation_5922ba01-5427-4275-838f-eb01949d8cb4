import React from 'react';
import { useCompanyPivot } from "@paytm-money/store"
import { View, Text, TouchableOpacity } from 'react-native';
import InfoIcon from "../../../assets/Info_icon.svg"
import ToggleVertical from "../../../assets/toggleVertical.svg"
import { styles } from './styles';

const DottedLine = ({ isPivot }) => (
  <View style={styles.dotLineContainer}>
    <View style={styles.dotsContainer}>
      {[...Array(15)].map((_, index) => (
        <View 
          key={index} 
          style={[
            styles.dot, 
            isPivot && styles.pivotDot
          ]} 
        />
      ))}
    </View>
  </View>
);

const PivotItem = ({ label, value, isPivot }) => (
  <View style={styles.pivotItem}>
    <Text style={[styles.label, isPivot && styles.pivotLabel]}>
      {isPivot ? 'Pivot' : label}
    </Text>
    <DottedLine isPivot={isPivot} />
    <Text style={[styles.value, isPivot && styles.pivotValue]}>{value}</Text>
  </View>
);

const LivePrice = ({ value, pivotValues }) => {
  // Find position based on value relative to pivot values
  const getPosition = () => {
    const sortedValues = pivotValues.map(item => Number(item.value)).sort((a, b) => b - a);
    const currentValue = Number(value);
    
    // Find the first value that's less than current price
    const index = sortedValues.findIndex(v => currentValue > v);
    
    if (index === -1) {
      // Price is lower than all values - position at bottom
      return styles.liveContainerBottom;
    } else if (index === 0) {
      // Price is higher than all values - position at top
      return styles.liveContainerTop;
    }

    // Get the values above and below current price
    const upperValue = sortedValues[index - 1];
    const lowerValue = sortedValues[index];
    
    // Calculate position based on the gap between values
    const totalGap = upperValue - lowerValue;
    const currentGap = currentValue - lowerValue;
    const gapPercentage = (currentGap / totalGap);
    
    // Calculate position between the two pivot points
    const sectionHeight = 250 / (sortedValues.length - 1); // Total height divided by number of gaps
    const basePosition = (index * sectionHeight); // Position of lower value
    const adjustedPosition = basePosition - (gapPercentage * sectionHeight);

    return {
      position: 'absolute',
      right: 0,
      left:210,
      top: adjustedPosition
    };
  };

  return (
    <View style={[styles.liveWrapper, getPosition()]}>
      <View style={styles.liveDottedLine}>
        {[...Array(8)].map((_, index) => (
          <View 
            key={index} 
            style={styles.liveDot}
          />
        ))}
      </View>
      <View style={styles.liveContainer}>
        <Text style={styles.liveLabel}>Live</Text>
        <Text style={styles.liveValue}>₹{value}</Text>
      </View>
    </View>
  );
};

const PivotPoints = ({pmlId}) => {
  const {
    pivotTypes,
    selectedPivotType,
    setSelectedPivotType,
    data,
    isLoading,
    isError,
    refetch,
    lastUpdatedAt
  } = useCompanyPivot(pmlId);

  if(isLoading) return null

  const handleTypeToggle = () => {
    setSelectedPivotType(prevType => 
      prevType === pivotTypes[0] ? pivotTypes[1] : pivotTypes[0]
    );
  };

  const ltp= 310

  const pivotData = data.map(item => ({
    label: item.key,
    value: item.value.toFixed(2),
    isPivot: item.key === 'PIVOT POINT'
  }));

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Pivot</Text>
          <InfoIcon />
        </View>
        <TouchableOpacity 
          style={styles.typeButton}
          onPress={handleTypeToggle}
        >
          <Text style={styles.typeText}>{selectedPivotType}</Text>
         <ToggleVertical/>
        </TouchableOpacity>
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.pivotList}>
          {pivotData.map((item, index) => (
            <PivotItem
              key={index}
              label={item.label}
              value={item.value}
              isPivot={item.isPivot}
            />
          ))}
        </View>
        <LivePrice value={ltp} pivotValues={pivotData} />
      </View>
    </View>
  );
};

export default PivotPoints; 