import React, { useRef } from "react";
import { View, Animated, PanResponder, ActivityIndicator } from "react-native";
import RightIcon from "../../../assets/rightIcon.svg"
import { 
  styles, 
  SWIPE_THRESHOLD, 
  COMPLETION_THRESHOLD, 
  SWIPEABLE_WIDTH, 
  BUTTON_WIDTH 
} from './styles';

const SwipeButton = ({ onComplete, isLoading = false }) => {
  const translateX = useRef(new Animated.Value(0)).current;

  // Create interpolated values for text opacity and position
  const textOpacity = translateX.interpolate({
    inputRange: [0, SWIPE_THRESHOLD / 2],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  // Update text position to start after the icon
  const textTranslateX = translateX.interpolate({
    inputRange: [0, SWIPE_THRESHOLD],
    outputRange: [SWIPEABLE_WIDTH + 10, BUTTON_WIDTH / 2],
    extrapolate: 'clamp',
  });

  const completeSwipe = () => {
    Animated.timing(translateX, {
      toValue: SWIPE_THRESHOLD,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      if (onComplete) onComplete();
    });
  };

  const resetSwipe = () => {
    Animated.timing(translateX, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !isLoading,
      onMoveShouldSetPanResponder: () => !isLoading,
      onPanResponderMove: (event, gestureState) => {
        if (gestureState.dx >= 0 && gestureState.dx <= SWIPE_THRESHOLD) {
          translateX.setValue(gestureState.dx);
        }
      },
      onPanResponderRelease: (event, gestureState) => {
        if (gestureState.dx > COMPLETION_THRESHOLD) {
          completeSwipe();
        } else {
          resetSwipe();
        }
      },
    })
  ).current;

  return (
    <View style={[
      styles.container,
      isLoading && styles.containerDisabled
    ]}>
      <Animated.View
        style={[styles.swipeable, { transform: [{ translateX }] }]}
        {...panResponder.panHandlers}
      >
        {isLoading ? (
          <ActivityIndicator color="#29A36E" size="small" />
        ) : (
          <RightIcon
            height={25}
            width={25}
          />
        )}
      </Animated.View>
      <Animated.Text 
        style={[
          styles.text, 
          { 
            opacity: textOpacity,
            transform: [{ translateX: textTranslateX }],
            left: 0,
          }
        ]}
      >
        Swipe to Buy using Pay Later
      </Animated.Text>
    </View>
  );
};

export default SwipeButton;