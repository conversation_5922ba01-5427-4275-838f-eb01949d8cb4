import { StyleSheet } from 'react-native';

export const BUTTON_WIDTH = 280;
export const SWIPEABLE_WIDTH = 60;
export const SWIPE_THRESHOLD = BUTTON_WIDTH - SWIPEABLE_WIDTH - 10;
export const COMPLETION_THRESHOLD = BUTTON_WIDTH * 0.4;

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#29A36E",
    width: BUTTON_WIDTH,
    height: 56,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
  },
  containerDisabled: {
    opacity: 0.7,
  },
  text: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
    position: "absolute",
    zIndex: 0,
    textAlign: "left",
  },
  swipeable: {
    width: SWIPEABLE_WIDTH,
    height: 48,
    borderRadius: 30,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    left: 5,
    zIndex: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  track: {
    height: 56,
    backgroundColor: '#00B8F5',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  trackText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  slider: {
    position: 'absolute',
    left: 4,
    top: 4,
    bottom: 4,
    width: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    width: 24,
    height: 24,
    tintColor: '#00B8F5',
  },
});