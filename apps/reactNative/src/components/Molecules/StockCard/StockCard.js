import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import CompanyIcon from "../../atoms/CompanyIcon/CompanyIcon";
import { ICONS_NAME } from '../../atoms/CompanyIcon/IconList';

import NegativeGraph from "../../../assets/negativeGraph.svg"
import PositiveGraph from "../../../assets/positiveGraph.svg"
import { styles } from './styles';

const StockCard = ({
    id,
    name = "",
    price = "15.63",
    one_year_return :changePercentage,
    type = ICONS_NAME.STOCKS,
    onPress,
    bottomTip,
}) => {
    const isNegative = changePercentage < 0;

    return (
        <View style={[
            styles.wrapper,
            bottomTip ? { marginBottom: 30 } : null
        ]}>
            {bottomTip && (
                <View style={styles.bottomTip}>
                    <Text style={styles.bottomTipText}>{bottomTip}</Text>
                </View>
            )}
            <TouchableOpacity
                style={styles.container}
                onPress={onPress}
                activeOpacity={0.7}
            >
                <View style={styles.content}>
                    <CompanyIcon
                        name={id}
                        type={type}
                    />
                    <Text style={styles.name} numberOfLines={2}>{name}</Text>
                    <View style={styles.priceContainer}>
                        <Text style={styles.price}>₹{price}</Text>
                        <Text style={[
                            styles.change,
                            isNegative ? styles.negative : styles.positive
                        ]}>
                            {isNegative ? '' : '+'}
                            {changePercentage.toFixed(2)}%
                        </Text>
                    </View>
                </View>
                <View style={styles.graphContainer}>
                    {isNegative ? 
                        <NegativeGraph width="100%" height="100%" /> : 
                        <PositiveGraph width="100%" height="100%" />
                    }
                </View>
            </TouchableOpacity>
        </View>
    );
};

export default StockCard;