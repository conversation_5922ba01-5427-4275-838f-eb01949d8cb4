import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import FlatTabs from '../../atoms/FlatTabs/FlatTabs';
import StockCarousel from '../StockCarousel/StockCarousel';
import { useStockCarousel } from '../../../hooks/useStockCarousel';
import { styles } from './styles';

const OPTION_TYPES = ['Call', 'Put'];

const TopOptions = ({ symbol }) => {
  const navigation = useNavigation();
  const { data, activeType, setActiveType, isLoading } = useStockCarousel(symbol);

  console.log("----->>>>",data)
  const handleCardPress = (item) => {
    navigation.navigate('OrderPad', { 
      scripDetails: item,
      type: activeType 
    });
  };

  const renderOptionCard = (item) => ({
    ...item,
    title: item.name,
    subtitle: item.displayName,
    value: item.option_type,
    quantity: 12, // This should come from actual data
    trend: '+9.81%', // This should come from actual data
    chartData: [], // This should be actual chart data
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Top Options</Text>
        <TouchableOpacity>
          <Text style={styles.viewAll}>View All</Text>
        </TouchableOpacity>
      </View>

      <FlatTabs
        tabs={OPTION_TYPES}
        activeTab={activeType}
        onTabPress={setActiveType}
      />

      <StockCarousel
        data={data || []}
        onCardPress={handleCardPress}
        isFno
        activeType={activeType}
      />
    </View>
  );
};

export default TopOptions; 