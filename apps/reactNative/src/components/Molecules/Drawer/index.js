import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  PanResponder,
} from 'react-native';
import ModalLoader from '../../atoms/ModalLoader/ModalLoader';
import CloseIcon from "../../../assets/CloseCircle.svg"
import { styles } from './styles';

const SCREEN_HEIGHT = Dimensions.get('window').height;
const SCREEN_WIDTH = Dimensions.get('window').width;


function Drawer({
  toastMessage = false,
  showToast = false,
  customSnackbar = false,
  isLoading = false,
  children,
  isOpen,
  onClose,
  popup,
  title,
  showCross = true,
  showHandleBar = false,
  customTitleClassName,
  closeAllowed = true,
  noAnimation = false,
  customHeaderStyle
}) {
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [showModal, setShowModal] = useState(false);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) { // Only allow downward drag
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          // User dragged down far enough or with enough velocity - close drawer
          handleClose();
        } else {
          // Reset position
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  useEffect(() => {
    if (isOpen) {
      setShowModal(true);
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
          bounciness: 0,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isOpen]);

  const handleClose = () => {
    if (!closeAllowed) return;

    Animated.parallel([
      Animated.timing(translateY, {
        toValue: SCREEN_HEIGHT,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowModal(false);
      onClose();
    });
  };

  if (!showModal) return null;

  const drawerStyle = popup ? styles.popup : styles.drawer;

  return (
    <Modal
      visible={showModal}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        style={styles.modalContainer}
        onPress={closeAllowed ? handleClose : undefined}
      >
        <Animated.View
          style={[
            styles.backdrop,
            {
              opacity: fadeAnim,
            },
          ]}
        />
      </TouchableOpacity>

      <Animated.View
        style={[
          drawerStyle,
          {
            transform: [
              {
                translateY: translateY.interpolate({
                  inputRange: [0, SCREEN_HEIGHT],
                  outputRange: [0, SCREEN_HEIGHT],
                }),
              },
            ],
          },
        ]}
        {...(popup || showHandleBar ? {} : panResponder.panHandlers)}
      >
        {showHandleBar ? (
          <View style={styles.handleBarContainer}>
            <View style={styles.handleBar} />
          </View>
        ) : null}
        
        {(title || showCross) && (
          <View style={[styles.header, customHeaderStyle]}>
            <Text style={[styles.title, customTitleClassName]}>
              {!isLoading ? title : ''}
            </Text>
            {showCross && (
              <TouchableOpacity onPress={closeAllowed ? handleClose : undefined}>
                <CloseIcon width={24} height={24} style={styles.image} />
              </TouchableOpacity>
            )}
          </View>
        )}

        {showToast && (
          <View
            style={[
              styles.toastContainer,
              customSnackbar ? styles.successToast : styles.errorToast,
            ]}
          >
            <Text style={styles.toastText}>{toastMessage}</Text>
          </View>
        )}

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ModalLoader />
          </View>
        ) : (
          <View style={styles.content}>{children}</View>
        )}
      </Animated.View>
    </Modal>
  );
}

export default Drawer;
