import { useState, useCallback } from 'react';
import { BackHandler } from 'react-native';
import { useEffect } from 'react';

function useDrawer(init = false) {
  const [isOpen, setIsOpen] = useState(init);

  const onToggle = useCallback(() => {
    setIsOpen(prevState => !prevState);
  }, []);

  const onClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const onOpen = useCallback(() => {
    setIsOpen(true);
  }, []);

  // Handle Android back button press
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (isOpen) {
        onClose();
        return true; // Prevent default behavior (app exit)
      }
      return false; // Let default behavior happen (app exit or screen navigation)
    });

    return () => backHandler.remove();
  }, [isOpen, onClose]);

  return {
    isOpen,
    onToggle,
    onClose,
    onOpen,
  };
}

export { useDrawer };
