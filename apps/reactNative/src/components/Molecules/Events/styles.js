import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  eventsContainer: {
    gap: 16,
  },
  eventCard: {
    gap: 4,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#10101014',
  },
  lastEventCard: {
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  leftContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventType: {
    ...typography.text,
    color: '#101010',
  },
  exDate: {
    ...typography.body2,
    color: '#1010108A',
  },
  eventValue: {
    ...typography.text,
    color: '#101010',
  },
  skeleton: {
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
  },
  typeText: {
    width: 60,
    height: 16,
    marginBottom: 4,
  },
  valueText: {
    width: 80,
    height: 16,
  },
  dateText: {
    width: 120,
    height: 16,
  }
}); 