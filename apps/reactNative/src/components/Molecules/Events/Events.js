import React from 'react';
import { View, Text } from 'react-native';
import { useCompanyEvents } from "@paytm-money/store"
import { styles } from './styles';
import Error from "../../atoms/ErrorState"
import NoEventsImage from '../../../assets/EmptyEvents.svg';
import ErrorEvents from '../../../assets/ErrorEvents.svg';
import { EVENTS_CONSTANTS } from "./constants"

const EventCardSkeleton = ({ isLast }) => (
  <View style={[
    styles.eventCard,
    isLast && styles.lastEventCard
  ]}>
    <View style={styles.leftContent}>
      <View style={[styles.skeleton, styles.typeText]} />
      <View style={[styles.skeleton, styles.valueText]} />
    </View>
    <View style={[styles.skeleton, styles.dateText]} />
  </View>
);

const EventCard = ({ type, value, date, isLast }) => (
  <View style={[
    styles.eventCard,
    isLast && styles.lastEventCard
  ]}>
    <View style={styles.leftContent}>
      <Text style={styles.eventType}>{type}</Text>
      <Text style={styles.eventValue}>{value}</Text>
    </View>
    <Text style={styles.exDate}>Ex-date: {date}</Text>
  </View>
);

const Events = () => {
  const { events, isLoading, isError, refetch } = useCompanyEvents();

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.eventsContainer}>
          {[1, 2, 3].map((_, index) => (
            <EventCardSkeleton
              key={index}
              isLast={index === 2}
            />
          ))}
        </View>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.container}>
        <Error
          isRetry
          onRetry={refetch}
          RetryIcon={ErrorEvents}
          title={EVENTS_CONSTANTS.ERROR.TITLE}
          description={EVENTS_CONSTANTS.ERROR.DESCRIPTION}
        />
      </View>
    );
  }

  if (!events.length) {
    return (
      <View style={styles.container}>
        <Error
          title={EVENTS_CONSTANTS.EMPTY.TITLE}
          description={EVENTS_CONSTANTS.EMPTY.DESCRIPTION}
          ErrorIcon={NoEventsImage}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.eventsContainer}>
        {events.map((event, index) => (
          <EventCard
            key={index}
            {...event}
            isLast={index === events.length - 1}
          />
        ))}
      </View>
    </View>
  );
};

export default Events; 