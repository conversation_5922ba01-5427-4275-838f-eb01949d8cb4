import React, { useRef, useState, useEffect } from 'react';
import { View, FlatList, Dimensions } from 'react-native';
import StockCard from '../StockCard/StockCard';
import FnoCard from '../FnoCard/FnoCard';
import { styles } from './styles';

const { width } = Dimensions.get('window');
const PADDING = 16;
const GAP = 16;
const CARD_WIDTH = (width - (2 * PADDING) - (2 * GAP)) / 2; // 2 cards with padding and gap
const ITEM_WIDTH = CARD_WIDTH + GAP; // Width of each item including gap
const PAGE_WIDTH = ITEM_WIDTH * 2; // Width of two cards + gap

const StockCarousel = ({ data, onCardPress, isFno = false, activeType }) => {
  const flatListRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  // Reset position when activeType changes
  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({ offset: 0, animated: true });
      setActiveIndex(0);
    }
  }, [activeType]);

  const renderItem = ({ item, index }) => {
    const isLastItem = index === data.length - 1;
    const isOddLength = data.length % 2 === 1;
    
    return (
      <View style={[
        { 
          width: CARD_WIDTH,
          marginRight: (isLastItem && isOddLength) ? width - PADDING - CARD_WIDTH : GAP,
        }
      ]}>
        {
          isFno ? <FnoCard
            {...item}
            onPress={() => onCardPress(item)}
          /> :
          <StockCard
            {...item}
            onPress={() => onCardPress(item)}
          />
        }
      </View>
    );
  };

  // Calculate snap points for pairs of cards
  const getSnapPoints = () => {
    const points = [];
    const numPairs = Math.floor(data.length / 2);
    
    // Add points for complete pairs
    for (let i = 0; i < numPairs; i++) {
      points.push(i * PAGE_WIDTH);
    }
    
    // Add point for last single card if length is odd
    if (data.length % 2 === 1) {
      points.push(numPairs * PAGE_WIDTH);
    }
    
    return points;
  };

  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / PAGE_WIDTH);
    setActiveIndex(index);
  };

  const renderIndicators = () => {
    const numIndicators = Math.ceil(data.length / 2);
    return (
      <View style={styles.indicatorWrapper}>
        <View style={styles.indicatorContainer}>
          {Array(numIndicators).fill(0).map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                index === activeIndex && styles.activeIndicator
              ]}
            />
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.symbol}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        snapToOffsets={getSnapPoints()}
        snapToAlignment="start"
        decelerationRate="fast"
        contentContainerStyle={styles.contentContainer}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      />
      {renderIndicators()}
    </View>
  );
};

export default StockCarousel;
