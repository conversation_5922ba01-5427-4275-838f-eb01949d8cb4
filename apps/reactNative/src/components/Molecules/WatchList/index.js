import React from 'react';
import { View ,Text} from 'react-native';
import Drawer from '../Drawer';
import WatchListOptions from './partials/WatchListOptions';
import AddNewWatchList from './partials/AddNewWatchList';

function WatchList({ watchListController }) {
  const {
    isOpen,
    onClose,
    watchListResults,
    createNewWatchList,
    setCreateNewWatchList,
    watchListData,
    selectedStockId,
    handleAddToWatchList,
    companyDetails,
    removeFromWatchListMutate,
  } = watchListController;

  const { data, refetch } = watchListResults;

  const watchlists = data?.watchlists;
  if (!watchlists?.length) return null;


  return (
    <Drawer
      isOpen={isOpen}
      onClose={()=>{
        
        onClose()
        setCreateNewWatchList(false)
      }}
      title={createNewWatchList ? 'Create Watchlist' : 'Add to Watchlist'}
      showHandleBar
    >
      {!createNewWatchList ? <View>
        {watchlists?.map((watchlist) => (
          <WatchListOptions
          id={watchlist?.id}
          onClick={handleAddToWatchList}
          key={watchlist?.id}
          title={watchlist?.name}
          count={watchlist?.security_count}
          maxItems={50}
          isDisabled={watchlist?.security_count >= 50}
          isSelected={watchListData?.stocksInsideWatchListMap?.[
            watchlist?.id
          ]?.has(selectedStockId)}
          stockId={selectedStockId}
          companyDetails={companyDetails}
          removeFromWatchListMutate={removeFromWatchListMutate}
          />
        ))}
      </View> : null}
      <AddNewWatchList
        watchlists={watchlists || []}
        createNewWatchList={createNewWatchList}
        setCreateNewWatchList={setCreateNewWatchList}
        refetch={refetch}
      />
    </Drawer>
  );
}

export default WatchList; 