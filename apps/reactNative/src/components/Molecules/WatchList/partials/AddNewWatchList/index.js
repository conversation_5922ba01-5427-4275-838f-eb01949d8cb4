import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import { createWatchlist ,useApiHeaders} from "@paytm-money/store";
import { styles } from './styles';

const AddNewWatchList = ({
  watchlists,
  createNewWatchList,
  setCreateNewWatchList,
  refetch
}) => {
  const [inputValue, setInputValue] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const { headers } = useApiHeaders();
  const addWatchList = async () => {
    let errMsg = '';
    // Validate watchlist name
    for (const item of watchlists) {
      if (item?.name?.toLowerCase() === inputValue?.trim()?.toLowerCase()) {
        errMsg = 'Watchlist name is already present.';
        break;
      }
    }
    if (inputValue.length < 3) {
      errMsg = 'Watchlist name should be of at least 3 characters';
    }

    if (errMsg) {
      setErrorMsg(errMsg);
    } else {
      try {
        await createWatchlist(inputValue,headers);
        setCreateNewWatchList(false);
        setInputValue('');
        refetch();
      } catch (err) {
        const displayMessage = err?.meta?.displayMessage;
        setErrorMsg(displayMessage || 'Something went wrong');
      }
    }
  };

  useEffect(() => {
    if (inputValue.length > 3) {
      setErrorMsg('');
    }
  }, [inputValue]);

  // When not in create mode, show a button to enter create mode
  if (!createNewWatchList) {
    return (
      <TouchableOpacity 
        onPress={() => setCreateNewWatchList(true)} 
        style={styles.createNewButton}
      >
        <Text style={styles.createNewButtonText}>Create New Watchlist</Text>
      </TouchableOpacity>
    );
  }

  // When in create mode, show the form
  return (
    <View style={styles.container}>
      <TextInput
        style={[
          styles.input,
          styles.inputSelected,
          errorMsg && styles.inputError
        ]}
        value={inputValue}
        onChangeText={setInputValue}
        placeholder="Watchlist Name"
        placeholderTextColor="#1010108A"
        maxLength={50}
        autoFocus={true}
      />
      {errorMsg ? (
        <Text style={styles.errorMessage}>{errorMsg}</Text>
      ) : null}
      <TouchableOpacity 
        style={[
          styles.button,
          !inputValue.trim() && styles.disabledButton
        ]}
        onPress={addWatchList}
        disabled={!inputValue.trim()}
      >
        <Text style={styles.buttonText}>Create</Text>
      </TouchableOpacity>
    </View>
  );
};

export default AddNewWatchList; 