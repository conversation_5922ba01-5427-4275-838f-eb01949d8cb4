import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 12,
  },
  input: {
    ...typography.body2R,
    height: 48,
    borderWidth: 1,
    borderColor: '#E7F1F8',
    borderRadius: 8,
    paddingHorizontal: 16,
    color: '#101010',
    backgroundColor: '#FFFFFF',
  },
  inputSelected: {
    borderColor: '#1576DB',
  },
  inputError: {
    borderColor: '#FD5154',
  },
  errorMessage: {
    ...typography.caption2R,
    color: '#FD5154',
    marginTop: 4,
  },
  button: {
    height: 48,
    backgroundColor: '#101010',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    ...typography.button1,
    color: '#FFFFFF',
  },
  createNewButton: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E7F1F8',
  },
  createNewButtonText: {
    ...typography.body2B2,
    color: '#1576DB',
  },
}); 