import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import checked from "../../../../../assets/checkbox-checked.png"
import unChecked from "../../../../../assets/checkbox-unchecked.png"
import { styles } from './styles';

const WatchlistOption = ({
  id,
  title,
  count,
  maxItems,
  isSelected,
  isDisabled,
  onClick,
  companyDetails,
  removeFromWatchListMutate,
}) => {
  const handleClick = () => {
    if (isSelected) {
      removeFromWatchListMutate({
        watchlist_id: id,
        watchlistName: title,
        companyDetails,
      });
    } else {
      onClick.mutate({
        watchlist_id: id,
        watchlistName: title,
        companyDetails,
      });
    }
  };

  return (
    <TouchableOpacity 
      onPress={handleClick}
      disabled={isDisabled}
      style={[styles.optionContainer, isDisabled && styles.disabled]}
    >
      <View style={styles.optionContent}>
        <Image 
          source={isSelected ? checked : unChecked}
          style={styles.checkboxImage}
        />
        <View style={styles.options}>
          <View style={styles.optionHeader}>
            <Text style={styles.title}>{title}</Text>
          </View>
          <Text style={styles.itemCount}>
            {count} of {maxItems} items
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default WatchlistOption; 