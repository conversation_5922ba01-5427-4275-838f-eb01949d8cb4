import { StyleSheet } from 'react-native';
import { typography } from '../../../../../theme/typography';

export const styles = StyleSheet.create({
  optionContainer: {
    display: 'flex',
    width: '100%',
    // alignItems: 'center',
    margin: 16,
    // fontFamily: 'Inter',
  },
  disabled: {
    opacity: 0.22,
  },
  optionContent: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  options: {
    // flex: 1,
    paddingBottom:16,
    width:"100%",
    marginLeft: 12,
    borderBottomWidth:1,
    borderBottomColor:'#1010108A'
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    ...typography.text,
    color: '#101010',
  },
  itemCount: {
    // marginTop: 4,
    ...typography.body2R3,
    color: '#1010108A',
  },
  checkboxImage: {
    width: 24,
    height: 24,
  }
}); 