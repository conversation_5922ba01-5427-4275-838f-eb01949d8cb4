import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  newsContainer: {
    gap: 16,
  },
  newsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },

  newsContent: {
    gap: 8,
  },
  newsTitle: {
    ...typography.heading14,
    color: '#101010',
  },
  newsDetailDescription:{
    ...typography.body2,
    color: '#101010B2',
    paddingVertical:16
  },

  newsDescription: {
    ...typography.body2,
    color: '#101010B2',
  },
  newsTime: {
    ...typography.body3,
    color: '#101010B2',
  },
  // drawerContent:{
  //   padding:16
  // },
  tabStyle:{
  backgroundColor: '#FFFFFF',
  },
  newsDetailContent:{
    paddingHorizontal:16
  },
  caption:{
    ...typography.text,
    color: '#101010',
  },
  skeleton: {
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
  },
  captionSkeleton: {
    height: 20,
    width: '60%',
  },
  descriptionSkeletonContainer: {
    paddingVertical: 16,
    gap: 16,
  },
  descriptionSkeleton: {
    height: 16,
    width: '100%',
  },
  timeSkeleton: {
    height: 16,
    width: '30%',
  },
  titleShimmer: {
    height: 14,
    width:88,
    backgroundColor: '#1010100F',
    borderRadius: 4,
    marginBottom: 8,
  },
  descriptionShimmer: {
    height: 14,
    backgroundColor: '#1010100F',
    borderRadius: 4,
    marginBottom: 8,
  },
  titleShimmerContainer:{
    display:'flex',
    flexDirection:'row',
    gap:10
  },
  timeShimmer: {
    width: '20%',
    height: 16,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  errorStyle: {
    paddingVertical:48
  }
}); 