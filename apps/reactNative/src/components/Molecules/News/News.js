import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import { View, Text, TouchableOpacity } from 'react-native';
import { useCompanyNewsDetails } from "@paytm-money/store"
import FlatTabs from '../../atoms/FlatTabs/FlatTabs';
import Drawer from '../Drawer';
import { useDrawer } from '../Drawer/useDrawer';
import ErrorState from '../../atoms/ErrorState';
import { NEWS_CONSTANTS as CONSTANTS } from './constants';
import { styles } from './styles';

const NewsCard = ({ title, description, time, onPress }) => (
  <TouchableOpacity style={styles.newsCard} onPress={onPress}>
    <View style={styles.newsContent}>
      <Text style={styles.newsTitle} numberOfLines={2}>{title}</Text>
      {description ? <Text style={styles.newsDescription} numberOfLines={4}>{description}</Text>:null}
      {time ? <Text style={styles.newsTime}>{time}</Text>:null}
    </View>
  </TouchableOpacity>
);

const NewsDetailCard = ({caption, description, time }) => (
    <View style={styles.newsDetailContent}>
      {caption ? <Text style={styles.caption}>{caption}</Text>:null}
      {description ? <Text style={styles.newsDetailDescription}>{description}</Text>:null}
      {time ? <Text style={styles.newsTime}>{time}</Text>:null}
    </View>
);

// Add loading skeleton component
const NewsDetailSkeleton = () => (
  <View style={styles.newsDetailContent}>
    <View style={[styles.skeleton, styles.captionSkeleton]} />
    <View style={styles.descriptionSkeletonContainer}>
      <View style={[styles.skeleton, styles.descriptionSkeleton]} />
      <View style={[styles.skeleton, styles.descriptionSkeleton]} />
      <View style={[styles.skeleton, styles.descriptionSkeleton]} />
    </View>
    <View style={[styles.skeleton, styles.timeSkeleton]} />
  </View>
);

const LoadingShimmer = () => {
  return (
      <View style={styles.newsContainer}>
        {[...Array(3)].map((_, i) => (
          <View key={i} style={styles.newsCard}>
            <View style={styles.newsContent}>
              <View style={styles.titleShimmer} />
              <View style={styles.descriptionShimmer} />
              <View style={styles.titleShimmerContainer}>
              <View style={styles.timeShimmer} />
              <View style={styles.timeShimmer} />
              <View style={styles.timeShimmer} />
              </View>
              
            </View>
          </View>
        ))}
      </View>
  );
};

const News = ({isin, companyName}) => {
  const { isOpen, onOpen, onClose } = useDrawer();
  const { 
    newsData, 
    isNewsLoading, 
    isNewsError,
    refetchNews,
    newsDetailsData,
    isNewsDetailsLoading,
    isNewsDetailsError,
    refetchDetails,
    onSelectNews,
    setSelectedNews,
  } = useCompanyNewsDetails(isin, companyName);
  const [activeTab, setActiveTab] = useState('');

  useEffect(() => {
    if (newsData?.availableTabs?.length) {
      setActiveTab(newsData.availableTabs[0]);
    }
  }, [newsData?.availableTabs]);

  const renderContent = () => {
    if (isNewsLoading) return <LoadingShimmer />;
    
    if (isNewsError) {
      return (
        <ErrorState 
          title={CONSTANTS.ERROR.TITLE}
          description={CONSTANTS.ERROR.DESCRIPTION}
          isRetry
          onRetry={refetchNews}
          style={styles.errorStyle}
        />
      );
    }

    if (!newsData?.tabs[activeTab]?.length) {
      return (
        <ErrorState 
          title={CONSTANTS.EMPTY.TITLE}
          description={CONSTANTS.EMPTY.DESCRIPTION}
          style={styles.errorStyle}
        />
      );
    }

    return (
      <>
      <FlatTabs
        tabs={newsData.availableTabs}
        activeTab={activeTab}
        onTabPress={setActiveTab}
        tabStyle={styles.tabStyle}
      />
      <View style={styles.newsContainer}>
        {newsData.tabs[activeTab]?.map((news, index) => (
          <NewsCard
            key={index}
            {...news}
            onPress={() => handleNewsPress(news)}
          />
        ))}
      </View>
      </>
    );
  };

  const handleNewsPress = async (news) => {
    await onSelectNews(news);
    onOpen();
  };

  const renderDrawerContent = () => {
    if (isNewsDetailsLoading) return <NewsDetailSkeleton />;
    
    if (isNewsDetailsError) {
      return (
        <ErrorState 
          description={CONSTANTS.DETAILS_ERROR.DESCRIPTION}
          isRetry
          onRetry={refetchDetails}
        />
      );
    }

    return (
      <NewsDetailCard
        caption={newsDetailsData?.caption}
        description={newsDetailsData?.news_details}
        time={`${dayjs(newsDetailsData?.date).format('DD MMM YYYY')} · ${newsDetailsData?.time}`}
      />
    );
  };

  return (
    <View style={styles.container}>
      {renderContent()}
      <Drawer
        isOpen={isOpen}
        onClose={() => {
          setSelectedNews(null);
          onClose();
        }}
        title={newsDetailsData?.heading || `${companyName} News`}
      >
        <View style={styles.drawerContent}>
          {renderDrawerContent()}
        </View>
      </Drawer>
    </View>
  );
};

export default News; 