import React from 'react';
import { Dimensions, Modal, StyleSheet, TouchableOpacity } from 'react-native';
import styles from './styles';

const SCREEN_HEIGHT = Dimensions.get('window').height;
const SCREEN_WIDTH = Dimensions.get('window').width;

const CustomModal = ({ children, isOpen, onClose }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isOpen}
      close
      onRequestClose={onClose}>
      <TouchableOpacity style={styles.centeredView} onPressOut={onClose}>

        {children}
      </TouchableOpacity>
    </Modal>
  );
};


export default CustomModal;