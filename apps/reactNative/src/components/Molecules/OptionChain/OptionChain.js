import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { styles } from './styles';

const OptionRow = ({ call, strike, put }) => {
  const getProgressWidth = (change) => {
    const absChange = Math.abs(change);
    // Cap at 100% and normalize the percentage
    return Math.min(absChange, 100);
  };

  return (
    <View style={styles.optionList}>
    <View style={styles.row}>
      {/* Call Section */}
      <View style={styles.callSection}>
        <Text style={styles.price}>{call.price}</Text>
        <Text style={[styles.change, call.change > 0 ? styles.positive : styles.negative]}>
          {call.change > 0 ? '+' : ''}{call.change}%
        </Text>
      </View>

      {/* Strike Section */}
      <View style={styles.strikeSection}>
        <Text style={styles.strike}>{strike}</Text>
      </View>

      {/* Put Section */}
      <View style={styles.putSection}>
        <Text style={styles.price}>{put.price}</Text>
        <Text style={[styles.change, put.change > 0 ? styles.positive : styles.negative]}>
          {put.change > 0 ? '+' : ''}{put.change}%
        </Text>
      </View>
    </View>
    <View style={styles.progressBarContainer}>
      <View style={styles.callProgressBarWidth}>
    <View 
            style={[
              styles.progressBar,
              styles.callProgressBar,
              { width: `${getProgressWidth(call.change)}%` },
              // call.change > 0 ? styles.positiveBar : styles.negativeBar
            ]} 
          />
          </View>
          <View style={styles.putProgressBarWidth}>
<View 
            style={[
              styles.progressBar,
              styles.putProgressBar,
              { width: `${getProgressWidth(put.change)}%` },
            ]} 
          />
          </View>
    </View>
    </View>
  );
};

const OptionChain = () => {
    const data = [
      { call: { price: '82.50', change: 76.52 }, strike: '23,400', put: { price: '163', change: -40.75 } },
      { call: { price: '35.65', change: 85.15 }, strike: '23,500', put: { price: '163', change: -55.15 } },
      { call: { price: '3.90', change: -95.32 }, strike: '23,550', put: { price: '16.14', change: 60.25 } },
      { call: { price: '1.45', change: -82.21 }, strike: '23,600', put: { price: '17.35', change: 30.15 } },
    ];
  
  // Find middle index
  const middleIndex = Math.floor(data.length / 2);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Option Chain</Text>
        <Text style={styles.headerDate}>15 Mar 2024</Text>
      </View>

      {/* Column Headers */}
      <View style={styles.columnHeaders}>
        <Text style={styles.columnHeader}>CALL</Text>
        <Text style={styles.columnHeader}>STRIKE</Text>
        <Text style={styles.columnHeader}>PUT</Text>
      </View>

      {/* First half of option rows */}
      {data.slice(0, middleIndex).map((item, index) => (
        <OptionRow
          key={index}
          call={item.call}
          strike={item.strike}
          put={item.put}
        />
      ))}

      {/* Current Price Section with connecting lines */}
      <View style={styles.currentPriceWrapper}>
        <View style={styles.connectingLine} />
        <View style={styles.currentPrice}>
          <View style={styles.currentPriceContent}>
            <Text style={styles.currentPriceText}>23,537.65(+1.20%)</Text>
            <Text style={styles.currentPriceLabel}>Long Build Up</Text>
          </View>
        </View>
        <View style={styles.connectingLine} />
      </View>

      {/* Second half of option rows */}
      {data.slice(middleIndex).map((item, index) => (
        <OptionRow
          key={index + middleIndex}
          call={item.call}
          strike={item.strike}
          put={item.put}
        />
      ))}
    </View>
  );
};

export default OptionChain; 