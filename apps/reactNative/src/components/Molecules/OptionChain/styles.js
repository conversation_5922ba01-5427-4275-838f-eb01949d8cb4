import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  optionList:{
    paddingVertical:12
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: 16,
  },
  headerTitle: {
    ...typography.heading4B1,
    color: '#101010',
  },
  headerDate: {
    ...typography.body2R,
    color: '#1010108A',
  },
  columnHeaders: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // paddingVertical: 8,
  },
  columnHeader: {
    // flex: 1,
    ...typography.body2R,
    color: '#1010108A',
    // width:'30%'
    // textAlign: 'center',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom:4
    // paddingVertical: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: '#E7F1F8',
  },
  callSection: {
    // flex: 1,
    alignItems: 'center',
    flexDirection:'row',
    gap: 4,
    width:'30%'
  },
  strikeSection: {
    // flex: 1,
    alignItems: 'center',
    width:'30%'
  },
  putSection: {
    // flex: 1,
    alignItems:'center',
    flexDirection:'row-reverse',
    gap: 4,
    width:'30%'
  },
  price: {
    ...typography.heading14,
    color: '#101010',
  },
  strike: {
    ...typography.heading14,
    color: '#101010',
  },
  change: {
    ...typography.caption1M,
  },
  positive: {
    color: '#21C179',
  },
  negative: {
    color: '#FD5154',
  },
  currentPriceWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingVertical: 8,
  },
  connectingLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#1576DB',
  },
  currentPrice: {
    backgroundColor: '#1576DB',
    borderRadius:8,
    paddingVertical: 6,
    paddingHorizontal:12
  },
  currentPriceContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  currentPriceText: {
    ...typography.body2B2,
    color: '#FFFFFF',
  },
  currentPriceLabel: {
    ...typography.body2R,
    color: '#FFFFFF',
  },
  progressBarContainer:{
    alignItems:'center',
    flexDirection:'row'
  },
  progressBar:{
    height:2,
    
  },
  callProgressBar:{
    backgroundColor: '#E63757',
    opacity:0.2
  },
  putProgressBar:{
    backgroundColor: '#52CB77',
    opacity:0.2
  },
  callProgressBarWidth:{
    width:"50%",
    flexDirection:'row-reverse'
  },
  putProgressBarWidth:{
    width:"50%",
    flexDirection:'row'
  }
}); 