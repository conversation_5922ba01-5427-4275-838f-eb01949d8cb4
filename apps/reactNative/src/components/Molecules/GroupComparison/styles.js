import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    marginVertical:8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  title: {
    ...typography.heading14,
    color: '#101010',
  },
  info: {
    ...typography.text,
    color: '#101010',
  },
  barContainer: {
    gap: 26,
    flexDirection: 'row',
    alignItems:'center',
    justifyContent:'space-between'
  },
  labelContainer: {
    flex:1,
    flexDirection: 'column',
  },
  label: {
    ...typography.body2,
    color: '#101010B2',
    paddingBottom:3
  },
  value: {
    ...typography.text,
    color: '#101010',
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#D8E7F7',
    borderRadius: 100,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 100,
  },
  labelShimmer: {
    height: 14,
    width: 92,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  barShimmer: {
    marginTop:8,
    height: 5,
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  valueShimmer: {
    height: 16,
    width: '15%',
    backgroundColor: '#1010100F',
    borderRadius: 4,
  },
  comparisonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
    marginBottom: 16,
  },
  content: {
    gap:16
  },

  lableShimmerContainer:{
    display:"flex",
    flexDirection:"column",
    width:"80%"
  }
}); 