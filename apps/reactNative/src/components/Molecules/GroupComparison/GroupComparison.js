import React from 'react';
import { View, Text } from 'react-native';
import ComparisonBar from '../../atoms/ComparisonBar/ComparisonBar';
import ErrorState from '../../atoms/ErrorState';
import { GROUP_COMPARISON_CONSTANTS as CONSTANTS } from './constants';
import InfoIcon from "../../../assets/Info_icon.svg"
import { styles } from './styles';

const Header = () => (
  <View style={styles.header}>
    <Text style={styles.title}>Group Comparison</Text>
    <InfoIcon />
  </View>
);

const LoadingShimmer = () => (
  <View style={styles.content}>
    {[...Array(4)].map((_, index) => (
      <View key={index} style={styles.comparisonItem}>
        <View style={styles.lableShimmerContainer}>
          <View style={styles.labelShimmer} />
          <View style={styles.barShimmer} />
        </View>
        <View style={styles.valueShimmer} />
      </View>
    ))}
  </View>
);

const GroupComparison = ({ comparisonData, isLoading, isError, refetch }) => {
  const renderContent = () => {
    if (isLoading) {
      return <LoadingShimmer />;
    }

    if (isError) {
      return (
        <ErrorState
          title={CONSTANTS.ERROR.TITLE}
          description={CONSTANTS.ERROR.DESCRIPTION}
          isRetry
          onRetry={refetch}
        />
      );
    }

    if (!comparisonData?.length) {
      return (
        <ErrorState
          title={CONSTANTS.EMPTY.TITLE}
          description={CONSTANTS.EMPTY.DESCRIPTION}
        />
      );
    }

    return (
      <View style={styles.content}>
        {comparisonData.map((item, index) => (
          <ComparisonBar
            key={index}
            label={item.label}
            value={item.value}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Header />
      {renderContent()}
    </View>
  );
};

export default GroupComparison; 