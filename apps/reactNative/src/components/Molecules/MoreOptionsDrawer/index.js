import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Share from '../../../assets/Share.svg';
import Pin from '../../../assets/Pin.svg';
import OptionChain from '../../../assets/optionChain.svg';
import Watchlist from '../../../assets/watchlist.svg';
import { styles } from './styles';

const MoreOptionsDrawer = ({ onClose }) => {
  const options = [
    { icon: Pin, label: 'Pin Stock', onPress: () => console.log('Pin pressed') },
    { icon: OptionChain, label: 'Option Chain', onPress: () => console.log('Option Chain pressed') },
    { icon: Watchlist, label: 'Watchlist', onPress: () => console.log('Watchlist pressed') },
    { icon: Share, label: 'Share', onPress: () => console.log('Share pressed') },
  ];

  return (
    <View style={styles.drawerContent}>
      {options.map((option, index) => (
        <TouchableOpacity 
          key={index}
          style={[
            styles.drawerOption,
            index === options.length - 1 && styles.lastOption
          ]}
          onPress={() => {
            option.onPress();
            onClose();
          }}
        >
          <option.icon width={24} height={24} />
          <Text style={styles.drawerOptionText}>{option.label}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default MoreOptionsDrawer; 