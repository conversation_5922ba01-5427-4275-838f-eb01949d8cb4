import { StyleSheet } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  tableContainer: {
    flexDirection: 'row',
    paddingHorizontal:16,
    paddingBottom:16
  },
  fixedColumn: {
    width: 100,
    flexShrink: 0,
  },
  headerCell: {
    ...typography.caption2R,
    paddingVertical: 10,
    minWidth: 100,
    color: '#1010108A',
    // textAlign: 'left',
  },
  stockNameContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#F5F0F0',
    paddingVertical: 10,
  },
  lastStockContainer: {
    borderBottomWidth: 0,
  },
  stockName: {
    ...typography.text,
    color: '#101010',
    paddingRight:10
  },
  headerRow: {
    flexDirection: 'row',
  },
  dataRows: {
    flexDirection: 'column',
  },
  dataRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#F5F0F0',
  },
  cellContainer: {
    minWidth: 100,
    paddingVertical: 10,
  },
  cell: {
    ...typography.heading14,
    textAlign: 'left',
  },
  positive: {
    color: '#21C179',
  },
  negative: {
    color: '#FD5154',
  },
}); 