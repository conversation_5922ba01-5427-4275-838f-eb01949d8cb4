import React,{useState} from 'react';
import { View, Text, ScrollView } from 'react-native';
import { styles } from './styles';
import Accordion from '../../atoms/Accordion/Accordion';

// const requiredFeedResponse = [RESPONSE_TYPES.TRADE];

const rangesToShow = [
  { days: 1, label: '1 Day' },
  { days: 7, label: '1 Week' },
  { days: 30, label: '1 Month' },
  { days: 90, label: '3 Month' },
  { days: 365, label: '1 Year' },
  { days: 1095, label: '3 Year' },
];


const StockPerformance = (props) => {

  const {companyDetail, pmlId, index} = props;
    const {
      exchange,
      security_id: securityId,
      segment,
      } = companyDetail;
      const [pmlData, setPmlData] = useState([]);

      // const stockFeed = useResultFromFeed(
      //   REQUEST_TYPES.STOCK,
      //   requiredFeedResponse,
      //   { exchange, securityId, segment },
      // );

      // const requiredFeed = useMarketIndexFeed();
      // const computedIndexDetails = useObservable(
      //   () => requiredFeed({ security_id: index?.security_id }),
      //   [requiredFeed, index],
      // );
    
      // const stockLtp = stockFeed ? stockFeed?.lastTradePrice : 0;
      // const indexLtp = computedIndexDetails?.ltp;
    
      // const returns = useMemo(() => {
      //   if (!pmlData.length || !indexLtp || !stockLtp) return [];
      //   return pmlData.map(({ label, stockPClose, indexPClose }) => {
      //     const stockReturns = ((stockLtp - stockPClose) / stockPClose) * 100;
      //     const indexReturns = ((indexLtp - indexPClose) / indexPClose) * 100;
      //     return {
      //       stockReturns,
      //       indexReturns,
      //       label,
      //     };
      //   });
      // }, [pmlData, indexLtp, stockLtp]);
    
      // useEffect(() => {
      //   const pCloseData = [];
      //   const stockPclose = [];
      //   const indexPclose = [];
      //   if (pmlId && index) {
      //     const stockPmlData = rangesToShow.map(range => ({
      //       pmlId,
      //       date: dayjs()
      //         .subtract(range.days, 'days')
      //         .format('DD-MM-YYYY'),
      //     }));
      //     const indexPmlData = rangesToShow.map(range => ({
      //       pmlId: index.id,
      //       date: dayjs()
      //         .subtract(range.days, 'days')
      //         .format('DD-MM-YYYY'),
      //     }));
      //     Promise.all([pClose(indexPmlData), pClose(stockPmlData)])
      //       .then(([indexPCloseData, stockPCloseData]) => {
      //         const results = indexPCloseData.data.data.results.concat(
      //           stockPCloseData.data.data.results,
      //         );
    
      //         for (let i = 0; i < results.length; i += 1) {
      //           if (pmlId === results[i].id) {
      //             stockPclose.push(results[i].p_close);
      //           } else {
      //             indexPclose.push(results[i].p_close);
      //           }
      //         }
      //         const pCloseDataLength =
      //           stockPclose.length < indexPclose.length
      //             ? stockPclose.length
      //             : indexPclose.length;
      //         for (let i = 0; i < pCloseDataLength; i += 1) {
      //           pCloseData.push({
      //             label: rangesToShow[i].label,
      //             stockPClose: stockPclose[i],
      //             indexPClose: indexPclose[i],
      //           });
      //         }
      //         setPmlData(pCloseData);
      //         if (pCloseData.length < rangesToShow.length)
      //           setSelectedIndex(pCloseData.length - 1);
      //       })
      //       .catch();
      //   }
      // }, [index, pmlId]);
    
      // if (!index || !returns.length) return null;
    
    
      // const availableRanges = rangesToShow.filter((range) =>
      //   returns.some((ret) => ret.label === range.label)
      // );
    

  const data = [
    { stock: "Reliancedsdsdsdsdsdsdsds", values: [-0.55, 0.15, 0.15, 0.25, 1.2, 5.0] },
    { stock: "Nifty 50", values: [5.00, 2.10, 2.10, 2.50, 6.2, 12.5] },
  ];

  const headers = ["1 Day", "1 Week", "1 Month", "3 Months", "1 Year", "3 Years"];

  const formatValue = (value) => {
    const formattedValue = Math.abs(value).toFixed(2);
    return value < 0 ? `- ${formattedValue}%` : `+ ${formattedValue}%`;
  };

  const renderContent = () => (
    <View style={styles.tableContainer}>
      {/* Fixed Stock Column */}
      <View style={styles.fixedColumn}>
        <Text style={styles.headerCell}>Stock</Text>
        {data.map((row, index) => (
          <View 
            key={index} 
            style={[
              styles.stockNameContainer,
              index === data.length - 1 && styles.lastStockContainer
            ]}
          >
            <Text style={styles.stockName} numberOfLines={1}>{row.stock}</Text>
          </View>
        ))}
      </View>

      {/* Scrollable Section */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View>
          {/* Headers */}
          <View style={styles.headerRow}>
            {headers.map((header, index) => (
              <Text key={index} style={styles.headerCell}>{header}</Text>
            ))}
          </View>

          {/* Values */}
          <View style={styles.dataRows}>
            {data.map((row, rowIndex) => (
              <View key={rowIndex} style={[
                styles.dataRow,
                rowIndex === data.length - 1 && styles.lastStockContainer
              ]}>
                {row.values.map((value, colIndex) => (
                  <View key={colIndex} style={styles.cellContainer}>
                    <Text 
                      style={[
                        styles.cell,
                        value < 0 ? styles.negative : styles.positive
                      ]}
                    >
                      {formatValue(value)}
                    </Text>
                  </View>
                ))}
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );

  return (
    <Accordion
      title="Stock Performance"
      isOpen={true}
    >
      {renderContent()}
    </Accordion>
  );
};

export default StockPerformance; 