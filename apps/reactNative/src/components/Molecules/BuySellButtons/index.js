import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { styles } from './styles';

const BuySellButtons = ({ 
  onBuyPress,
  onSellPress,
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity 
        style={styles.sellButton} 
        onPress={onSellPress}
      >
        <Text style={styles.sellText}>Sell</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={styles.buyButton} 
        onPress={onBuyPress}
      >
        <Text style={styles.buyText}>Buy</Text>
      </TouchableOpacity>
    </View>
  );
};

export default BuySellButtons; 