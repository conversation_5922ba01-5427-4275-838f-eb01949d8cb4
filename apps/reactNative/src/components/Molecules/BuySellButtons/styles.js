import { StyleSheet, Platform } from 'react-native';
import { typography } from '../../../theme/typography';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 16,
    padding: 16,
    height: 100,
    paddingBottom: Platform.OS === 'ios' ? 40 : 16,
    backgroundColor: '#FFFFFF',
    bottom: 0,
    left: 0,
    right: 0,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 8,
  },
  sellButton: {
    flex: 1,
    height: 48,
    backgroundColor: '#FF4B4E',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buyButton: {
    flex: 1,
    height: 48,
    backgroundColor: '#21C179',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sellText: {
    ...typography.button1,
    color: '#FFFFFF',
  },
  buyText: {
    ...typography.button1,
    color: '#FFFFFF',
  }
}); 