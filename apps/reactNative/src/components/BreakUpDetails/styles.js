import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 11,
  },
  headerText: {
    ...typography.heading3B1,
    lineHeight: 24,
    color: '#101010', // ETFRed from colors.scss
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  label: {
    color: 'rgba(0, 0, 0, 0.7)',
    ...typography.heading14,
  },
  value: {
    color: '#111111',
    ...typography.heading14,
  },
  leverage: {
    marginLeft: 3,
    color: '#F58F00',
  },
  total: {
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 12,
  },
  bold: {
    fontWeight: '500',
  },
});

export default styles;