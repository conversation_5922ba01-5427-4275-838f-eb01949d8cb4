import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { typography } from '../../theme/typography';
import Drawer from '../Molecules/Drawer/Drawer';
import Change from '../atoms/Change';
import styles from './styles';

function BreakUpDetails({
  isLimitOrder,
  price,
  exchange,
  segment,
  securityId,
  quantity,
  leverage = '3.3x',
  marginRequired,
  isOpen,
  onClose
}) {

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>Buy using Pay Later (Break Up)</Text>
        </View>
      }
    >
      <View style={styles.container}>
        <View style={styles.row}>
          <Text style={styles.label}>
            You Pay
            <Text style={styles.leverage}>{` (${leverage}x leverage)`}</Text>
          </Text>
          <Change
            value={marginRequired}
            style={styles.value}
            withRupee
            neutral
          />
        </View>

        <View style={styles.row}>
          <Text style={styles.label}>Paytm Money Funding</Text>
          <Change
            value={price ? (price || 0) * quantity - marginRequired : '-'}
            style={styles.value}
            withRupee
            neutral
          />
        </View>

        <View style={[styles.row, styles.total]}>
          <Text style={[styles.label, styles.bold]}>Total Required</Text>
          <Change
            value={price || '-'}
            style={styles.value}
            withRupee
            neutral
          />
        </View>


      </View>
    </Drawer>
  );
}



export default BreakUpDetails;