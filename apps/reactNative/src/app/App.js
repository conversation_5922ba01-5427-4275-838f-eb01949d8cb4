import React from 'react';
import { StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { SnackbarProvider, ApiHeaderProvider, ReactQueryWrapper } from '@paytm-money/store';
import Snackbar from '../components/atoms/Snackbar';
import { APPEARANCE_TYPES, SNACKBAR_THEME } from '../enums/snackbarEnums';

// Import the navigator
import AppNavigator from '../navigation/AppNavigator';

const defaultHeaders = {
  headers: {
    "Content-Type": "application/json; charset=utf-8",
    "x-pmngx-key": "paytmmoney",
    "x-sso-token": "cba6210a-85a6-40c8-bf04-b19633891900",
    "x-user-agent": JSON.stringify({"platform":"paytm-android","app_version":"2.1.2602-development","model":"undefined - motorola Moto G (5S)","user_id":"15122086","source":"PAYTM"})
  },
  userId: '15122086',
  broadCastHeader: []
};

export const App = () => {
  return (
    <ReactQueryWrapper>
      <ApiHeaderProvider 
        headersValue={defaultHeaders.headers}
        userIdValue={defaultHeaders.userId}
        broadCastHeaderValue={defaultHeaders.broadCastHeader}
      >
        <SnackbarProvider 
          Snackbar={Snackbar}
          APPEARANCE_TYPES={APPEARANCE_TYPES}
          SNACKBAR_THEME={SNACKBAR_THEME}
        >
          <NavigationContainer>
            <AppNavigator />
          </NavigationContainer>
        </SnackbarProvider>
      </ApiHeaderProvider>
    </ReactQueryWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
  },
});

export default App;