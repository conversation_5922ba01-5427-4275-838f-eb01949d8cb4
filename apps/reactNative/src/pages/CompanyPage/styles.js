import { StyleSheet, Dimensions, Platform } from 'react-native';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const BOTTOM_SPACING = Platform.OS === 'ios' ? 190 : 70;

export const styles = StyleSheet.create({
  container: {
      backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    // flexGrow: 1,
  },
  content: {
    backgroundColor: '#E8E1E1',
    paddingTop: 16,
    paddingHorizontal: 8,
    paddingBottom: BOTTOM_SPACING,
    minHeight: SCREEN_HEIGHT - 200,
  },
  stockCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    padding: 16,
  },
  buySellContainer: {
    position: "absolute",
    bottom: 80,
    width: "100%",
  }
});
