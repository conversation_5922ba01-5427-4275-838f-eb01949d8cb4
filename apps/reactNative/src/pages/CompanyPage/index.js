import React, { useState, useRef } from 'react';
import { View, ScrollView , SafeAreaView } from 'react-native';
import {
  useNavigation,
} from '@react-navigation/native';

import { styles } from './styles';
import { useCompanyDetails, useWatchList } from '@paytm-money/store';
import CompanyTechnicals from '../../components/organisms/CompanyTechnicals';
import Header from '../../components/atoms/Header/Header';
import ActionButtons from '../../components/atoms/ActionButtons/ActionButtons';
import Tabs from '../../components/atoms/Tabs/Tabs';
import CurrentDemand from '../../components/atoms/CurrentDemand/CurrentDemand';
import AnalystRating from '../../components/atoms/AnalystRating/AnalystRating';
import Breakout from '../../components/atoms/Breakout';
import PerformanceOveview from '../../components/molecules/PerformanceOverview';
import StockPerformance from '../../components/molecules/StockPerformance';
import PriceAlert from '../../components/molecules/PriceAlert';
import MostBought from '../../components/organisms/MostBought/MostBougth';

import ShareholdingPattern from '../../components/molecules/ShareholdingPattern/ShareholdingPattern';
import AboutCompany from '../../components/molecules/AboutCompany/AboutCompany';
import News from '../../components/molecules/News/News';
import Events from '../../components/molecules/Events/Events';
import BuySellButtons from '../../components/molecules/BuySellButtons';
import Drawer from '../../components/molecules/Drawer';
import MoreOptionsDrawer from '../../components/molecules/MoreOptionsDrawer';
import Holdings from '../../components/molecules/Holdings';
import WatchList from '../../components/molecules/WatchList';
import FundamentalDetails from '../../components/molecules/FundamentalDetails';
import TopOptions from '../../components/molecules/TopOptions';
import OptionChain from '../../components/molecules/OptionChain/OptionChain';

const dummyData = {
  depth: [
    {
      bidPrice: 2415.65,
      bidQuantity: 1250,
      askPrice: 2416.0,
      askQuantity: 850,
    },
    {
      bidPrice: 2415.6,
      bidQuantity: 850,
      askPrice: 2416.05,
      askQuantity: 1200,
    },
    {
      bidPrice: 2415.55,
      bidQuantity: 2100,
      askPrice: 2416.1,
      askQuantity: 950,
    },
    {
      bidPrice: 2415.5,
      bidQuantity: 1500,
      askPrice: 2416.15,
      askQuantity: 1600,
    },
    {
      bidPrice: 2415.45,
      bidQuantity: 750,
      askPrice: 2416.2,
      askQuantity: 780,
    },
  ],
};

const dummyTopAskBid = {
  totalBuyQty: 12500,
  totalSellQty: 8500,
  totalBidPercent: 73,
  totalAskPercent: 27,
};

const analystRatings = [
  { type: 'Buy', percentage: 60 },
  { type: 'Hold', percentage: 50 },
  { type: 'Sell', percentage: 43 },
];

const TABS = [
  'Overview',
  'F&O',
  'Technicals',
  'Fundamentals',
  'News',
  'Events',
];

const CompanyDetail = ({ route }) => {
  const { companyId } = route.params;
  const [activeTab, setActiveTab] = useState('Overview');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const scrollViewRef = useRef(null);
  const priceAlertRef = useRef(null);
  const watchListController = useWatchList(true);
  // const watchListData = watchListController?.watchListData;
  const navigation = useNavigation();

  const {
    data: { data: companyData } = {},
    isLoading: isCompanyDetailLoading,
  } = useCompanyDetails(parseInt(companyId), true);
  const companyDetail = {
    ...companyData?.results[0],
  };

  const handleMorePress = () => {
    setIsDrawerOpen(true);
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const scrollToPriceAlert = () => {
    if (activeTab !== 'Overview') {
      setActiveTab('Overview');
      setTimeout(() => {
        priceAlertRef.current?.measureLayout(
          scrollViewRef.current,
          (x, y) => {
            scrollViewRef.current?.scrollTo({ y, animated: true });
          },
          () => console.log('Failed to measure layout')
        );
      }, 100);
    } else {
      priceAlertRef.current?.measureLayout(
        scrollViewRef.current,
        (x, y) => {
          scrollViewRef.current?.scrollTo({ y, animated: true });
        },
        () => console.log('Failed to measure layout')
      );
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Overview':
        return (
          <>
            <Breakout pmlId={companyDetail?.id} />
            <CurrentDemand
              securityId={companyDetail.security_id}
              exchange={companyDetail.exchange}
              segment={companyDetail.segment}
              pmlId={companyDetail?.id}
              isCompanyDetailLoading={isCompanyDetailLoading}
              depthResponse={dummyData}
              topAskBidFeedResponse={dummyTopAskBid}
            />
            <Holdings />
            <PerformanceOveview
              exchange={companyDetail.exchange}
              securityId={companyDetail.security_id}
              segment={companyDetail.segment}
              expiryDate={companyDetail.expiry_display_date}
            />
            <AnalystRating
              isin={companyDetail?.isin}
              pmlId={companyDetail?.id}
            />
            <View ref={priceAlertRef} collapsable={false}>
              <PriceAlert pmlId={companyId} />
            </View>
            <StockPerformance companyDetail={companyDetail}/>
            <MostBought id={companyId}/>
          </>
        );
      case 'Technicals':
        return (
          <CompanyTechnicals
            pmlId={companyDetail?.id}
            companyName={companyDetail?.name}
          />
        );
      case 'Fundamentals':
        return (
          <>
            <FundamentalDetails pmlId={companyDetail?.id} />
            <ShareholdingPattern pmlId={companyDetail?.id} />
            <AboutCompany pmlId={companyDetail?.id} />
          </>
        );
      case 'News':
        return (
          <News isin={companyDetail?.isin} companyName={companyDetail?.name} />
        );
      case 'Events':
        return <Events />;
      case 'F&O':
        return (
          <>
          <TopOptions symbol={companyDetail?.symbol} />
          <OptionChain />
          <TopOptions symbol={companyDetail?.symbol} />
          </>
        );
      default:
        return null;
    }
  };

  const onWatchlistPress = () => {
    watchListController.addToWatchlist({
      id: companyDetail?.id,
      ...companyDetail,
    });
  };

  if (isCompanyDetailLoading) return null;

  return (
    <SafeAreaView style={styles.container}>
    <View>
      <Header
        onBackPress={handleBackPress}
        onOptionChainPress={() => console.log('option chain pressed')}
        onWatchlistPress={onWatchlistPress}
        onMorePress={handleMorePress}
      />
      <ScrollView
        ref={scrollViewRef}
        stickyHeaderIndices={[1]}
        contentContainerStyle={styles.scrollContent}
      >
        <ActionButtons onAlertPress={scrollToPriceAlert} />
        <Tabs tabs={TABS} activeTab={activeTab} onTabPress={setActiveTab} />
        <View style={styles.content}>{renderTabContent()}</View>
      </ScrollView>
      <View style={styles.buySellContainer}>
        <BuySellButtons />
      </View>
      <Drawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        showHandleBar
        showCross={false}
      >
        <MoreOptionsDrawer onClose={() => setIsDrawerOpen(false)} />
      </Drawer>
      <WatchList watchListController={watchListController} />
    </View>
    </SafeAreaView>
  );
};

export default CompanyDetail;
