import React from 'react';
import { View, StyleSheet ,Text} from 'react-native';
import SwipeableButton from '../../components/molecules/SwipeableButton';
import { useSnackbar } from '@paytm-money/store';
import { APPEARANCE_TYPES } from '../../enums/snackbarEnums';
import { useCombine } from '@paytm-money/store';

const OrderPad = () => {
  const { addSnackbar } = useSnackbar();

const {isLoading,data} = useCombine()
  const handleBuyOrder = async () => {
      
      addSnackbar({
        message: 'Order placed successfully',
        type: APPEARANCE_TYPES.SUCCESS,
      });

  };

  const handleSellOrder = () => {
    addSnackbar({
      message: 'Insufficient balance',
      type: APPEARANCE_TYPES.FAIL,

    });
  };

  const handleMTFInfo = () => {
    addSnackbar({
      message: 'MTF is not activated for your account',
      type: APPEARANCE_TYPES.INFO,

    });
  };

  return (
    <View style={styles.container}>
        <Text>
        {JSON.stringify(data || {}).substring(0,100)}
        </Text>
      <SwipeableButton
        text="Swipe to Buy using Pay Later"
        onComplete={handleBuyOrder}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
});

export default OrderPad; 