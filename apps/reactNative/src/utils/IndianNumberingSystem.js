import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { floorDecimalValue } from '../../utils/commonUtils';

const IndianNumberingSystem = ({
  number,
  withRupeeSymbol = true,
  style,
  ...props
}) => {
  const prefix = withRupeeSymbol ? '₹' : '';

  if (number == null) return <Text>0</Text>;

  let stringNumber = number.toString().split('.');
  const len = stringNumber[0].length;
  const lenDup = len >= 8 ? 8 : len;
  stringNumber = stringNumber.join('');

  if (Number.isNaN(number)) {
    return (
      <View>
        <Text style={styles.amount}>-</Text>
      </View>
    );
  }

  const renderContent = (value, suffix) => (
    <View style={[styles.container, style]} {...props}>
      <Text style={styles.prefix}>{prefix}</Text>
      <Text style={styles.amount}>{floorDecimalValue(parseFloat(value))}</Text>
      {suffix && <Text style={styles.suffix}>{suffix}</Text>}
    </View>
  );

  switch (lenDup) {
    case 8:
      return renderContent(
        `${stringNumber.substring(0, len - 7)}.${stringNumber.substring(len - 7)}`,
        'Cr'
      );
    case 7:
    case 6:
      return renderContent(
        `${stringNumber.substring(0, len - 5)}.${stringNumber.substring(len - 5)}`,
        'L'
      );
    case 5:
    case 4:
      return renderContent(
        `${stringNumber.substring(0, len - 3)}.${stringNumber.substring(len - 3)}`,
        'K'
      );
    default:
      return renderContent(number.toString());
  }
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prefix: {
    marginRight: 2,
  },
  amount: {
    fontFamily: 'Roboto-Regular',
  },
  suffix: {
    marginLeft: 2,
  }
});

export default IndianNumberingSystem;