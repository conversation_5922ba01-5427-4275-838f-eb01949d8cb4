import { Text } from 'react-native';
import React from 'react';
import { RUPEE_SYMBOL } from './constants.js';


export function roundValue(value, decimals = 2) {
  if (!(value)) {
    return '0.00';
  }
  const base = 10 ** decimals;
  return (Math.round(value * base) / base).toFixed(decimals);
}

export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  }).format(amount || 0);
}; 

export function isValidNumber(value) {
  if (!value) return false;
  return !(Number.isNaN(value) || value === Infinity);
}

export function getAbsoluteValue(value, decimals = 2, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return '0.00';
  }
  const roundedValue = roundValue(value, decimals);
  if (!getAbsolute) {
    return roundedValue;
  }
  return roundedValue >= 0
    ? roundedValue
    : roundValue(-1 * roundedValue, decimals);
}

export const roundToTwoDecimalPlaces = input => {
  if (Number.isInteger(input)) return input.toFixed(2);
  return parseInt(input, 10).toFixed(2);
};


export const FormatNumber = ({
  input = 0,
  isRoundingRequired = true,
  isDecimalLengthFixRequired = false,
  decimalLength = null,
  isDecimalRequired = true,
}) => {
  const fontWeightLighter = { fontWeight: '300' };

  // Existing number processing logic
  const num = isRoundingRequired
    ? roundToTwoDecimalPlaces(input)
    : input.toString();
  let n1 = num.split('.')[0].replace(/(\d)(?=(\d\d)+\d$)/g, '$1,');
  let n2 = num.split('.')[1] || null;

  // Decimal formatting logic remains same
  if (isDecimalLengthFixRequired && decimalLength && n2) {
    n2 = n2.padEnd(decimalLength, '0').slice(0, decimalLength);
  }

  if (n2) {
    return (
      <Text>
        <Text>{RUPEE_SYMBOL}{n1}</Text>
        {isDecimalRequired && <Text style={fontWeightLighter}>.{n2}</Text>}
      </Text>
    );
  }

  if (n1) {
    return (
      <Text>
        {RUPEE_SYMBOL}{n1}
        {isDecimalRequired && <Text style={fontWeightLighter}>.00</Text>}
      </Text>
    );
  }

  return <Text>--</Text>;
};

export function formatPrice(value, decimals = 2, format = true, getAbsolute = true) {
  if (!isValidNumber(value)) {
    return `0${decimals ? `.${'0'.repeat(decimals)}` : ''}`;
  }
  if (format) {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: decimals,
    }).format(getAbsoluteValue(value, decimals, getAbsolute));
  }
  return getAbsoluteValue(value, decimals, getAbsolute);
}

export const SEGMENT_TYPES = {
  CASH: 'E',
  DERIVATIVES: 'D',
};

export const REQUEST_TYPES = {
  STOCK: 'stock',
  DEPTH: 'depth',
  INDEX: 'index',
  ORDER: 'order',
  MARKET_STATUS: 'marketStatus',
};

export const RESPONSE_TYPES = {
  TRADE: 'TRADE',
  DEPTH: 'DEPTH',
  OHLC: 'OHLC',
  SPREAD: 'SPREAD',
  INDEX: 'INDEX',
  TOP_BID_ASK: 'TOP_BID_ASK',
  MARKET_STATUS: 'MARKET_STATUS',
  P_CLOSE: 'P_CLOSE',
  CIRCUIT_LIMIT: 'CIRCUIT_LIMIT',
  HIGH_LOW: 'HIGH_LOW',
  ORDER: 'ORDER',

  /** Only available in TCP socket mode */
  // PRICE_TICKER: 'PRICE_TICKER',
  // JOURNAL_MSG: 'JOURNAL_MSG',
};

export const LIMITS = {
  LOWER_CIRCUIT: 'Lower Circuit',
  UPPER_CIRCUIT: 'Upper Circuit',
  EXPIRY_DATE: 'Expiry Date:',
};