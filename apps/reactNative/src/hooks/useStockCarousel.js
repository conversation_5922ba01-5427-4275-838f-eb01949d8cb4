import { useState, useMemo } from 'react';
import { useQuery } from 'react-query';
import { useApiHeaders } from '@paytm-money/store';
import { makeApiGetCall } from '../utils/apiUtils';

export const useStockCarousel = (symbol) => {
  const [activeType, setActiveType] = useState('Call');
  const { headers } = useApiHeaders();

  const { data, isLoading, error } = useQuery(
    ['topOptions', symbol],
    async () => {
      const response = {
        "meta": {
          "code": "PM_FNO_200",
          "message": "Success",
          "displayMessage": "Success"
        },
        "data": {
          "results": {
            "CALL": [
              {
                "category": "AT_THE_MONEY",
                "displayName": "At the Money",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-CE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "CE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 CALL"
                  }
                ]
              },
              {
                "category": "HIGHEST_OI",
                "displayName": "Highest OI",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-CE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "CE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 CALL"
                  }
                ]
              },
              {
                "category": "HIGHEST_VOLUME",
                "displayName": "Highest Volume",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-CE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "CE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 CALL"
                  }
                ]
              }
            ],
            "PUT": [
              {
                "category": "AT_THE_MONEY",
                "displayName": "At the Money",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-PE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "PE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 PUT"
                  }
                ]
              },
              {
                "category": "HIGHEST_OI",
                "displayName": "Highest OI",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-PE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "PE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 PUT"
                  }
                ]
              },
              {
                "category": "HIGHEST_VOLUME",
                "displayName": "Highest Volume",
                "scripDetails": [
                  {
                    "pml_id": 5002091686,
                    "exchange": "NSE",
                    "underlaying_scrip_code": 26074,
                    "segment": "D",
                    "security_id": 42027,
                    "pml_symbol": "MIDCPNIFTY-Oct2024-13500-PE",
                    "symbol": "MIDCPNIFTY",
                    "expiry_date": "2024-10-28T00:00:00",
                    "option_type": "PE",
                    "instrument": "OPTIDX",
                    "name": "MIDCPNIFTY 28 OCT 13500 PUT"
                  }
                ]
              }
            ]
          }
        }
      }
      return response?.data?.results;
    },
    { enabled: true }
  );

  const formattedData = useMemo(() => {
    if (!data) return null;

    const types = {
      CALL: 'Call',
      PUT: 'Put'
    };

    return Object.entries(data).reduce((acc, [type, categories]) => {
      acc[types[type]] = categories.map(category => ({
        ...category.scripDetails[0],
        category: category.category,
        displayName: category.displayName
      }));
      return acc;
    }, {});
  }, [data]);

  return {
    data: formattedData?.[activeType] || [],
    activeType,
    setActiveType,
    isLoading,
    error
  };
}; 