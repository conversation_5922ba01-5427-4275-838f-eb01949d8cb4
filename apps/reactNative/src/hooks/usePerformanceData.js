import { useEffect, useMemo, useState } from 'react';

// import { useResultFromFeed } from '@utils/Equities/hooks';

import {
  roundValue,
  formatPrice,
  SEGMENT_TYPES,
  REQUEST_TYPES,
  RESPONSE_TYPES,
  LIMITS,
} from '../utils/commonUtils';

const requiredFeedResponse = [
  RESPONSE_TYPES.OHLC,
  RESPONSE_TYPES.HIGH_LOW,
  RESPONSE_TYPES.TRADE,
  RESPONSE_TYPES.P_CLOSE,
  RESPONSE_TYPES.CIRCUIT_LIMIT,
];

const dayHighLowData = {
  High: 713.7,
  Low: 589.65,
  Open: 679.0999755859375,
  exchangeId: 1,
  pClose: 620.6500244140625,
  securityId: 3456,
};

const yearHighLowData = {
  exchangeId: 1,
  securityId: 3456,
  yearHigh: 1179,
  yearLow: 589.6500244140625,
};

const tradeData = {
  averageTradePrice: 646.4400024,
  exchangeId: 1,
  lastTradePrice: 658.4000244140625,
  lastTradeQuantity: 1,
  lastTradeTime: 1425288589,
  lastUpdatedTime: 0,
  openInterest: 88540950,
  securityId: 3456,
  tradeVolume: 2492753,
};

const pCloseData = {
  exchangeId: 1,
  previousOpenInterest: 0,
  previousPrice: 648.5499877929688,
  securityId: 3456,
};

const limitData = {
  exchangeId: 1,
  lcl: 558.5999755859375,
  securityId: 3456,
  ucl: 682.72,
};

// TODO: useResultFromFeed from broadcast
const useResultFromFeed = () => [
  dayHighLowData,
  yearHighLowData,
  tradeData,
  pCloseData,
  limitData,
];
const emptyObj = {};

const usePerformanceData = ({
  exchange,
  securityId,
  segment = SEGMENT_TYPES.CASH,
}) => {
  const [dayHighLow, setDayHighLow] = useState(emptyObj);
  const [yearHighLow, setYearHighLow] = useState(emptyObj);

  const [
    dayHighLowData = emptyObj,
    yearHighLowData = emptyObj,
    tradeData = emptyObj,
    pCloseData = emptyObj,
    limitData,
  ] = useResultFromFeed(REQUEST_TYPES.STOCK, requiredFeedResponse, {
    exchange,
    securityId,
    segment,
  });

  useEffect(() => {
    setDayHighLow(dayHighLowData);
  }, [dayHighLowData]);
  useEffect(() => {
    const { lastTradePrice: ltp = 0 } = tradeData;
    setDayHighLow(({ High = 0, Low = 0 }) => ({
      High: ltp > High ? ltp : High,
      Low: ltp < Low ? ltp : Low,
    }));
  }, [tradeData]);

  useEffect(() => {
    setYearHighLow(yearHighLowData);
  }, [yearHighLowData]);
  useEffect(() => {
    const { lastTradePrice: ltp = 0 } = tradeData;
    setYearHighLow(({ yearHigh = 0, yearLow = 0 }) => ({
      yearHigh: ltp > yearHigh ? ltp : yearHigh,
      yearLow: ltp < yearLow ? ltp : yearLow,
    }));
  }, [tradeData]);

  const ohlcData = useMemo(
    () => ({
      ...dayHighLowData,
      ...dayHighLow,
      ...pCloseData,
    }),
    [dayHighLowData, dayHighLow, pCloseData]
  );

  const weekPerformanceData = useMemo(() => {
    const { lastTradePrice: ltp = 0 } = tradeData;
    return {
      ...yearHighLow,
      ...tradeData,
      ltp,
      maxLoss: roundValue(((yearHighLow.yearLow - ltp) / ltp) * 100),
      maxProfit: roundValue(((yearHighLow.yearHigh - ltp) / ltp) * 100),
    };
  }, [yearHighLow, tradeData]);

  return {
    ohlcData,
    limitData,
    weekPerformanceData,
  };
};

function getFormattedDataForCard(
  { ohlcData, limitData, weekPerformanceData, expiryDate },
  segment
) {
  if (segment === SEGMENT_TYPES.CASH || true) {
    return {
      Open: formatPrice(ohlcData?.Open),
      'Prev. Close': formatPrice(ohlcData?.previousPrice),
      "Today's Low": formatPrice(ohlcData?.Low),
      "Today's High": formatPrice(ohlcData?.High),
      '52-wk_Low': formatPrice(weekPerformanceData?.yearLow),
      '52-wk_High': formatPrice(weekPerformanceData?.yearHigh),
      [LIMITS.LOWER_CIRCUIT]: formatPrice(limitData?.lcl),
      [LIMITS.UPPER_CIRCUIT]: formatPrice(limitData?.ucl),
      Volume: formatPrice(weekPerformanceData?.tradeVolume),
      'Avg. Price': formatPrice(weekPerformanceData?.averageTradePrice),
    };
  }
  return {
    open: formatPrice(ohlcData?.Open),
    pclose: formatPrice(ohlcData?.previousPrice),
    high: formatPrice(ohlcData?.High),
    low: formatPrice(ohlcData?.Low),
    [LIMITS.LOWER_CIRCUIT]: formatPrice(limitData?.lcl),
    [LIMITS.UPPER_CIRCUIT]: formatPrice(limitData?.ucl),
    volume: formatPrice(weekPerformanceData?.tradeVolume, 0),
    'avg. price': formatPrice(weekPerformanceData?.averageTradePrice),
    [LIMITS.EXPIRY_DATE]: expiryDate,
    'open interest': formatPrice(weekPerformanceData?.openInterest, 0),
  };
}

export { usePerformanceData as default, getFormattedDataForCard };
