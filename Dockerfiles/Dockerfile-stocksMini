#FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:node-14.15.1v-ssh-jenkin-s3
#FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:node-14.21.3-alpine-ssh-new
FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:node-18.20.3-alpine
WORKDIR /root/.ssh

# Install openssh-client package to get ssh-keyscan
RUN apk update && apk add openssh-client

RUN > /root/.ssh/known_hosts
RUN echo "StrictHostKeyChecking no" > config

RUN ssh -T ************************ || true

# Set working directory to the monorepo root
WORKDIR /var/www/frontend-monorepo

# Copy the entire monorepo into the container
COPY . .
RUN ls -ltrh

# Install required system dependencies
RUN apk update
RUN apk add openjdk8-jre
RUN apk add git openssh

# Set up Java environment variables
ENV JAVA_HOME /usr/lib/jvm/java-1.8-openjdk
ENV PATH $PATH:/usr/lib/jvm/java-1.8-openjdk/jre/bin:/usr/lib/jvm/java-1.8-openjdk/bin

RUN echo $PATH

# Install npm dependencies
RUN npm install --force
RUN npm run stocksMini:install
RUN npm run shared:install

# If using a monorepo manager like Lerna, bootstrap dependencies
RUN npm run bootstrap || echo "No bootstrap needed"

# Change to the correct directory for the app
#WORKDIR /var/www/frontend-monorepo/apps/stocksMini

# Run the build process
#RUN npm run build
RUN npm run stocksMini:build

# AWS CLI setup for S3 upload
ARG bucket_name
#RUN apk add --no-cache python3 py3-pip && \
    #pip3 install --upgrade pip && \
    #pip3 install --no-cache-dir awscli && \
    #rm -rf /var/cache/apk/*

RUN apk add --no-cache python3 py3-pip && \
    python3 -m venv /venv && \
    . /venv/bin/activate && \
    pip install --upgrade pip && \
    pip install --no-cache-dir awscli && \
    rm -rf /var/cache/apk/*

# Upload build to the S3 bucket
#RUN aws s3 cp build s3://$bucket_name/ --recursive
RUN . /venv/bin/activate && aws s3 cp ./apps/stocksMini/build s3://$bucket_name/ --recursive